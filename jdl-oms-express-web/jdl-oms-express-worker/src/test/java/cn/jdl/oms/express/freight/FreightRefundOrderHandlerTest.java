package cn.jdl.oms.express.freight;

import cn.jdl.oms.express.worker.scheduler.pay.WaybillRefundApplyMessageHandler;
import com.jd.paq.core.Message;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.text.ParseException;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:./applicationContext.xml"})
@ActiveProfiles("test")
public class FreightRefundOrderHandlerTest {

    @Resource
    private WaybillRefundApplyMessageHandler waybillRefundApplyMessageHandler;

    @Test
    public void testRefundHandler() throws ParseException {
        Message message = new Message();
        message.setTopic("800022");
        message.setMessageBody(
                "{\"dtoClass\":\"cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.AbstractMessageDto\",\"dtoJson\":\"{\\\"businessIdentity\\\":{\\\"businessScene\\\":\\\"callback\\\",\\\"businessStrategy\\\":\\\"BFreight\\\",\\\"businessType\\\":\\\"reverse_transport\\\",\\\"businessUnit\\\":\\\"cn_jdl_freight-consumer\\\"},\\\"orderNo\\\":\\\"FO0010002077682\\\",\\\"requestProfile\\\":{\\\"ext\\\":{},\\\"locale\\\":\\\"zh_CN\\\",\\\"tenantId\\\":\\\"1000\\\",\\\"timeZone\\\":\\\"GMT+8\\\",\\\"traceId\\\":\\\"1612345234234560\\\"}}\"}");

        waybillRefundApplyMessageHandler.execute(message);
    }
}
