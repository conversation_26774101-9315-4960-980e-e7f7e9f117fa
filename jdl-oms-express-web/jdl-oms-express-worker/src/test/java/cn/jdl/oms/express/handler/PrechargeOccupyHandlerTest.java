package cn.jdl.oms.express.handler;


import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.CommonJmqMessageDto;
import cn.jdl.oms.express.worker.message.handler.transaction.PrechargeOccupyHandler;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.HashMap;
import java.util.Random;

/**
 * @version 1.0
 * @description
 * @copyright &copy;2022 JDL.CN All Right Reserved
 * @Creator: liujiangwai1
 * @Date: 2025/4/9
 * @since 1.8
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:./applicationContext.xml"})
@ActiveProfiles("test")
public class PrechargeOccupyHandlerTest {

    @Resource
    PrechargeOccupyHandler prechargeOccupyHandler;

    @Test
    public void test() throws ParseException {
        CommonJmqMessageDto dto = new CommonJmqMessageDto();
        dto.setOrderNo("ECO1000010018831486");
        dto.setRequestProfile(requestProfile("1000"));
        prechargeOccupyHandler.handle(dto);
    }

    public RequestProfile requestProfile(String tenantId) {
        RequestProfile requestProfile = new RequestProfile();
        requestProfile.setLocale("zh_CN");
        requestProfile.setTenantId(tenantId);
        requestProfile.setTimeZone("GMT+8");
        requestProfile.setTraceId("test" + new Random(System.currentTimeMillis()).nextLong());
        requestProfile.setExt(new HashMap<>());
        return requestProfile;
    }
}
