package cn.jdl.oms.express;

import cn.jdl.oms.express.domain.infrs.acl.facade.pay.RefundFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.pay.RefundTranslator;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.OutSideRefundMessageDto;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import cn.jdl.oms.express.worker.message.handler.RefundMessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * @Package: cn.jdl.oms.express
 * @ClassName: cn.jdl.oms.express.RefundMessageHandlerTest
 * @Description: 退款消费者单元测试用例
 * @Author: liufarui
 * @CreateDate: 2021/4/19
 * @Copyright: Copyright (c)2021 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version: V1.0
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:./applicationContext.xml"})
@ActiveProfiles("test")
public class RefundMessageHandlerTest {
    private static final Logger LOGGER = LoggerFactory.getLogger(RefundOrderHandlerTest.class);

    /**
     * 防腐层对象转换器
     */
    @Resource
    private RefundTranslator refundTranslator;
    /**
     * 防腐层服务
     */
    @Resource
    private RefundFacade refundFacade;

    @Resource
    private RefundMessageHandler refundMessageHandler;

    @Test
    public void test() {
        OutSideRefundMessageDto dto = new OutSideRefundMessageDto();
        dto.setFlag(true);
        dto.setOrderId("123456");

        refundMessageHandler.handle(dto);
    }

    @Test
    public void jg12123RefundSuccessTest() {
        OutSideRefundMessageDto dto = JSONUtils.jsonToBean("{\n" +
                "    \"id\": 16973647853,\n" +
                "    \"orderId\": \"lq12123test000000005\",\n" +
                "    \"flag\": true,\n" +
                "    \"failReason\": \"autoCheck\",\n" +
                "    \"refid\": 43728306129,\n" +
                "    \"refundAmount\": 0.11,\n" +
                "    \"modifyDate\": 1747190281958,\n" +
                "    \"payInfos\": [\n" +
                "        {\n" +
                "            \"id\": 17577048291,\n" +
                "            \"refundId\": 16973647853,\n" +
                "            \"refundSourceId\": 17577048291,\n" +
                "            \"payPhysicsId\": \"61583859333\",\n" +
                "            \"payId\": \"110181762505141017340121435310\",\n" +
                "            \"payType\": 6,\n" +
                "            \"payEnum\": 11018,\n" +
                "            \"refundableAmount\": 0.11,\n" +
                "            \"payTime\": 1747189060000,\n" +
                "            \"orderId\": \"3266409378518\",\n" +
                "            \"currency\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"outsideOrdersRefundBusinessType\": 1080\n" +
                "}", OutSideRefundMessageDto.class);

        refundMessageHandler.handle(dto);
    }
}
