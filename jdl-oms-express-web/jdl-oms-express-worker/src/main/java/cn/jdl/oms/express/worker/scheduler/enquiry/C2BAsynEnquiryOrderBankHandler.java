package cn.jdl.oms.express.worker.scheduler.enquiry;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.bo.CustomerConfig;
import cn.jdl.oms.express.domain.dto.DiscountInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceDetailInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceInfoDto;
import cn.jdl.oms.express.domain.dto.MoneyInfoDto;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.CustomerConfigFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.enquiry.C2BOrderBankOrgFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.enquiry.EnquiryFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.issue.ModifyIssueFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.GetOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.ModifyOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.orderbank.GetMerchantOrderBankFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.orderbank.OrderBankFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.customer.BasicTraderResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.C2BCreateEnquiryFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.ModifyIssueFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.ModifyIssueFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderModelCreatorTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.ModifyOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.ModifyOrderFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.C2BOrderBankFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeMiddleRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankRedisOp;
import cn.jdl.oms.express.domain.infrs.acl.pl.org.OrderbankOrgFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.org.OrderbankOrgFacadeRquest;
import cn.jdl.oms.express.domain.infrs.acl.pl.org.OrderbankOrgFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.ohs.locals.ducc.ExpressDUCConfigCenter;
import cn.jdl.oms.express.domain.infrs.ohs.locals.promise.MakingDispatcherHandler;
import cn.jdl.oms.express.domain.infrs.ohs.locals.redis.IRedisLock;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.ModifyRepositoryMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.OrderBankPdqMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.impl.SchedulerService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.message.SchedulerMessage;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.CurrencyCodeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.FlowConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.PDQTopicEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.mdc.MDCTraceConstants;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import cn.jdl.oms.express.worker.scheduler.AbstractSchedulerHandler;
import com.jd.paq.core.Message;
import com.jd.paq.plugin.IMessage;
import com.jd.paq.plugin.Result;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;

import static cn.jdl.oms.express.shared.common.constant.EnquiryConstants.CALC_PRICE_ITEM_LIST;

/**
 * @Description  C2B - 异步询价台账消息处理
 * @Copyright    &copy;2022 JDL.CN All Right Reserved
 * <AUTHOR> liu @ 2022/5/31 11:11 AM
 * @version      1.0
 * @since        1.8
 */
public class C2BAsynEnquiryOrderBankHandler extends AbstractSchedulerHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(C2BAsynEnquiryOrderBankHandler.class);

    /** 查询订单防腐层 */
    @Resource
    private GetOrderFacade getOrderFacade;

    /** 修改防腐层 */
    @Resource
    private ModifyOrderFacade modifyOrderFacade;

    /** 修改防腐层对象转换器 */
    @Resource
    private ModifyOrderFacadeTranslator modifyOrderFacadeTranslator;

    /** 订单详情model转换 */
    @Resource
    private GetOrderModelCreatorTranslator orderModelCreatorTranslator;

    /** 支付机构facade */
    @Resource
    private C2BOrderBankOrgFacade c2BOrderBankOrgFacade;

    /** 询价facade */
    @Resource
    private EnquiryFacade enquiryFacade;

    /** 机构信息获取防腐层转换器 */
    @Resource
    private OrderbankOrgFacadeTranslator orderbankOrgFacadeTranslator;

    @Resource
    private C2BCreateEnquiryFacadeTranslator c2BCreateEnquiryFacadeTranslator;

    /** 台账防腐层 */
    @Resource
    private OrderBankFacade orderBankFacade;

    @Resource
    private C2BOrderBankFacadeTranslator c2BOrderBankFacadeTranslator;

    /** 修改下发服务 */
    @Resource
    private ModifyIssueFacade modifyIssueFacade;

    @Resource
    private ModifyIssueFacadeTranslator modifyIssueFacadeTranslator;

    /** 下发履约执行层达标逻辑 */
    @Resource
    private MakingDispatcherHandler makingDispatcherHandler;

    /** 任务调度 */
    @Resource
    private SchedulerService schedulerService;

    /** 单据台账锁 */
    @Resource
    private OrderBankRedisOp orderBankRedisOp;

    /** 商家基础信息 */
    @Resource
    private CustomerConfigFacade customerConfigFacade;

    /** B商家台账查询 */
    @Resource
    private GetMerchantOrderBankFacade getMerchantOrderBankFacade;

    /** 开关 */
    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    /** DUCC */
    @Resource
    private ExpressDUCConfigCenter expressDUCConfigCenter;

    /** 重试最大次数 */
    private static final int MAX_RETRY_TIME = 10;

    /**
     * 异步询价台账消息处理处理
     *
     * @param  iMessage 消息
     * @return 业务结果
     * <AUTHOR> liu
     */
    @Override
    public Result execute(IMessage iMessage) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        //任务调度执行结果对象根据实际的执行结果定义返回具体的业务结果
        Result result = new Result(Result.SUCCESS);
        Lock lock = new Lock();
        MDC.put(MDCTraceConstants.TRACEID, String.valueOf(System.nanoTime()));
        try {
            if (iMessage instanceof Message) {
                int retryTime = ((Message) iMessage).getRedriveCount();
                if (retryTime >= MAX_RETRY_TIME) {
                    LOGGER.info("异步询价台账消息处理任务调度【{}】,重试次数超过{}次,暂停重试", iMessage.getTopic(), MAX_RETRY_TIME);
                    result.setCode(Result.INVALID_REQUEST);
                    return result;
                }
                String iMessageContent = iMessage.getMessageBody();
                PDQTopicEnum pdqTopicEnum = PDQTopicEnum.getByTopic(iMessage.getTopic());
                if (null == pdqTopicEnum) {
                    LOGGER.info("异步询价台账消息处理任务调度【{}】未匹配到任务队列,暂停重试", iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }
                // 重试消息体
                SchedulerMessage schedulerMessage = JSONUtils.jsonToBean(
                        iMessageContent, SchedulerMessage.class);
                if (null == schedulerMessage) {
                    LOGGER.info("异步询价台账消息处理任务调度【{}】,重试消息体不存在,暂停重试", iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }
                OrderBankPdqMessageDto messageDto = JSONUtils.jsonToBean(schedulerMessage.getDtoJson(),
                        OrderBankPdqMessageDto.class);
                if (null == messageDto) {
                    LOGGER.info("异步询价台账消息处理任务调度【{}】,场景业务数据对象不存在,暂停重试", iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }
                if (!this.asynEnquiryOrderbankHandle(messageDto, lock)) {
                    LOGGER.info("异步询价台账消息处理任务调度【{}】,场景业务数据对象不存在,暂停重试", iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }
            }
        } catch (Exception e) {
            result.setCode(Result.SYSTEMERROR);
            result.setReason(e.getMessage());
            LOGGER.info("异步询价台账消息处理任务调度【{}】执行异常", iMessage.getTopic(), e);
            Profiler.functionError(callerInfo);
        } finally {
            LOGGER.info("异步询价台账消息处理任务调度【{}】执行结束", iMessage.getTopic());
            Profiler.registerInfoEnd(callerInfo);
            if (lock.getRedisLock() != null) {
                lock.getRedisLock().unlock();
            }
            MDC.remove(MDCTraceConstants.TRACEID);
        }
        return result;
    }

    /**
     * 异步询价-台账-持久化
     *
     * @param messageDto 消息体
     * @return {@code true} 持久化成功
     */
    private boolean asynEnquiryOrderbankHandle(OrderBankPdqMessageDto messageDto, Lock lock) throws ParseException {
        if (messageDto == null || StringUtils.isBlank(messageDto.getOrderNo())) {
            return true;
        }

        LOGGER.info("异步询价开始：orderNo:{}",messageDto.getOrderNo());
        //获取询价台账锁，获取不到锁重试. 需要做对应的释放功能
        IRedisLock redisLock = orderBankRedisOp.getLock(messageDto.getRequestProfile(), messageDto.getBusinessIdentity(), messageDto.getOrderNo());
        if (!redisLock.tryLock()) {
            LOGGER.info("订单获取台账锁失败，需要重试");
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ASYN_ENQUIRY_ORDER_BANK_FAIL, "订单orderNo:" + messageDto.getOrderNo() + "获取台账锁失败，需要重试");
            return false;
        }
        lock.setRedisLock(redisLock);

        //获取订单详情
        GetOrderFacadeResponse orderFacadeResponse = toGetOrderFacadeResponse(messageDto.getRequestProfile(), messageDto.getOrderNo());
        if (orderFacadeResponse == null) {
            LOGGER.error("异步询价台账消息处理查询订单为空");
            /*未获取到订单详情信息需继续重试*/
            return false;
        }
        //将订单详情转换成model
        ExpressOrderContext orderContext = toExpressOrderContext(messageDto, orderFacadeResponse);
        //订单状态检查. 已取消的订单直接忽略
        ExpressOrderModel orderModel = orderContext.getOrderModel();
        if (OrderStatusEnum.CANCELED == orderModel.getOrderStatus().getOrderStatus()) {
            LOGGER.info("订单状态为已取消，忽略这个消息");
            return true;
        }
        //判断台账是否初始化
        boolean init = orderBankRedisOp.haveInit(messageDto.getRequestProfile(), messageDto.getBusinessIdentity(), messageDto.getOrderNo(), true);
        if (!init) {
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ASYN_ENQUIRY_ORDER_BANK_FAIL, "订单orderNo:" + orderModel.orderNo() + "尚未初始化台账，需要重试");
            LOGGER.info("订单尚未初始化台账，需要重试");
            return false;
        }

        //已支付的不操作台账
        if (PaymentStatusEnum.COMPLETE_PAYMENT == orderModel.getFinance().getPaymentStatus()) {
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ASYN_ENQUIRY_ORDER_BANK_FAIL, "已支付的不操作台账,orderNo:" + orderModel.orderNo());
            return true;
        }

        //结算方式是月结, 且无cod, 不写账
        if (SettlementTypeEnum.MONTHLY_PAYMENT == orderModel.getFinance().getSettlementType()
                && orderModel.getProductDelegate().getCodProducts().isEmpty()) {
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ASYN_ENQUIRY_ORDER_BANK_FAIL, "结算方式是月结, 且无cod的不操作台账,orderNo:" + orderModel.orderNo());
            return true;
        }

        //补全订单信息, 1.原单订单快照
        //原单信息
        GetOrderFacadeResponse refOrderFacadeResponse = toGetOrderFacadeResponse(messageDto.getRequestProfile(), messageDto.getRelateOrderNo());
        if (refOrderFacadeResponse == null) {
            LOGGER.error("异步询价台账消息处理查询原订单为空,原单号：{}", messageDto.getRelateOrderNo());
            return false;
        }
        ExpressOrderContext refOrderContext = toExpressOrderContext(messageDto, refOrderFacadeResponse);
        //将原单的快照信息存入当前单
        orderModel.assignSnapshot(refOrderContext.getOrderModel());

        //当前单询价
        BillingEnquiryFacadeResponse billingEnquiryFacadeResponse = toBillingEnquiryFacadeResponse(orderContext, null);
        // key:订单号  value:计费信息
        Map<String, BillingEnquiryFacadeResponse> billMap = new HashMap<>();
        billMap.put(orderModel.orderNo(), billingEnquiryFacadeResponse);
        // 原单询价时，无需询价的增值产品
        Set<String> originOrderEnquiryProductNoBlacklist = getOriginOrderEnquiryProductNoBlacklist();

        //逆向单询价(逆向次数原则上不受限，有大对象风险)
        if (OrderTypeEnum.RETURN_ORDER == orderModel.getOrderType()) {
            LOGGER.info("逆向单补全计费明细开始");
            // 多次逆向的场景，每一次逆向，当最新的逆向单是到付现结时，最新逆向单在接单后调计费接口询价，同时获取所有原单信息（取最原始的正向单及最新一次逆向之前的所有逆向单，
            // 且只取结算方式为到付现结的单子）并依次调询价接口重新询价，新单和所有原单均询价成功后，需要加和所有原单运费（所有原单折后金额）并写在最新单运费（最新单折后金额）上落库，
            // 且所有原单的费用明细均需落在新单的费用明细上（相同费用编码的费用需加和）。
            // 所有单据询价完成后，在最新单的订单财务表的“备注”中落所有单（含所有原单及最新逆向单）的每一单的折后金额（形式为“订单号1：折后金额1；订单号2：折后金额2”；…），
            // 在最新单的费用明细表的“备注”中落每一项费用下所有单（含所有原单及最新逆向单）的每一单的该费用的折后金额（形式为“订单号1：折后金额1；订单号2：折后金额2”；…
            GetOrderFacadeResponse subOrderFacadeResponse = refOrderFacadeResponse;
            ExpressOrderContext subOrderContext = null;
            BillingEnquiryFacadeResponse subBillingEnquiryFacadeResponse = null;
            do {
                // 当前单(相对), 补全订单信息
                subOrderContext = toExpressOrderContext(messageDto, subOrderFacadeResponse);
                /*// 1.商家基础资料
                subOrderContext.setCustomerConfig(customerConfig);
                // 2.支付机构id和名称
                orderContext.getOrderModel().complement().complementFinanceCollectionOrg(this, parseCollectionOrgModelCreator(
                        orderbankOrg.getOrgId(), orderbankOrg.getOrgName()));*/
                // 当前单关联单(相对)
                GetOrderFacadeResponse subRefOrderFacadeResponse = null;
                // C2B 正向都为取件单 503
                if (OrderTypeEnum.PICKUP != subOrderContext.getOrderModel().getOrderType()) {
                    subRefOrderFacadeResponse = toGetOrderFacadeResponse(messageDto.getRequestProfile(), subOrderContext.getOrderModel().getRefOrderInfoDelegate().getOriginalOrderNo());
                }
                LOGGER.info("逆向单-中间过程询价, subOrderContext:{}", JSONUtils.beanToJSONDefault(subOrderContext));
                // 询价 只有到付现结询价
                if (SettlementTypeEnum.CASH_ON_DELIVERY.equals(subOrderContext.getOrderModel().getFinance().getSettlementType())) {
                    if (OrderTypeEnum.PICKUP == subOrderContext.getOrderModel().getOrderType()) {
                        // 回到第一单
                        subBillingEnquiryFacadeResponse = toBillingEnquiryFacadeResponse(subOrderContext, originOrderEnquiryProductNoBlacklist);
                    } else {
                        // 3.关联单快照存入当前单(相对)
                        ExpressOrderContext subRefOrderContext = toExpressOrderContext(messageDto, subRefOrderFacadeResponse);
                        subOrderContext.getOrderModel().assignSnapshot(subRefOrderContext.getOrderModel());
                        subBillingEnquiryFacadeResponse = toBillingEnquiryFacadeResponse(subOrderContext, originOrderEnquiryProductNoBlacklist);
                    }
                    billMap.put(subOrderContext.getOrderModel().orderNo(), subBillingEnquiryFacadeResponse);
                }
                // 更新当前单(相对)subOrderFacadeResponse
                subOrderFacadeResponse = subRefOrderFacadeResponse;
            } while (OrderTypeEnum.RETURN_ORDER == subOrderContext.getOrderModel().getOrderType());
            LOGGER.info("逆向单完成全量计费获取. {}", JSONUtils.mapToJson(billMap));
        }
        // 更新补全财务信息
        complementBillingResult(orderContext, billMap, billingEnquiryFacadeResponse);
        LOGGER.info("补全计费明细信息结束.billing: {}", JSONUtils.beanToJSONDefault(orderModel.getFinance()));
        //查询商家基础资料
        BasicTraderResponse basicTraderResponse = customerConfigFacade.getCustomerConfig(orderModel.getCustomer().getAccountNo());
        CustomerConfig customerConfig = new CustomerConfig();
        customerConfig.setCustomerId(basicTraderResponse.getCustomerId());
        customerConfig.setCustomerName(basicTraderResponse.getCustomerName());
        orderContext.setCustomerConfig(customerConfig);

        //查询支付机构id和名称
        OrderbankOrgFacadeRquest orderbankOrgFacadeRquest = orderbankOrgFacadeTranslator.toOrderbankOrgFacadeRquest(orderContext);
        OrderbankOrgFacadeResponse orderbankOrg = c2BOrderBankOrgFacade.getOrderBankOrg(orderbankOrgFacadeRquest);
        if (orderbankOrg == null) {
            LOGGER.error("获取机构信息失败");
            return false;
        }
        orderModel.complement().complementFinanceCollectionOrg(this, parseCollectionOrgModelCreator(
                orderbankOrg.getOrgId(), orderbankOrg.getOrgName()));
        //台账入参转换
        OrderBankFacadeMiddleRequest middleRequest = c2BOrderBankFacadeTranslator.toReverseReAddressOrderBankFacadeRequest(orderContext);
        modifyOrderBank(messageDto, orderContext, middleRequest);
        //下发ofc  仅有财务的
        issueOrder(orderContext);
        //持久化, 以及异常重试
        //仅有财务的
        try {
            ModifyOrderFacadeRequest facadeRequest = modifyOrderFacadeTranslator.toReverseOrChangeAddressOrderFacadeRequest(orderContext);
            modifyOrderFacade.modifyOrder(orderModel.requestProfile(), facadeRequest);
        } catch (Exception e) {
            LOGGER.error("单持久化防腐层异常", e);
            //触发重试
            produceRetryMq(orderModel.requestProfile(), orderContext);
        }
        return true;
    }

    /**
     * 调用台账接口修改台账
     *
     * @param messageDto
     * @param orderContext
     * @param middleRequest
     */
    private void modifyOrderBank(OrderBankPdqMessageDto messageDto, ExpressOrderContext orderContext, OrderBankFacadeMiddleRequest middleRequest) {
        if (middleRequest.getBMerchantDfModify() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = c2BOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setBMerchantModify(middleRequest.getBMerchantDfModify());
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, C2BAsynEnquiryOrderBankHandler.class);
            if (orderBankFacadeResponse != null) {
                c2BOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }
        if (middleRequest.getBMerchantCodModify() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = c2BOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setBMerchantModify(middleRequest.getBMerchantCodModify());
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, C2BAsynEnquiryOrderBankHandler.class);
            if (orderBankFacadeResponse != null) {
                c2BOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }
        if (middleRequest.getBMerchantJfModify() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = c2BOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setBMerchantModify(middleRequest.getBMerchantJfModify());
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, C2BAsynEnquiryOrderBankHandler.class);
            if (orderBankFacadeResponse != null) {
                c2BOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }
        if (middleRequest.getPosJfYun() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = c2BOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setPosJfYun(middleRequest.getPosJfYun());
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, C2BAsynEnquiryOrderBankHandler.class);
            if (orderBankFacadeResponse != null) {
                c2BOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }
        if (middleRequest.getPosYun() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = c2BOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setPosYun(middleRequest.getPosYun());
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, C2BAsynEnquiryOrderBankHandler.class);
            if (orderBankFacadeResponse != null) {
                c2BOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }
    }

    /**
     * 持久化异常处理
     *
     * @param orderContext
     */
    private void produceRetryMq(RequestProfile requestProfile, ExpressOrderContext orderContext) throws ParseException {
        SchedulerMessage schedulerMessage = new SchedulerMessage();
        //持久化消息
        ModifyRepositoryMessageDto modifyRepositoryMessageDto = this.toMessageDto(requestProfile, orderContext);
        schedulerMessage.setDtoJson(JSONUtils.beanToJSONDefault(modifyRepositoryMessageDto));
        schedulerMessage.setDtoClass(ModifyRepositoryMessageDto.class);
        schedulerService.addSchedulerTask(PDQTopicEnum.REVERSE_REPOSITORY_RETRY, schedulerMessage,
                FlowConstants.EXPRESS_ORDER_REVERSE_REPOSITORY_FLOW_CODE);
    }

    /**
     * 下发ofc
     *
     * @param orderContext
     * @throws ParseException
     */
    private void issueOrder(ExpressOrderContext orderContext) throws ParseException {
        Set<String> promiseUnits = makingDispatcherHandler.execute(orderContext);
        Optional.ofNullable(promiseUnits).orElseThrow(() ->
                new BusinessDomainException(UnifiedErrorSpec.BasisOrder.INTERNAL_ERROR).withCustom("下发履约执行层打标无法识别")
        );
        //下发履约层复制给业务身份对象信息
        promiseUnits.forEach(promiseUnit -> orderContext.getOrderModel().getBusinessIdentity().setFulfillmentUnit(promiseUnit));
        //订单领域模型
        promiseUnits.forEach(promiseUnit -> orderContext.getOrderModel().getOrderBusinessIdentity().setFulfillmentUnit(promiseUnit));
        //下发
        ModifyIssueFacadeRequest modifyIssueFacadeRequest = modifyIssueFacadeTranslator.toReverseOrChangeAddressIssueFacadeRequest(
                orderContext);
        modifyIssueFacade.modifyIssue(modifyIssueFacadeRequest, orderContext.getOrderModel().getOrderBusinessIdentity());
    }

    /**
     * 询价，逆向单原单询价时，需要传入逆向单新单支付信息
     *
     * @param orderContext                         原单
     * @param originOrderEnquiryProductNoBlacklist 原单询价增值产品黑名单（无需询价）
     * @return
     */
    private BillingEnquiryFacadeResponse toBillingEnquiryFacadeResponse(ExpressOrderContext orderContext, Set<String> originOrderEnquiryProductNoBlacklist) {
        BillingEnquiryFacadeRequest billingEnquiryFacadeRequest = c2BCreateEnquiryFacadeTranslator.toBillingEnquiryFacadeRequest(orderContext);

        // 处理原单询价时，不需要计费的产品
        if (CollectionUtils.isNotEmpty(originOrderEnquiryProductNoBlacklist)
                && CollectionUtils.isNotEmpty(billingEnquiryFacadeRequest.getProductFacadeDtoList())) {
            List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtoList = billingEnquiryFacadeRequest.getProductFacadeDtoList();
            Iterator<BillingEnquiryFacadeRequest.ProductFacadeDto> iterator = productFacadeDtoList.iterator();
            while (iterator.hasNext()) {
                BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto = iterator.next();
                if (originOrderEnquiryProductNoBlacklist.contains(productFacadeDto.getProductNo())) {
                    LOGGER.info("原单不询价的产品：" + productFacadeDto.getProductNo());
                    iterator.remove();
                }
            }
        }

        return enquiryFacade.billingEnquiry(billingEnquiryFacadeRequest);
    }

    /**
     * 组装context
     *
     * @param messageDto
     * @param orderFacadeResponse
     * @return
     */
    private ExpressOrderContext toExpressOrderContext(OrderBankPdqMessageDto messageDto, GetOrderFacadeResponse orderFacadeResponse) {
        ExpressOrderModelCreator orderModelCreator = orderModelCreatorTranslator.toExpressOrderModelCreator(orderFacadeResponse);
        ExpressOrderModel orderModel = new ExpressOrderModel(orderModelCreator);
        orderModel.withRequestProfile(messageDto.getRequestProfile());
        ExpressOrderContext orderContext = new ExpressOrderContext(messageDto.getBusinessIdentity(), messageDto.getRequestProfile(), messageDto.getBusinessIdentity().getBusinessScene());
        orderContext.setOrderModel(orderModel);
        return orderContext;
    }

    /**
     * 查询订单详情
     *
     * @param profile
     * @param orderNo
     * @return
     */
    private GetOrderFacadeResponse toGetOrderFacadeResponse(RequestProfile profile, String orderNo) {
        GetOrderFacadeRequest facadeRequest = new GetOrderFacadeRequest();
        facadeRequest.setOrderNo(orderNo);
        return getOrderFacade.getOrder(profile, facadeRequest);
    }

    private ExpressOrderModelCreator parseCollectionOrgModelCreator(String orgId, String orgName) {
        ExpressOrderModelCreator creater = new ExpressOrderModelCreator();
        FinanceInfoDto financeInfoDto = new FinanceInfoDto();
        financeInfoDto.setCollectionOrgNo(orgId);
        financeInfoDto.setCollectionOrgName(orgName);
        creater.setFinanceInfo(financeInfoDto);
        return creater;
    }

    /**
     * 原单财务数据根据计费结果重算
     *
     * @param orderContext
     * @param billMap      所有计费
     * @param currentOrder 当前单计费
     */
    public void complementBillingResult(ExpressOrderContext orderContext, Map<String, BillingEnquiryFacadeResponse> billMap, BillingEnquiryFacadeResponse currentOrder) {
        ExpressOrderModelCreator modelCreator = new ExpressOrderModelCreator();
        FinanceInfoDto financeInfoDto = new FinanceInfoDto();

        BigDecimal preMoney = new BigDecimal(0);
        BigDecimal disMoney = new BigDecimal(0);
        CurrencyCodeEnum preCodeEnum = null;
        CurrencyCodeEnum disCodeEnum = null;
        //费用明细 key：CostNo
        Map<String,FinanceDetailInfoDto> detailInfoDtoMap =  new HashMap<>();
        StringBuilder financeRemark = new StringBuilder();
        for (Map.Entry<String, BillingEnquiryFacadeResponse> entry : billMap.entrySet()) {
            // 费用总额
            preMoney = preMoney.add(entry.getValue().getFinanceFacadeDto().getPreAmount().getAmount());
            preCodeEnum = entry.getValue().getFinanceFacadeDto().getPreAmount().getCurrencyCode();

            disMoney = disMoney.add(entry.getValue().getFinanceFacadeDto().getDiscountAmount().getAmount());
            disCodeEnum = entry.getValue().getFinanceFacadeDto().getDiscountAmount().getCurrencyCode();
            financeRemark.append(entry.getKey()).append(":").append(entry.getValue().getFinanceFacadeDto().getDiscountAmount().getAmount()).append(",");
            // 费用明细
            for (BillingEnquiryFacadeResponse.FinanceDetailFacadeDto detailFacadeDto : entry.getValue().getFinanceFacadeDto().getFinanceDetailFacadeDtoList()) {
                //费用明细按CostNo分组
                FinanceDetailInfoDto detailInfoDto = detailInfoDtoMap.get(detailFacadeDto.getCostNo());
                if (detailInfoDto == null) {
                    detailInfoDto = new FinanceDetailInfoDto();
                    detailInfoDto.setCostNo(detailFacadeDto.getCostNo());
                    detailInfoDto.setCostName(detailFacadeDto.getCostName());
                    detailInfoDto.setProductNo(detailFacadeDto.getProductNo());

                    detailInfoDto.setExtendProps(new HashMap<>());
                    // 价格项明细
                    if (detailFacadeDto.getExtendProps() != null && detailFacadeDto.getExtendProps().containsKey(CALC_PRICE_ITEM_LIST)) {
                        detailInfoDto.getExtendProps().put(CALC_PRICE_ITEM_LIST, JSONUtils.beanToJSONDefault(detailFacadeDto.getExtendProps().get(CALC_PRICE_ITEM_LIST)));
                    }

                    detailInfoDtoMap.put(detailInfoDto.getCostNo(),detailInfoDto);
                }
                //费用明细整合
                toFinanceDetailInfoDto(detailInfoDto,detailFacadeDto);
                LOGGER.info("费用明细整合出参detailInfoDto：{}",JSONUtils.beanToJSONDefault(detailInfoDto));

                if (CollectionUtils.isNotEmpty(detailFacadeDto.getDiscountInfoFacadeDtos())) {
                    // 费用折扣
                    Map<String,DiscountInfoDto> discountInfoDtoMap = new HashMap<>();
                    for (BillingEnquiryFacadeResponse.DiscountInfoFacadeDto discountInfoFacadeDto : detailFacadeDto.getDiscountInfoFacadeDtos()) {
                        //费用折扣按DiscountNo分组
                        DiscountInfoDto discountInfoDto = discountInfoDtoMap.get(discountInfoFacadeDto.getDiscountNo());
                        if (discountInfoDto == null) {
                            discountInfoDto = new DiscountInfoDto();
                            discountInfoDtoMap.put(discountInfoDto.getDiscountNo(), discountInfoDto);
                        }
                        //折扣整合
                        toDiscountInfoFacadeDto(discountInfoDto, discountInfoFacadeDto);
                    }
                    List<DiscountInfoDto> discountInfoDtoList = new ArrayList<>(discountInfoDtoMap.values());
                    detailInfoDto.setDiscountInfoDtos(discountInfoDtoList);
                }

                //明细remark
                StringBuilder detailRemark;
                if (StringUtils.isNotBlank(detailInfoDto.getRemark())) {
                    detailRemark = new StringBuilder(detailInfoDto.getRemark());
                } else {
                    detailRemark = new StringBuilder();
                }
                detailRemark.append(entry.getKey()).append(":").append(detailInfoDto.getDiscountAmount().getAmount()).append(",");
                detailInfoDto.setRemark(detailRemark.toString());
            }
        }
        LOGGER.info("费用明细整合出参detailInfoDtoMap：{}",JSONUtils.mapToJson(detailInfoDtoMap));
        List<FinanceDetailInfoDto> financeDetailInfoDtoList = new ArrayList<>(detailInfoDtoMap.values());
        //折前金额
        MoneyInfoDto preAmount = new MoneyInfoDto();
        preAmount.setAmount(preMoney);
        preAmount.setCurrencyCode(preCodeEnum);
        financeInfoDto.setPreAmount(preAmount);
        //折后金额
        MoneyInfoDto discountAmount = new MoneyInfoDto();
        discountAmount.setAmount(disMoney);
        discountAmount.setCurrencyCode(disCodeEnum);
        financeInfoDto.setDiscountAmount(discountAmount);
        //计费重量
        financeInfoDto.setBillingWeight(currentOrder.getFinanceFacadeDto().getBillingWeight());
        //计费体积
        financeInfoDto.setBillingVolume(currentOrder.getFinanceFacadeDto().getBillingVolume());
        financeInfoDto.setBillingMode(currentOrder.getFinanceFacadeDto().getBillingMode());
        //费用明细
        financeInfoDto.setFinanceDetailInfos(financeDetailInfoDtoList);
//        toFinanceDetailInfoDto(currentOrder.getFinanceFacadeDto().getFinanceDetailFacadeDtoList(),detailRemark.toString())
        //总优惠金额
        MoneyInfoDto totalDiscountAmount = new MoneyInfoDto();
        totalDiscountAmount.setAmount(preMoney.subtract(disMoney));
        totalDiscountAmount.setCurrencyCode(preCodeEnum);
        financeInfoDto.setTotalDiscountAmount(totalDiscountAmount);
        financeInfoDto.setRemark(financeRemark.toString());
        modelCreator.setFinanceInfo(financeInfoDto);
        orderContext.getOrderModel().complement().complementFinanceInfo(this, modelCreator);
    }

    private List<FinanceDetailInfoDto> toFinanceDetailInfoDto(List<BillingEnquiryFacadeResponse.FinanceDetailFacadeDto> detailFacadeDtos, String remark) {
        List<FinanceDetailInfoDto> financeDetailInfoDtos = new ArrayList<>();
        for (BillingEnquiryFacadeResponse.FinanceDetailFacadeDto detailFacadeDto : detailFacadeDtos) {
            FinanceDetailInfoDto financeDetailInfoDto = new FinanceDetailInfoDto();
            financeDetailInfoDto.setPointsInfoDto(detailFacadeDto.getPointsInfoDto());
            financeDetailInfoDto.setCostName(detailFacadeDto.getCostName());
            financeDetailInfoDto.setCostNo(detailFacadeDto.getCostNo());
            //折后金额
            MoneyInfoDto detailDiscountAmount = new MoneyInfoDto();
            detailDiscountAmount.setAmount(detailFacadeDto.getDiscountAmount() != null ? detailFacadeDto.getDiscountAmount().getAmount() : null);
            detailDiscountAmount.setCurrencyCode(detailFacadeDto.getDiscountAmount() != null ? detailFacadeDto.getDiscountAmount().getCurrencyCode() : null);
            financeDetailInfoDto.setDiscountAmount(detailDiscountAmount);
            //折前金额
            MoneyInfoDto detailPreAmount = new MoneyInfoDto();
            detailPreAmount.setAmount(detailFacadeDto.getPreAmount() != null ? detailFacadeDto.getPreAmount().getAmount() : null);
            detailPreAmount.setCurrencyCode(detailFacadeDto.getPreAmount() != null ? detailFacadeDto.getPreAmount().getCurrencyCode() : null);
            financeDetailInfoDto.setPreAmount(detailPreAmount);
            financeDetailInfoDto.setDiscountInfoDtos(toDiscountInfoFacadeDto(detailFacadeDto.getDiscountInfoFacadeDtos()));
            financeDetailInfoDto.setProductNo(detailFacadeDto.getProductNo());
            financeDetailInfoDto.setProductName(detailFacadeDto.getProductName());
            financeDetailInfoDto.setRemark(remark);
            financeDetailInfoDtos.add(financeDetailInfoDto);
        }
        return financeDetailInfoDtos;
    }

    private List<DiscountInfoDto> toDiscountInfoFacadeDto(List<BillingEnquiryFacadeResponse.DiscountInfoFacadeDto> discountInfoFacadeDto) {
        List<DiscountInfoDto> discountInfoDtos = new ArrayList<>();
        for (BillingEnquiryFacadeResponse.DiscountInfoFacadeDto facadeDto : discountInfoFacadeDto) {
            DiscountInfoDto discountInfoDto = new DiscountInfoDto();
            discountInfoDto.setDiscountNo(facadeDto.getDiscountNo());
            discountInfoDto.setDiscountType(facadeDto.getDiscountType());
            Money disAmount = new Money();
            disAmount.setAmount(facadeDto.getDiscountedAmount() != null ? facadeDto.getDiscountedAmount().getAmount() : null);
            disAmount.setCurrency(facadeDto.getDiscountedAmount() != null ? facadeDto.getDiscountedAmount().getCurrencyCode() : null);
            discountInfoDto.setDiscountedAmount(disAmount);
            discountInfoDtos.add(discountInfoDto);
        }
        return discountInfoDtos;
    }

    private void toFinanceDetailInfoDto(FinanceDetailInfoDto detailInfoDto,BillingEnquiryFacadeResponse.FinanceDetailFacadeDto detailFacadeDto) {
//        detailInfoDto.setCostNo(detailFacadeDto.getCostNo()); // TODO 判断一下是不是不需要赋值
//        detailInfoDto.setCostName(detailFacadeDto.getCostName());
//        detailInfoDto.setProductNo(detailFacadeDto.getProductNo());
        //折前金额
        MoneyInfoDto detailPreAmount = new MoneyInfoDto();
        BigDecimal preNewMoney = detailFacadeDto.getPreAmount() != null ? detailFacadeDto.getPreAmount().getAmount() : BigDecimal.ZERO;
        BigDecimal preMoneySum = detailInfoDto.getPreAmount() != null ? detailInfoDto.getPreAmount().getAmount().add(preNewMoney) : preNewMoney;
        detailPreAmount.setAmount(preMoneySum);
        detailPreAmount.setCurrencyCode(detailFacadeDto.getPreAmount() != null ? detailFacadeDto.getPreAmount().getCurrencyCode() : null);
        detailInfoDto.setPreAmount(detailPreAmount);
        //折后金额
        MoneyInfoDto detailDiscountAmount = new MoneyInfoDto();
        BigDecimal disNewMoney = detailFacadeDto.getDiscountAmount() != null ? detailFacadeDto.getDiscountAmount().getAmount() : BigDecimal.ZERO;
        BigDecimal disMoneySum = detailInfoDto.getDiscountAmount() != null ? detailInfoDto.getDiscountAmount().getAmount().add(disNewMoney) : disNewMoney;
        detailDiscountAmount.setAmount(disMoneySum);
        detailDiscountAmount.setCurrencyCode(detailFacadeDto.getDiscountAmount() != null ? detailFacadeDto.getDiscountAmount().getCurrencyCode() : null);
        detailInfoDto.setDiscountAmount(detailDiscountAmount);
    }

    private void toDiscountInfoFacadeDto(DiscountInfoDto discountInfoDto, BillingEnquiryFacadeResponse.DiscountInfoFacadeDto discountInfoFacadeDto) {
        discountInfoDto.setDiscountNo(discountInfoFacadeDto.getDiscountNo()); // detail是不是一个逻辑
        discountInfoDto.setDiscountType(discountInfoFacadeDto.getDiscountType());

        Money disAmount = new Money();
        BigDecimal discountNewMoney = discountInfoFacadeDto.getDiscountedAmount() != null ? discountInfoFacadeDto.getDiscountedAmount().getAmount() : null;
        BigDecimal discountSumMoney = discountInfoDto.getDiscountedAmount() != null ? discountInfoDto.getDiscountedAmount().getAmount().add(discountNewMoney != null ? discountNewMoney : BigDecimal.ZERO) : discountNewMoney;
        disAmount.setAmount(discountSumMoney);
        disAmount.setCurrency(discountInfoFacadeDto.getDiscountedAmount().getCurrencyCode());
        discountInfoDto.setDiscountedAmount(disAmount);
    }

    /**
     * 功能: 转换消息, 大报文
     *
     * @param:
     * @return:
     * @throw:
     * @description: todo, 使用jss存储大报文
     * @author: liufarui
     * @date: 2021/6/22 14:36
     */
    private ModifyRepositoryMessageDto toMessageDto(RequestProfile requestProfile, ExpressOrderContext context) throws ParseException {
        ModifyRepositoryMessageDto messageDto = new ModifyRepositoryMessageDto();
        messageDto.setRequestProfile(requestProfile);
        messageDto.setModifyOrderFacadeRequest(modifyOrderFacadeTranslator.toModifyOrderFacadeRequest(context));
        return messageDto;
    }

    /**
     * 用于传输lock，避免在上下文中增加lock信息
     */
    private static class Lock {
        private IRedisLock redisLock;

        IRedisLock getRedisLock() {
            return redisLock;
        }

        void setRedisLock(IRedisLock redisLock) {
            this.redisLock = redisLock;
        }
    }

    /**
     * 获取异步询价，原单不参与询价的增值产品
     */
    private Set<String> getOriginOrderEnquiryProductNoBlacklist() {
        Set<String> set = new HashSet<>();
        // 改址单、逆向单接单成功删除原单的增值产品名单：此配置主要是避免异步删除延迟、异步删除失败，导致原单多计费：https://joyspace.jd.com/pages/CAAeERwjW4rXKCafN5Ae
        set.addAll(BatrixSwitch.obtainListByUccKey(BatrixSwitchKey.REVERSE_OR_READDRESS_DELETE_ORIGINAL_PRODUCT_NO_LIST,","));
        return set;
    }
}

