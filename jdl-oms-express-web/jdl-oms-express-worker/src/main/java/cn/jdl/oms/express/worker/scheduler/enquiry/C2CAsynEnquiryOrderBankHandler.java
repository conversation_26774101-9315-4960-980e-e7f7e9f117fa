package cn.jdl.oms.express.worker.scheduler.enquiry;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.core.model.ReaddressRecordDetailInfo;
import cn.jdl.oms.express.c2c.extension.orderbank.IntlC2COrderbankOrgExtension;
import cn.jdl.oms.express.c2c.infrs.acl.pl.orderbank.C2COrderBankFacadeTranslator;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.bo.CustomerConfig;
import cn.jdl.oms.express.domain.dto.FinanceDetailInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceInfoDto;
import cn.jdl.oms.express.domain.dto.MoneyInfoDto;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.CustomerConfigFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.enquiry.EnquiryFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.exchangecurrency.ExchangeCurrencyFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.issue.ModifyIssueFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.GetOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.ModifyOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.orderbank.GetMerchantOrderBankFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.orderbank.OrderBankFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.org.OrderbankOrgFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.EnquiryFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.exchangecurrency.ExchangeCurrencyRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.ModifyIssueFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.ModifyIssueFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderModelCreatorTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.ModifyOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.ModifyOrderFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.BMerchantReceiveTypeEnum;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.GetMerchantOrderBankFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeMiddleRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankRedisOp;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.ReceivableDetailFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.bdue.PayModeEnum;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.FLowDirectionUtils;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.OTSLedgerUtil;
import cn.jdl.oms.express.domain.infrs.acl.pl.org.OrderbankOrgFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.org.OrderbankOrgFacadeRquest;
import cn.jdl.oms.express.domain.infrs.acl.pl.org.OrderbankOrgFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.util.ChannelUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedBusinessIdentityUtil;
import cn.jdl.oms.express.domain.infrs.ohs.locals.promise.MakingDispatcherHandler;
import cn.jdl.oms.express.domain.infrs.ohs.locals.redis.IRedisLock;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.IntegralReleaseMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.ModifyRepositoryMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.OrderBankPdqMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.impl.SchedulerService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.message.SchedulerMessage;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductAttrEnum;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductEnum;
import cn.jdl.oms.express.domain.spec.dict.CurrencyCodeEnum;
import cn.jdl.oms.express.domain.spec.dict.ModifyRecordTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.ServiceProductTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.model.IProduct;
import cn.jdl.oms.express.domain.spec.util.ReceiptCurrencyUtil;
import cn.jdl.oms.express.domain.vo.Address;
import cn.jdl.oms.express.domain.vo.Discount;
import cn.jdl.oms.express.domain.vo.Finance;
import cn.jdl.oms.express.domain.vo.FinanceDetail;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.domain.vo.Points;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.domain.vo.record.ModifyRecord;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.BusinessConstants;
import cn.jdl.oms.express.shared.common.constant.EnquiryConstants;
import cn.jdl.oms.express.shared.common.constant.FinanceConstants;
import cn.jdl.oms.express.shared.common.constant.FlowConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.IntegralOperaterEnum;
import cn.jdl.oms.express.shared.common.dict.PDQTopicEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import cn.jdl.oms.express.shared.common.utils.TypeConversion;
import cn.jdl.oms.express.worker.scheduler.AbstractSchedulerHandler;
import com.jd.paq.core.Message;
import com.jd.paq.plugin.IMessage;
import com.jd.paq.plugin.PDQClientException;
import com.jd.paq.plugin.Result;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.jdl.oms.express.shared.common.constant.BusinessConstants.DEFAULT_AMOUNT_DECIMAL_SCALE;
import static cn.jdl.oms.express.shared.common.constant.EnquiryConstants.CALC_PRICE_ITEM_LIST;

/**
 * @ProjectName：jdl-oms-express-c2c-infrastructure
 * @Package： cn.jdl.oms.express.worker.scheduler.issue
 * @ClassName: ReverseBillHandler
 * @Description: C2C异步询价台账消息处理 -- 涉及 后款改址到付 & 逆向到付
 * @Author： wanghuanhuan632
 * @CreateDate 2021/3/30 20:48
 * @Copyright: Copyright (c)2020 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version：V1.0
 */
public class C2CAsynEnquiryOrderBankHandler extends AbstractSchedulerHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(C2CAsynEnquiryOrderBankHandler.class);

    private static final String POINTS_DISCOUNT = "pointsDiscount";

    //查询订单防腐层
    @Resource
    private GetOrderFacade getOrderFacade;

    //修改防腐层
    @Resource
    private ModifyOrderFacade modifyOrderFacade;
    //修改防腐层对象转换器
    @Resource
    private ModifyOrderFacadeTranslator modifyOrderFacadeTranslator;

    /**
     * 订单详情model转换
     */
    @Resource
    private GetOrderModelCreatorTranslator orderModelCreatorTranslator;

    /**
     * 支付机构facade
     */
    @Resource
    private OrderbankOrgFacade orderbankOrgFacade;

    /**
     * 询价facade
     */
    @Resource
    private EnquiryFacade enquiryFacade;

    /**
     * 机构信息获取防腐层转换器
     */
    @Resource
    OrderbankOrgFacadeTranslator orderbankOrgFacadeTranslator;

    @Resource
    private EnquiryFacadeTranslator enquiryFacadeTranslator;

    /**
     * 台账防腐层
     */
    @Resource
    private OrderBankFacade orderBankFacade;

    @Resource
    private C2COrderBankFacadeTranslator c2cOrderBankFacadeTranslator;

    /**
     * 修改下发服务
     */
    @Resource
    private ModifyIssueFacade modifyIssueFacade;

    @Resource
    private ModifyIssueFacadeTranslator modifyIssueFacadeTranslator;

    /**
     * 下发履约执行层达标逻辑
     */
    @Resource
    private MakingDispatcherHandler makingDispatcherHandler;

    /**
     * 任务调度
     */
    @Resource
    private SchedulerService schedulerService;

    /**
     * 单据台账锁
     */
    @Resource
    private OrderBankRedisOp orderBankRedisOp;

    /**
     * 商家基础信息
     */
    @Resource
    private CustomerConfigFacade customerConfigFacade;

    /**
     * B商家台账查询
     */
    @Resource
    private GetMerchantOrderBankFacade getMerchantOrderBankFacade;

    /**
     * 开关
     */
    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    @Resource
    private ExchangeCurrencyFacade exchangeCurrencyFacade;

    /**
     * 重试最大次数
     */
    private static final int MAX_RETRY_TIME = 10;

    /**
     * 异步询价台账消息处理处理
     *
     * @param iMessage
     * @return
     * @throws PDQClientException
     * <AUTHOR>
    @Override
    public Result execute(IMessage iMessage) throws PDQClientException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_WORKER_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        //任务调度执行结果对象根据实际的执行结果定义返回具体的业务结果
        Result result = new Result(Result.SUCCESS);

        IRedisLock[] redisLock = {null};
        try {
            if (iMessage instanceof Message) {
                int retryTime = ((Message) iMessage).getRedriveCount();
                if (retryTime >= MAX_RETRY_TIME) {
                    LOGGER.info("异步询价台账消息处理任务调度【{}】,重试次数超过{}次,暂停重试", iMessage.getTopic(), MAX_RETRY_TIME);
                    result.setCode(Result.INVALID_REQUEST);
                    return result;
                }
                String iMessageContent = iMessage.getMessageBody();
                PDQTopicEnum pdqTopicEnum = PDQTopicEnum.getByTopic(iMessage.getTopic());
                if (null == pdqTopicEnum) {
                    LOGGER.info("异步询价台账消息处理任务调度【{}】未匹配到任务队列,暂停重试", iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }
                // 重试消息体
                SchedulerMessage schedulerMessage = JSONUtils.jsonToBean(
                        iMessageContent, SchedulerMessage.class);
                if (null == schedulerMessage) {
                    LOGGER.info("异步询价台账消息处理任务调度【{}】,重试消息体不存在,暂停重试", iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }
                OrderBankPdqMessageDto messageDto = JSONUtils.jsonToBean(schedulerMessage.getDtoJson(), OrderBankPdqMessageDto.class);
                if (null == messageDto) {
                    LOGGER.info("异步询价台账消息处理任务调度【{}】,场景业务数据对象不存在,暂停重试", iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }

                // 异步询价业务处理
                if (!this.asynEnquiryOrderbankHandle(messageDto, redisLock)) {
                    LOGGER.info("异步询价台账消息处理任务调度【{}】,场景业务数据对象不存在,暂停重试", iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }
            }
        } catch (Exception e) {
            result.setCode(Result.SYSTEMERROR);
            result.setReason(e.getMessage());
            LOGGER.info("异步询价台账消息处理任务调度【{}】执行异常", iMessage.getTopic(), e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
            if (redisLock[0] != null) {
                redisLock[0].unlock();
            }
        }
        return result;
    }

    /**
     * 异步询价-台账-持久化
     *
     * @param messageDto
     * @return
     */
    public boolean asynEnquiryOrderbankHandle(OrderBankPdqMessageDto messageDto, IRedisLock[] redisLock) throws ParseException {
        if (messageDto == null || StringUtils.isBlank(messageDto.getOrderNo())) {
            return true;
        }
        LOGGER.info("异步询价开始,orderNo:{}",messageDto.getOrderNo());

        //获取订单详情
        GetOrderFacadeResponse orderFacadeResponse = toGetOrderFacadeResponse(messageDto, messageDto.getOrderNo());
        if (orderFacadeResponse == null) {
            LOGGER.error("异步询价台账消息处理查询订单为空");
            /*未获取到订单详情信息需继续重试*/
            return false;
        }
        //将订单详情转换成model
        ExpressOrderContext orderContext = toExpressOrderContext(messageDto, orderFacadeResponse);
        ExpressOrderModel orderModel = orderContext.getOrderModel();

        // 订单状态检查 已取消和已支付不操作
        if (orderStatusCheck(orderContext)) {
            return true;
        }

        //原单信息
        GetOrderFacadeResponse refOrderFacadeResponse = toGetOrderFacadeResponse(messageDto, messageDto.getRelateOrderNo());
        if (refOrderFacadeResponse == null) {
            LOGGER.error("异步询价台账消息处理查询原订单为空,原单号：{}", messageDto.getRelateOrderNo());
            return false;
        }
        //当前单的原单信息
        ExpressOrderModelCreator orderModelCreator = orderModelCreatorTranslator.toExpressOrderModelCreator(refOrderFacadeResponse);
        ExpressOrderModel refOrderModel = new ExpressOrderModel(orderModelCreator);
        refOrderModel.withRequestProfile(messageDto.getRequestProfile());
        //将原单的快照信息存入当前单
        orderModel.assignSnapshot(refOrderModel);
        LOGGER.info("逆向单到付原单refordermodel：{}", JSONUtils.beanToJSONDefault(refOrderModel));
        LOGGER.info("逆向单到付新单ordermodel：{}", JSONUtils.beanToJSONDefault(orderModel));

        if (UnitedBusinessIdentityUtil.isUnitedIdentity(orderContext)) {
            // 更新为真实身份继续执行流程
            UnitedBusinessIdentityUtil.convertToReal(orderContext, true);
        }

        // 当前单不需要询价
        if(!needCurrentEnquiry(orderModel)){
            LOGGER.info("当前单不需要询价,orderNo:{}",orderModel.orderNo());
            return true;
        }

        //查询商家基础资料 并 补齐到上下文
        CustomerConfig customerConfig = customerConfigFacade.getCustomerConfig(orderContext);

        //查询支付机构id和名称
        OrderbankOrgFacadeResponse orderbankOrg = null;
        // 港澳 流向港澳到内地 结算方式到付现结
        if (orderModel.isHKMO() && orderModel.isCashOnDelivery()) {
            orderbankOrg = orderbankOrgFacade.getHMCashOnDeliveryOrgByAddress(orderContext);
        } else if (orderModel.isIntl()) {
            // fixme 国际C2C出口使用固定值，后续有进口业务再结合正逆向场景进行调整
            orderbankOrg = new OrderbankOrgFacadeResponse();
            orderbankOrg.setOrgId(IntlC2COrderbankOrgExtension.ORG_ID);
            orderbankOrg.setOrgName(IntlC2COrderbankOrgExtension.ORG_NAME);
        } else {
            OrderbankOrgFacadeRquest orderbankOrgFacadeRquest = orderbankOrgFacadeTranslator.toOrderbankOrgFacadeRquest(orderContext);
            // 先通过寄件人地址获取机构信息
            // 如果未获取到，通过揽收站点获取机构信息
            // 如果未获取到，通过商家id获取机构信息
            orderbankOrg = orderbankOrgFacade.getOrderbankOrg(orderbankOrgFacadeRquest);
        }

        if(null == orderbankOrg){
            LOGGER.error("获取机构信息失败,orderModel:{}",JSONUtils.beanToJSONDefault(orderModel));
            return false;
        }
        // 补齐支付机构信息
        orderModel.complement().complementFinanceCollectionOrg(this, parseCollectionOrgModelCreator(orderbankOrg.getOrgId(), orderbankOrg.getOrgName()));

        //判断台账是否初始化
        boolean init = orderBankRedisOp.haveInit(messageDto.getRequestProfile(), messageDto.getBusinessIdentity(), messageDto.getOrderNo(), true);
        if (!init) {
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ASYN_ENQUIRY_ORDER_BANK_FAIL, "订单orderNo:" + orderModel.orderNo() + "尚未初始化台账，需要重试");
            LOGGER.info("订单尚未初始化台账，需要重试，orderNo:{}", orderModel.orderNo());
            return false;
        }

        //获取询价台账锁，获取不到锁重试. 需要做对应的释放功能
        redisLock[0] = orderBankRedisOp.getLock(messageDto.getRequestProfile(), messageDto.getBusinessIdentity(), messageDto.getOrderNo());
        if (!redisLock[0].tryLock()) {
            LOGGER.info("订单获取台账锁失败，需要重试");
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ASYN_ENQUIRY_ORDER_BANK_FAIL, "订单orderNo:" + orderModel.orderNo() + "获取台账锁失败，需要重试");
            return false;
        }

        //当前单询价
        BillingEnquiryFacadeResponse billingEnquiryFacadeResponse = this.billingEnquiry(orderContext, null);

        /*
         * 淘天逆向建新单时（渠道.特殊来源=淘天），新单结算方式=到付现结、支付环节=后款。
         * 如果是淘天，新单的询价折后金额置 0。
         * - 淘天原单到付现结，将原单费用信息加到逆向新单写B商家和POS台账。后面原单询价逻辑已支持。
         * - 淘天原单其他场景（原单寄付现结、月结），逆向新单B商家和POS台账 折后金额写0
         */
        if (ChannelUtil.isTaoTianSpecial(orderModel.getOrderSnapshot())) {
            // 折后金额
            BigDecimal discountAmount = billingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount().getAmount();
            if (discountAmount.compareTo(BigDecimal.ZERO) > 0) {
                LOGGER.info("淘天逆向建新单时，新单的询价 折后金额置0。discountAmount: {} -> 0。", discountAmount);
                billingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount().setAmount(BigDecimal.ZERO);
            }
        }

        //逆向单询价
        if (OrderTypeEnum.RETURN_ORDER == orderModel.getOrderType()) {
            if (reverseOrderEnquireAndComplement(messageDto, orderContext, customerConfig, orderbankOrg, refOrderFacadeResponse, billingEnquiryFacadeResponse)) {
                return false;
            }
        } else if (OrderTypeEnum.READDRESS == orderModel.getOrderType()) {
            //改址单只询新单
            LOGGER.info("改址单补全计费明细信息");
            enquiryFacadeTranslator.complementBillingResult(orderContext, billingEnquiryFacadeResponse);
        }

        //台账入参转换
        OrderBankFacadeMiddleRequest middleRequest = c2cOrderBankFacadeTranslator.toReverseReAddressOrderBankFacadeRequest(orderContext);
        if (middleRequest.getBMerchantDfModify() == null && middleRequest.getBMerchantJfModify() == null
                && middleRequest.getBMerchantCodModify() == null && middleRequest.getPosJfYun() == null && middleRequest.getPosYun() == null) {
            LOGGER.info("调用台账写接口，入参全部为空，不需要写台账");
        } else {
            modifyOrderBank(messageDto, orderContext, middleRequest);
        }

        if (UnitedBusinessIdentityUtil.isUnitedIdentity(orderContext)) {
            // 更新为真实身份继续执行流程
            UnitedBusinessIdentityUtil.convertToOriginal(orderContext);
        }

        //下发ofc  仅有财务的
        issueOrder(orderContext);

        //持久化, 以及异常重试 仅有财务的
        ModifyOrderFacadeRequest modifyOrderFacadeRequest = null;
        try {
            modifyOrderFacadeRequest = modifyOrderFacadeTranslator.toReverseOrChangeAddressOrderFacadeRequest(orderContext);
            modifyOrderFacade.modifyOrder(orderModel.requestProfile(), modifyOrderFacadeRequest);
        } catch (Exception e) {
            LOGGER.error("单持久化防腐层异常", e);
            //触发重试
            produceRetryMq(orderModel.requestProfile(), modifyOrderFacadeRequest);
        }

        //释放积分:询价后，实际使用积分可能会小于预占积分，需要异步释放
        releasePoints(orderContext);

        // 原单为到付，改址单是后款到付 的 改址单 写完账 需要释放原单台账
        // UPDATE：订单不需要再合并原单费用，所以不需要清原单信息
        return true;
    }

    private boolean reverseOrderEnquireAndComplement(OrderBankPdqMessageDto messageDto, ExpressOrderContext orderContext, CustomerConfig customerConfig, OrderbankOrgFacadeResponse orderbankOrg, GetOrderFacadeResponse refOrderFacadeResponse, BillingEnquiryFacadeResponse billingEnquiryFacadeResponse) {
        ExpressOrderContext refOrderContext;
        LOGGER.info("逆向单补全计费明细开始,当前单:{},原单:{}",orderContext.getOrderModel().orderNo(),orderContext.getOrderModel().getOrderSnapshot().orderNo());
        //将原单详情转换成model
        refOrderContext = toExpressOrderContext(messageDto, refOrderFacadeResponse);
        refOrderContext.setCustomerConfig(customerConfig);
        refOrderContext.getOrderModel().complement().complementFinanceCollectionOrg(this, parseCollectionOrgModelCreator(
                orderbankOrg.getOrgId(), orderbankOrg.getOrgName()));

        // 港澳国际询原单场景处理
        if(refOrderContext.getOrderModel().isHKMO() || refOrderContext.getOrderModel().isIntlC2C()){
            if (refOrderContext.getOrderModel().isHKMO()
                    && CollectionUtils.isNotEmpty(refOrderContext.getOrderModel().getModifyRecordDelegate().getEnabledThroughOrderModifyRecords())) {
                // 从改址记录获取原单应收总费用，并补全到逆向单费用上
                getOriginOrderTotalAmountByModifyRecords(refOrderContext, orderContext, billingEnquiryFacadeResponse);
                return false;
            } else {
                crossBorderReverseOriEnquiry(refOrderContext, orderContext, billingEnquiryFacadeResponse);
                return false;
            }
        }
        //询价
        if (isNeedSumOriginalOrder(refOrderContext.getOrderModel())) {
            //到付现结，走询价
//                临时方案：新单在接单后调计费接口询价，同时查询原单实际总应收（查询订单信息的部分费用明细并做加和：运费（QIPSF/QLDZQD/SXZSYF-C2C/ SXHDYF/ SXSDYF）+包装费（QLHCF）+京尊达服务费（QLJZD）+保价费（QLBJ/C2CBJF/SXZSBJ-C2C/SXHDBJ/SXSDBJ）+高峰附加费（KDGFQFJF/ SXTHGFQFJF/ SXTKGFQFJF）+优惠总额（折前金额-折后金额）（正数代表调减的金额，负数代表调增的金额））
//                a)	若原单实际总应收不为空，则将原单实际总应收加和新单询价的折后金额后落在新单的折后金额上
//                b)	若原单实际总应收为空，则原单调台账查询接口（com.jd.supplierrecon.rpc.server.QueryPaymentInfo#searchReceivable）。若台账查询不为空，则取台账查询出的总应收作为原单实际总应收，并加和新单询价的折后金额后落在新单的折后金额上，同时发送报警邮件（找杨建辉对接邮件发送）；若台账查询也为空，则提示“询价失败”

            if (expressUccConfigCenter.isOrderBankMergeSwitch()) {
                LOGGER.info("逆向单到付现结，台账合并开关打开，补全计费明细信息");

                // 处理原单存在一单到底场景
                if (CollectionUtils.isNotEmpty(refOrderContext.getOrderModel().getModifyRecordDelegate().getEnabledThroughOrderModifyRecords())) {
                    //原单存在改址记录，则需根据改址记录和复重复量方询价记录，查询原单到付未支付的台账信息，金额合并继承到新单
                    List<ModifyRecord> modifyRecords = refOrderContext.getOrderModel().getModifyRecordDelegate().getEnabledModifyRecords();
                    BigDecimal refOrderBankReturn = BigDecimal.ZERO;
                    for (ModifyRecord modifyRecord : modifyRecords) {
                        BigDecimal dfAmount = BigDecimal.ZERO;
                        if (ModifyRecordTypeEnum.READDRESS.getCode().equals(modifyRecord.getModifyRecordType())// 改址记录
                                || ModifyRecordTypeEnum.INTERCEPT.getCode().equals(modifyRecord.getModifyRecordType())) {// 拦截记录
                            // 不处理拒收改址记录 ModifyRecordTypeEnum.REJECT ，原因：拒收一单到底走不到逆向单接单异步询价
                            //改址记录
                            ReaddressRecordDetailInfo readdressDetail = (ReaddressRecordDetailInfo) modifyRecord.getModifyRecordDetail();
                            if (SettlementTypeEnum.CASH_ON_DELIVERY.getCode().equals(readdressDetail.getFinance().getSettlementType())
                                    && !PaymentStatusEnum.COMPLETE_PAYMENT.getPaymentStatus().equals(readdressDetail.getFinance().getPaymentStatus())
                                    && (null != readdressDetail.getFinance().getPendingMoney() && BigDecimal.ZERO.compareTo(readdressDetail.getFinance().getPendingMoney().getAmount()) < 0)) {
                                //到付&&未支付&&待支付金额大于0的改址记录的台账金额，需继承到逆向新单上
                                dfAmount = getBMerchantDfAmount(modifyRecord.getModifyRecordNo());
                                LOGGER.info("到付&&未支付&&待支付金额大于0的改址记录号:{},dfAmount{}",modifyRecord.getModifyRecordNo(),dfAmount);
                            } else {
                                continue;
                            }
                        } else if (ModifyRecordTypeEnum.RECHECK.getCode().equals(modifyRecord.getModifyRecordType())) {
                            //复重复量方询价记录记录
                            dfAmount = getBMerchantDfAmount(modifyRecord.getModifyRecordNo());
                            LOGGER.info("复重复量方询价记录号:{},dfAmount{}",modifyRecord.getModifyRecordNo(),dfAmount);
                        }
                        refOrderBankReturn = refOrderBankReturn.add(dfAmount);
                    }
                    this.complementReverseBillingResult(orderContext, billingEnquiryFacadeResponse, refOrderBankReturn, false);
                } else {
                    if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.C2C_REVERSE_ORIGIN_ORDER_ENQUIRY_SWITCH)) {
                        LOGGER.info("逆向单询价，原单费用计算命中新规则：使用原单折后金额");
                        //原单、新单费用合并至新单
                        //1:使用订单折后金额
                        //2:若订单折后金额为空，则用B商家台账兜底，若B商家台账到付运费不为空或者0则报警
                        ExpressOrderModel originOrder = refOrderContext.getOrderModel();
                        BigDecimal refOrderMoney = null;
                        if (originOrder.getFinance() != null && originOrder.getFinance().getDiscountAmount() != null) {
                            refOrderMoney = originOrder.getFinance().getDiscountAmount().getAmount();
                        }
                        if (refOrderMoney == null || refOrderMoney.compareTo(BigDecimal.ZERO) == 0) {

                            ReceivableDetailFacade bMerchantDetail = getMerchantOrderBankFacade.getBMerchantDetail(originOrder.getCustomOrderNo(), BMerchantReceiveTypeEnum.DF);
                            if (bMerchantDetail != null) {
                                refOrderMoney = bMerchantDetail.getReceivableAmount();
                                if (bMerchantDetail.getReceivableAmount().compareTo(BigDecimal.ZERO) > 0) {
                                    //自定义告警
                                    Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_REVERSE_ORIGIN_ORDER_MONEY_NULL_ALARM, "原单orderNo=" + originOrder.orderNo() + ",逆向单询价原单折后金额为空或0，台账金额不为0");
                                }
                            }
                        }
                        if (refOrderMoney == null) {
                            refOrderMoney = BigDecimal.ZERO;
                            //自定义告警
                            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_REVERSE_ORIGIN_ORDER_BANK_MONEY_NULL_ALARM, "原单orderNo=" + originOrder.orderNo() + ",逆向单询价原单折后金额和台账金额都为空");
                        }
                        this.complementReverseBillingResult(orderContext, billingEnquiryFacadeResponse, refOrderMoney, false);

                    } else {
                        LOGGER.info("逆向单询价，原单费用计算命中老规则：使用原单费用明细和折扣按配置进行计算");
                        //原单调台账查询接口
                        GetMerchantOrderBankFacadeResponse getMerchantOrderBankFacadeResponse = getMerchantOrderBankFacade
                                .searchReceivable(refOrderContext.getOrderModel().getRefOrderInfoDelegate().getWaybillNo());
                        //原单cod
                        BigDecimal refCod = BigDecimal.ZERO;
                        //台账接口返回总应收去除cod后的费用
                        BigDecimal refOrderBankReturn = BigDecimal.ZERO;
                        Product product = refOrderContext.getOrderModel().getProductDelegate().ofProductNo(AddOnProductEnum.JDL_COD_TOC.getCode());
                        if (product != null) {
                            refCod = TypeConversion.stringToBigDecimal(product.getProductAttrs().get(AddOnProductAttrEnum.COD.getCode()), DEFAULT_AMOUNT_DECIMAL_SCALE, null);
                        }
                        if (getMerchantOrderBankFacadeResponse != null && getMerchantOrderBankFacadeResponse.getReceivableAmount() != null) {
                            refOrderBankReturn = getMerchantOrderBankFacadeResponse.getReceivableAmount();
                            if (refCod != null && refCod.compareTo(BigDecimal.ZERO) > 0) {
                                refOrderBankReturn = getMerchantOrderBankFacadeResponse.getReceivableAmount().subtract(refCod);
                            }
                        }

                        List<FinanceDetail> financeDetails = refOrderContext.getOrderModel().getFinance().getFinanceDetails();
                        BigDecimal refOrderReceive = BigDecimal.ZERO;
                        if (!CollectionUtils.isEmpty(financeDetails)) {
                            for (FinanceDetail financeDetail : financeDetails) {
                                if (expressUccConfigCenter.isReverseOriginOrderFinance(financeDetail.getCostNo())) {
                                    if (financeDetail.getPreAmount() != null && financeDetail.getPreAmount().getAmount() != null) {
                                        refOrderReceive = refOrderReceive.add(financeDetail.getPreAmount().getAmount()).setScale(BusinessConstants.DEFAULT_AMOUNT_DECIMAL_SCALE, RoundingMode.HALF_UP);
                                        LOGGER.info("逆向单到付现结增加金额：{}, 加后金额:{}", financeDetail.getPreAmount().getAmount(), refOrderReceive);
                                    }

                                    //积分金额属于优惠，但是在落在了明细上，需要扣减
                                    //增加DUCC开关配置，若折扣里配置了积分，则进行积分扣减,需要提前配置UCC开关里含有积分。
                                    if (expressUccConfigCenter.isReverseOriginOrderDiscount(POINTS_DISCOUNT)) {
                                        if (financeDetail.getPoints() != null && financeDetail.getPoints().getRedeemPointsAmount() != null
                                                && financeDetail.getPoints().getRedeemPointsAmount().getAmount() != null) {
                                            refOrderReceive = refOrderReceive.subtract(financeDetail.getPoints().getRedeemPointsAmount().getAmount()).setScale(BusinessConstants.DEFAULT_AMOUNT_DECIMAL_SCALE, RoundingMode.HALF_UP);
                                            LOGGER.info("逆向单到付现结增加金额：{}, 加后金额:{}", financeDetail.getPoints().getRedeemPointsAmount().getAmount(), refOrderReceive);
                                        }
                                    }

                                    //总优惠金额,财务明细中的折扣明细
                                    if (!CollectionUtils.isEmpty(financeDetail.getDiscounts())) {
                                        for (Discount discount : financeDetail.getDiscounts()) {
                                            if (expressUccConfigCenter.isReverseOriginOrderDiscount(discount.getDiscountNo())) {
                                                if (discount.getDiscountedAmount() != null && discount.getDiscountedAmount().getAmount() != null) {
                                                    refOrderReceive = refOrderReceive.add(discount.getDiscountedAmount().getAmount()).setScale(BusinessConstants.DEFAULT_AMOUNT_DECIMAL_SCALE, RoundingMode.HALF_UP);
                                                    LOGGER.info("逆向单到付现结增加金额：{}, 加后金额:{}", discount.getDiscountedAmount().getAmount(), refOrderReceive);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            LOGGER.info("逆向单到付现结，财务明细非空，计算原单的总应收:{}", refOrderReceive);
                            //若原单实际总应收不为空，则将原单实际总应收加和新单询价的折后金额后落在新单的折后金额上
                            this.complementReverseBillingResult(orderContext, billingEnquiryFacadeResponse, refOrderReceive, false);
                        } else {
                            LOGGER.info("逆向单到付现结，财务明细空, 需要加和台账接口返回的数据");
                            //判断原单台账是否为空, 不为空加和落库
                            if (getMerchantOrderBankFacadeResponse != null && getMerchantOrderBankFacadeResponse.getReceivableAmount() != null) {
                                LOGGER.info("逆向单到付现结，财务明细空, 原单调用台账接口查询数据非空，接口返回值:{} ,代收货款:{}", getMerchantOrderBankFacadeResponse.getReceivableAmount(), refCod);
                                this.complementReverseBillingResult(orderContext, billingEnquiryFacadeResponse, refOrderBankReturn, false);
                            } else {
                                LOGGER.info("逆向单到付现结，财务明细空, 台账接口返回的数据也是空，询价失败");
                                //为空报警，发邮件, 这种是业务有错误，需要终止业务
                                Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ASYN_ENQUIRY_ORDER_BANK_FAIL, "逆向单异步询价失败,orderNo:" + orderContext.getOrderModel().orderNo());
                                return true;
                            }
                        }

                        //	不影响询价结果，但需要发报警的场景（本期可先做异常报警，后期发报警邮件需要找杨建辉对接）：
                        //i.	原单费用明细和原单台账应收金额其中一项查询为空
                        //ii.	原单费用明细及原单台账应收金额查询均不为空，但原单实际总应收和原单台账应收金额不一致
                        if (CollectionUtils.isEmpty(financeDetails)
                                || getMerchantOrderBankFacadeResponse == null || getMerchantOrderBankFacadeResponse.getReceivableAmount() == null) {
                            //为空报警，发邮件
                            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ASYN_ENQUIRY_ORDER_BANK_FAIL, "逆向单异步询价,原单费用明细和原单台账应收金额其中一项查询为空,orderNo:" + orderContext.getOrderModel().orderNo());
                            LOGGER.info("逆向单到付现结，原单费用明细和原单台账应收金额其中一项查询为空, 财务明细空:{},台账查询接口返回空：{}",
                                    CollectionUtils.isEmpty(financeDetails), (getMerchantOrderBankFacadeResponse == null || getMerchantOrderBankFacadeResponse.getReceivableAmount() == null));
                        } else {
                            //原单费用明细及原单台账应收金额查询均不为空，但原单实际总应收和原单台账应收金额不一致
                            LOGGER.info("逆向单到付现结，财务明细非空，计算原单的总应收:{},refOrderBankReturn(去除cod):{}", refOrderReceive, refOrderBankReturn);
                            if (refOrderReceive.compareTo(refOrderBankReturn) != 0) {
                                //为空报警，发邮件
                                Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ASYN_ENQUIRY_ORDER_BANK_FAIL, "逆向单异步询价,原单费用明细及原单台账应收金额查询均不为空，但原单实际总应收和原单台账应收金额(扣除代收货款)不一致,orderNo:" + orderContext.getOrderModel().orderNo());
                                LOGGER.info("逆向单到付现结，原单费用明细及原单台账应收金额查询均不为空，但原单实际总应收和原单台账应收金额(扣除代收货款)不一致, 原单总应收:{},台账接口返回总应收:{},代收货款:{}",
                                        refOrderReceive, getMerchantOrderBankFacadeResponse.getReceivableAmount(), refCod);
                            }
                        }
                    }
                }
            } else {
                //走原逻辑: 对原单合并，折后金额加到一起
                LOGGER.info("逆向单到付现结，台账合并开关关闭，补全计费明细信息");
                //原单询价
                BillingEnquiryFacadeResponse refBillingEnquiryFacadeResponse = this.billingEnquiry(refOrderContext, orderContext.getOrderModel());

                //原单、新单费用合并至新单
                BigDecimal refOrderMoney = BigDecimal.ZERO;
                if (refBillingEnquiryFacadeResponse != null && refBillingEnquiryFacadeResponse.getFinanceFacadeDto() != null
                        && refBillingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount() != null
                        && refBillingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount().getAmount() != null) {
                    refOrderMoney = refOrderMoney.add(refBillingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount().getAmount());
                }
                this.complementReverseBillingResult(orderContext, billingEnquiryFacadeResponse, refOrderMoney, false);
            }
        } else {
            //原单非到付现结只询新单
            LOGGER.info("逆向单补全计费明细信息,原单非到付现结只询新单");
            enquiryFacadeTranslator.complementBillingResult(orderContext, billingEnquiryFacadeResponse);
        }
        LOGGER.info("逆向单补全计费明细信息结束");
        return false;
    }

    /**
     * 获取B商家台账费用明细中的到付运费金额
     *
     * @param businessNo
     * @return
     */
    private BigDecimal getBMerchantDfAmount(String businessNo) {
        BigDecimal result = BigDecimal.ZERO;
        GetMerchantOrderBankFacadeResponse response = getMerchantOrderBankFacade.searchReceivableFullInfo(businessNo);
        if (null != response && CollectionUtils.isNotEmpty(response.getReceivableDetails())) {
            List<ReceivableDetailFacade> receivableDetails = response.getReceivableDetails();
            for (ReceivableDetailFacade detailFacade : receivableDetails) {
                if (BMerchantReceiveTypeEnum.DF.getCode().equals(detailFacade.getReceiveType())) {
                    result = detailFacade.getReceivableAmount();
                }
            }
        }
        return result;
    }

    /**
     * 逆向单是否需要加原单金额
     * true:需要，false:不需要
     *
     * @return
     */
    public boolean isNeedSumOriginalOrder(ExpressOrderModel originalOrderModel) {
        if (originalOrderModel.isReceiverPayOnDelivery()) {
            //原单到付-需要和原单求和
            LOGGER.info("是否需要加原单金额，原单到付现结-后款--需要");
            return true;
        } else if (originalOrderModel.isCashOnPick()) {
            if (PaymentStatusEnum.COMPLETE_PAYMENT != originalOrderModel.getFinance().getPaymentStatus()) {
                if (originalOrderModel.getFinance().getPayment() != null && expressUccConfigCenter.isNeedSumOriginalOrderPaymentWhite(originalOrderModel.getFinance().getPayment().getCode())) {
                    LOGGER.info("是否需要加原单金额，原单寄付现结、支付状态不是支付完成、支付方式为{}--需要", originalOrderModel.getFinance().getPayment());
                    return true;
                }
            }
        }
        LOGGER.info("是否需要加原单金额--不需要");
        return false;
    }

    private boolean orderStatusCheck(ExpressOrderContext orderContext) {
        //订单状态检查. 已取消的订单直接忽略
        if (OrderStatusEnum.CANCELED == orderContext.getOrderModel().getOrderStatus().getOrderStatus()) {
            LOGGER.info("订单状态为已取消，忽略这个消息");
            return true;
        }
        //已支付的不操作台账
        if (PaymentStatusEnum.COMPLETE_PAYMENT == orderContext.getOrderModel().getFinance().getPaymentStatus()) {
            LOGGER.info("已支付的不操作台账，忽略这个消息");
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ASYN_ENQUIRY_ORDER_BANK_FAIL, "已支付的不操作台账,orderNo:" + orderContext.getOrderModel().orderNo());
            return true;
        }
        return false;
    }

    /**
     * 异步询价后释放积分
     *
     * @param orderContext
     */
    private void releasePoints(ExpressOrderContext orderContext) {
        if (orderContext.getOrderModel().getFinance().getPoints() == null) {
            LOGGER.info("无积分信息，不用回退积分");
            return;
        }
        ExpressOrderModel model = orderContext.getOrderModel();
        Points points = model.getFinance().getPoints();
        if (points.getRedeemPointsQuantity() == null || points.getOriginalPointsQuantity() == null) {
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ASNY_RELEASE_POINTS_ORDER_ALARM_MONITOR, "异步询价释放积分异常，orderNo=" + model.orderNo());
            LOGGER.info("异步询价释放积分异常,orderNo:{}", model.orderNo());
            return;
        }
        if (points.getOriginalPointsQuantity().getValue().compareTo(points.getRedeemPointsQuantity().getValue()) > 0) {
            LOGGER.info("异步询价需要释放积分");
            IntegralReleaseMessageDto messageDto = new IntegralReleaseMessageDto();
            messageDto.setWaybillNo(model.getRefOrderInfoDelegate().getWaybillNo());
            messageDto.setOrderNo(model.orderNo());
            messageDto.setRollbackNumber(points.getOriginalPointsQuantity().getValue().subtract(points.getRedeemPointsQuantity().getValue()).intValue());
            messageDto.setPin(model.getOperator());
            messageDto.setRelease(true);
            messageDto.setTitle(IntegralOperaterEnum.RELEASE.getTitle());
            messageDto.setCancelFlag(false);
            LOGGER.info("异步询价释放积分参数：{}", JSONUtils.beanToJSONDefault(messageDto));
            SchedulerMessage schedulerMessage = new SchedulerMessage();
            schedulerMessage.setDtoJson(JSONUtils.beanToJSONDefault(messageDto));
            schedulerMessage.setDtoClass(IntegralReleaseMessageDto.class);
            schedulerService.addSchedulerTask(PDQTopicEnum.INTEGRAL_RELEASE, schedulerMessage, FlowConstants.EXPRESS_ORDER_INTEGRAL_FLOW_CODE);
        }
    }

    /**
     * 调用台账接口修改台账
     *
     * @param messageDto
     * @param orderContext
     * @param middleRequest
     */
    private void modifyOrderBank(OrderBankPdqMessageDto messageDto, ExpressOrderContext orderContext, OrderBankFacadeMiddleRequest middleRequest) {
        if (middleRequest.getBMerchantDfModify() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = c2cOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setBMerchantModify(middleRequest.getBMerchantDfModify());
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, C2CAsynEnquiryOrderBankHandler.class);
            if (orderBankFacadeResponse != null) {
                c2cOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }
        if (middleRequest.getBMerchantCodModify() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = c2cOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setBMerchantModify(middleRequest.getBMerchantCodModify());
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, C2CAsynEnquiryOrderBankHandler.class);
            if (orderBankFacadeResponse != null) {
                c2cOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }
        if (middleRequest.getBMerchantJfModify() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = c2cOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setBMerchantModify(middleRequest.getBMerchantJfModify());
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, C2CAsynEnquiryOrderBankHandler.class);
            if (orderBankFacadeResponse != null) {
                c2cOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }
        if (middleRequest.getPosJfYun() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = c2cOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setPosJfYun(middleRequest.getPosJfYun());
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, C2CAsynEnquiryOrderBankHandler.class);
            if (orderBankFacadeResponse != null) {
                c2cOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }
        if (middleRequest.getPosYun() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = c2cOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setPosYun(middleRequest.getPosYun());
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, C2CAsynEnquiryOrderBankHandler.class);
            if (orderBankFacadeResponse != null) {
                c2cOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }
        //税金台账创建
        if (middleRequest.getBMerchantTaxCreate() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = c2cOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setOrgId(OTSLedgerUtil.OrgConstant.HKMO_TAX_ORG_ID);
            orderBankFacadeRequest.setOrgName(OTSLedgerUtil.OrgConstant.HKMO_TAX_ORG_NAME);
            orderBankFacadeRequest.setWaybillNo(middleRequest.getTaxBankServiceOrderNo());
            middleRequest.getBMerchantTaxCreate().setPayMode(PayModeEnum.COD);
            orderBankFacadeRequest.setBMerchantCreate(middleRequest.getBMerchantTaxCreate());
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, C2CAsynEnquiryOrderBankHandler.class);
            if (orderBankFacadeResponse != null) {
                c2cOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }
        if (middleRequest.getPosYunTax() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = c2cOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setOrgId(OTSLedgerUtil.OrgConstant.HKMO_TAX_ORG_ID);
            orderBankFacadeRequest.setOrgName(OTSLedgerUtil.OrgConstant.HKMO_TAX_ORG_NAME);
            orderBankFacadeRequest.setWaybillNo(middleRequest.getTaxBankServiceOrderNo());
            orderBankFacadeRequest.setPosYun(middleRequest.getPosYunTax());
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, C2CAsynEnquiryOrderBankHandler.class);
            if (orderBankFacadeResponse != null) {
                c2cOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }
    }

    /**
     * 持久化异常处理
     */
    private void produceRetryMq(RequestProfile requestProfile, ModifyOrderFacadeRequest modifyOrderFacadeRequest) {
        SchedulerMessage schedulerMessage = new SchedulerMessage();
        //持久化消息
        ModifyRepositoryMessageDto modifyRepositoryMessageDto = this.toMessageDto(requestProfile, modifyOrderFacadeRequest);
        schedulerMessage.setDtoJson(JSONUtils.beanToJSONDefault(modifyRepositoryMessageDto));
        schedulerMessage.setDtoClass(ModifyRepositoryMessageDto.class);
        schedulerService.addSchedulerTask(PDQTopicEnum.REVERSE_REPOSITORY_RETRY, schedulerMessage,
                FlowConstants.EXPRESS_ORDER_REVERSE_REPOSITORY_FLOW_CODE);
    }

    /**
     * 下发ofc
     *
     * @param orderContext
     * @throws ParseException
     */
    private void issueOrder(ExpressOrderContext orderContext) throws ParseException {
        Set<String> promiseUnits = makingDispatcherHandler.execute(orderContext);
        Optional.ofNullable(promiseUnits).orElseThrow(() ->
                new BusinessDomainException(UnifiedErrorSpec.BasisOrder.INTERNAL_ERROR).withCustom("下发履约执行层打标无法识别")
        );
        //下发履约层复制给业务身份对象信息
        promiseUnits.forEach(promiseUnit -> orderContext.getOrderModel().getBusinessIdentity().setFulfillmentUnit(promiseUnit));
        //订单领域模型
        promiseUnits.forEach(promiseUnit -> orderContext.getOrderModel().getOrderBusinessIdentity().setFulfillmentUnit(promiseUnit));
        //下发
        ModifyIssueFacadeRequest modifyIssueFacadeRequest = modifyIssueFacadeTranslator.toReverseOrChangeAddressIssueFacadeRequest(
                orderContext);
        modifyIssueFacade.modifyIssue(modifyIssueFacadeRequest, orderContext.getOrderModel().getOrderBusinessIdentity());
    }

    /**
     * 询价，逆向单原单询价时，需要传入逆向单新单支付信息
     *
     * @param orderContext
     * @param reverseOrderModel
     * @return
     */
    private BillingEnquiryFacadeResponse billingEnquiry(ExpressOrderContext orderContext, ExpressOrderModel reverseOrderModel) {
        // 逆向单询价时 orderContext 是逆向单
        LOGGER.info("当前单询价，order:{}",JSONUtils.beanToJSONDefault(orderContext.getOrderModel()));
        BillingEnquiryFacadeRequest billingEnquiryFacadeRequest = enquiryFacadeTranslator.toBillingEnquiryFacadeRequest(orderContext);
        if (reverseOrderModel != null && OrderTypeEnum.RETURN_ORDER == reverseOrderModel.getOrderType()) {
            billingEnquiryFacadeRequest.setReverseOrderInfo(new BillingEnquiryFacadeRequest.ReverseOrderInfo());
            billingEnquiryFacadeRequest.getReverseOrderInfo().setSettlementType(reverseOrderModel.getFinance().getSettlementType());
            // 设置逆向拓展字段  一定要调用这个方法，否则设置的 orderStatus	单据状态还是默认的0
            enquiryFacadeTranslator.toSetReverseExtendProps(billingEnquiryFacadeRequest);
        }
        return enquiryFacade.billingEnquiry(billingEnquiryFacadeRequest);
    }

    /**
     * 组装context
     *
     * @param messageDto
     * @param orderFacadeResponse
     * @return
     */
    private ExpressOrderContext toExpressOrderContext(OrderBankPdqMessageDto messageDto, GetOrderFacadeResponse orderFacadeResponse) {
        ExpressOrderModelCreator orderModelCreator = orderModelCreatorTranslator.toExpressOrderModelCreator(orderFacadeResponse);
        ExpressOrderModel orderModel = new ExpressOrderModel(orderModelCreator);
        orderModel.withRequestProfile(messageDto.getRequestProfile());
        ExpressOrderContext orderContext = new ExpressOrderContext(orderModel.getBusinessIdentity(), messageDto.getRequestProfile()
                , messageDto.getBusinessIdentity().getBusinessScene());
        orderContext.setOrderModel(orderModel);
        return orderContext;
    }

    /**
     * 查询订单详情
     *
     * @param messageDto
     * @param orderNo
     * @return
     */
    private GetOrderFacadeResponse toGetOrderFacadeResponse(OrderBankPdqMessageDto messageDto, String orderNo) {
        GetOrderFacadeRequest facadeRequest = new GetOrderFacadeRequest();
        facadeRequest.setOrderNo(orderNo);
        return getOrderFacade.getOrder(messageDto.getRequestProfile(), facadeRequest);
    }

    private ExpressOrderModelCreator parseCollectionOrgModelCreator(String orgId, String orgName) {
        ExpressOrderModelCreator creater = new ExpressOrderModelCreator();
        FinanceInfoDto financeInfoDto = new FinanceInfoDto();
        financeInfoDto.setCollectionOrgNo(orgId);
        financeInfoDto.setCollectionOrgName(orgName);
        creater.setFinanceInfo(financeInfoDto);
        return creater;
    }

    /**
     * 原单实际总应收加和新单询价的折后金额后落在新单的折后金额上
     * 这里不再操作原单明细到新单上
     *
     * @param expressOrderContext
     * @param currentOrder
     * @param refOrderReceive
     * @param isHKMOReaddress 是否港澳同城改址，逆向单计算原单费用
     */
    public void complementReverseBillingResult(ExpressOrderContext expressOrderContext, BillingEnquiryFacadeResponse currentOrder, BigDecimal refOrderReceive, boolean isHKMOReaddress) {
        ExpressOrderModelCreator expressOrderModelCreator = new ExpressOrderModelCreator();

        FinanceInfoDto financeInfoDto = new FinanceInfoDto();
        //折前金额
        MoneyInfoDto preAmount = new MoneyInfoDto();
        //折前金额加和原单的折后金额
        if (currentOrder.getFinanceFacadeDto().getPreAmount().getAmount() != null) {
            preAmount.setAmount(currentOrder.getFinanceFacadeDto().getPreAmount().getAmount().add(refOrderReceive).setScale(BusinessConstants.DEFAULT_AMOUNT_DECIMAL_SCALE, RoundingMode.HALF_UP));
        } else {
            preAmount.setAmount(refOrderReceive);
        }
        preAmount.setCurrencyCode(currentOrder.getFinanceFacadeDto().getPreAmount().getCurrencyCode());
        financeInfoDto.setPreAmount(preAmount);

        //折后金额
        MoneyInfoDto discountAmount = new MoneyInfoDto();
        if (currentOrder.getFinanceFacadeDto().getDiscountAmount().getAmount() != null) {
            financeInfoDto.setRemark("逆向单合并支付，加和原单金额:"
                    + refOrderReceive + ",新单金额:"
                    + currentOrder.getFinanceFacadeDto().getDiscountAmount().getAmount()
                    + ",合并支付后新单总金额:"
                    + currentOrder.getFinanceFacadeDto().getDiscountAmount().getAmount().add(refOrderReceive).setScale(BusinessConstants.DEFAULT_AMOUNT_DECIMAL_SCALE, RoundingMode.HALF_UP));

            discountAmount.setAmount(currentOrder.getFinanceFacadeDto().getDiscountAmount().getAmount().add(refOrderReceive).setScale(BusinessConstants.DEFAULT_AMOUNT_DECIMAL_SCALE, RoundingMode.HALF_UP));
        } else {
            financeInfoDto.setRemark("逆向单合并支付，加和原单金额:"
                    + refOrderReceive + ",新单金额:"
                    + 0
                    + ",合并支付后新单总金额:"
                    + refOrderReceive);

            discountAmount.setAmount(refOrderReceive);
        }
        discountAmount.setCurrencyCode(currentOrder.getFinanceFacadeDto().getDiscountAmount().getCurrencyCode());
        financeInfoDto.setDiscountAmount(discountAmount);

        //计费重量
        financeInfoDto.setBillingWeight(currentOrder.getFinanceFacadeDto().getBillingWeight());
        //计费体积
        financeInfoDto.setBillingVolume(currentOrder.getFinanceFacadeDto().getBillingVolume());
        //计费模式
        financeInfoDto.setBillingMode(currentOrder.getFinanceFacadeDto().getBillingMode());

        //费用明细
        List<FinanceDetailInfoDto> financeDetailInfoDtoList = new ArrayList<>();
        for (BillingEnquiryFacadeResponse.FinanceDetailFacadeDto detailFacadeDto : currentOrder.getFinanceFacadeDto().getFinanceDetailFacadeDtoList()) {
            FinanceDetailInfoDto detailInfoDto = toFinanceDetailInfoDto(detailFacadeDto);
            financeDetailInfoDtoList.add(detailInfoDto);
        }
        if (refOrderReceive != null) {
            CurrencyCodeEnum currencyCodeEnum = CurrencyCodeEnum.CNY;
            if (isHKMOReaddress) {
                // fixme 目前只处理港澳改址。港澳订单改址目前只有同城改址，判断币种看逆向单或者原单都可
                currencyCodeEnum = getCurrencyCodeEnum(expressOrderContext.getOrderModel());
            }
            FinanceDetailInfoDto financeDetailInfoDtoOld = new FinanceDetailInfoDto();
            financeDetailInfoDtoOld.setCostNo("9999");
            financeDetailInfoDtoOld.setCostName("原单应收总费用");
            MoneyInfoDto moneyInfoDto = new MoneyInfoDto();
            moneyInfoDto.setAmount(refOrderReceive);
            moneyInfoDto.setCurrencyCode(currencyCodeEnum);
            financeDetailInfoDtoOld.setPreAmount(moneyInfoDto);
            financeDetailInfoDtoOld.setDiscountAmount(moneyInfoDto);
            //原单号
            financeDetailInfoDtoOld.setRemark(expressOrderContext.getOrderModel().getOrderSnapshot().orderNo());
            financeDetailInfoDtoList.add(financeDetailInfoDtoOld);
        }
        financeInfoDto.setFinanceDetailInfos(financeDetailInfoDtoList);
//        financeInfoDto.setPayDeadline(currentOrder.getFinanceFacadeDto().getPayDeadline());
        expressOrderModelCreator.setFinanceInfo(financeInfoDto);
        expressOrderContext.getOrderModel().complement().complementFinanceInfo(this, expressOrderModelCreator);
    }

    private FinanceDetailInfoDto toFinanceDetailInfoDto(BillingEnquiryFacadeResponse.FinanceDetailFacadeDto detailFacadeDto) {
        FinanceDetailInfoDto detailInfoDto = new FinanceDetailInfoDto();
        //折前金额
        MoneyInfoDto detailPreAmount = new MoneyInfoDto();
        detailPreAmount.setAmount(detailFacadeDto.getPreAmount() != null ? detailFacadeDto.getPreAmount().getAmount() : null);
        detailPreAmount.setCurrencyCode(detailFacadeDto.getPreAmount() != null ? detailFacadeDto.getPreAmount().getCurrencyCode() : null);
        detailInfoDto.setPreAmount(detailPreAmount);
        //折后金额
        MoneyInfoDto detailDiscountAmount = new MoneyInfoDto();
        detailDiscountAmount.setAmount(detailFacadeDto.getDiscountAmount() != null ? detailFacadeDto.getDiscountAmount().getAmount() : null);
        detailDiscountAmount.setCurrencyCode(detailFacadeDto.getDiscountAmount() != null ? detailFacadeDto.getDiscountAmount().getCurrencyCode() : null);
        detailInfoDto.setDiscountAmount(detailDiscountAmount);
        detailInfoDto.setCostNo(detailFacadeDto.getCostNo());
        detailInfoDto.setCostName(detailFacadeDto.getCostName());
        detailInfoDto.setProductNo(detailFacadeDto.getProductNo());
        detailInfoDto.setProductName(detailFacadeDto.getProductName());

        detailInfoDto.setExtendProps(new HashMap<>());
        // 价格项明细
        if (detailFacadeDto.getExtendProps() != null && detailFacadeDto.getExtendProps().containsKey(CALC_PRICE_ITEM_LIST)) {
            detailInfoDto.getExtendProps().put(CALC_PRICE_ITEM_LIST, JSONUtils.beanToJSONDefault(detailFacadeDto.getExtendProps().get(CALC_PRICE_ITEM_LIST)));
        }
        return detailInfoDto;
    }

    /**
     * 转换消息
     *
     * @return
     */
    private ModifyRepositoryMessageDto toMessageDto(RequestProfile requestProfile, ModifyOrderFacadeRequest modifyOrderFacadeRequest) {
        ModifyRepositoryMessageDto messageDto = new ModifyRepositoryMessageDto();
        messageDto.setRequestProfile(requestProfile);
        messageDto.setModifyOrderFacadeRequest(modifyOrderFacadeRequest);
        return messageDto;
    }

    /**
     * 港澳-出口-拦截逆向
     * @param orderModel
     * @return
     */
    private boolean isHKMOExportInterceptReverse(ExpressOrderModel orderModel) {
        return orderModel.isHKMO() //港澳
                && orderModel.isInterceptReverse() //拦截逆向
                && FLowDirectionUtils.FLowDirection.CN2HKMO == FLowDirectionUtils.getFLowDirection(orderModel.getOrderSnapshot().getCustoms()) //原单内地到港澳
                ;
    }

    /**
     * 出口-拦截逆向
     * @param orderModel
     * @return
     */
    private boolean isExportInterceptReverse(ExpressOrderModel orderModel) {
        return orderModel.getOrderSnapshot().isExport() //原单出口
                && orderModel.isInterceptReverse() //拦截逆向
                ;
    }

    /**
     * 逆向单原单询价
     * @param originContext  原单
     * @param reverseContext 逆向单
     * @return
     */
    private BillingEnquiryFacadeResponse originOrderEnquiry(ExpressOrderContext originContext, ExpressOrderContext reverseContext) {
        ExpressOrderModel reverseOrderModel = reverseContext.getOrderModel();
        ExpressOrderModel orderModel = originContext.getOrderModel();
        LOGGER.info("拦截原单询价,当前单:{},原单:{}",JSONUtils.beanToJSONDefault(reverseOrderModel),JSONUtils.beanToJSONDefault(orderModel));
        BillingEnquiryFacadeRequest facadeRequest = enquiryFacadeTranslator.toBillingEnquiryFacadeRequest(originContext);
        //收件人信息拦截逆向，需要替换收件人地址-为逆向单寄件人地址
        BillingEnquiryFacadeRequest.AddressFacadeDto addressFacadeDto = toConsigneeAddressFacadeDto(reverseOrderModel.getConsignor().getAddress());
        BillingEnquiryFacadeRequest.ConsigneeFacadeDto consigneeFacadeDto = new BillingEnquiryFacadeRequest.ConsigneeFacadeDto();
        consigneeFacadeDto.setAddressFacadeDto(addressFacadeDto);
        facadeRequest.setConsigneeFacadeDto(consigneeFacadeDto);
        //计费询价跨境 始末地址 原单询价按逆向新单取反赋值
        String startRegionNo = reverseOrderModel.getConsignee().getAddress().getRegionNo();
        String endRegionNo = reverseOrderModel.getConsignor().getAddress().getRegionNo();
        if(StringUtils.isNotBlank(startRegionNo) && StringUtils.isNotBlank(endRegionNo)){
            facadeRequest.getExtendProps().put(EnquiryConstants.CROSS_BORDER_TYPE,startRegionNo + "_" + endRegionNo);
        } else {
            facadeRequest.getExtendProps().remove(EnquiryConstants.CROSS_BORDER_TYPE);
        }
        // 拦截都不应该传换汇币种；不会出现跨境后拦截
        facadeRequest.getExtendProps().remove(EnquiryConstants.EXCHANGE_CURRENCY);
        //国际拦截逆向原单询价 需要使用逆向单的主产品
        if(orderModel.isIntl()){
            intlReplaceProduct(facadeRequest, reverseContext);
        }
        return enquiryFacade.billingEnquiry(facadeRequest);
    }


    /**
     * 收件人信息 传过来的orderModel
     * @param consigneeAddress
     * @return
     */
    private BillingEnquiryFacadeRequest.AddressFacadeDto toConsigneeAddressFacadeDto(Address consigneeAddress) {

        BillingEnquiryFacadeRequest.AddressFacadeDto addressFacadeDto = new BillingEnquiryFacadeRequest.AddressFacadeDto();
        if (consigneeAddress != null) {
            //收件人行政区｜国家
            addressFacadeDto.setRegionNo(consigneeAddress.getRegionNo());
            addressFacadeDto.setRegionName(consigneeAddress.getRegionName());
            //收件人省
            addressFacadeDto.setProvinceNoGis(consigneeAddress.getProvinceNoGis());
            addressFacadeDto.setProvinceNo(consigneeAddress.getProvinceNo());
            // 收件人市
            addressFacadeDto.setCityNoGis(consigneeAddress.getCityNoGis());
            addressFacadeDto.setCityNo(consigneeAddress.getCityNo());
            //收件人县
            addressFacadeDto.setCountyNoGis(consigneeAddress.getCountyNoGis());
            addressFacadeDto.setCountyNo(consigneeAddress.getCountyNo());
            addressFacadeDto.setTownNoGis(consigneeAddress.getTownNoGis());
            addressFacadeDto.setTownNo(consigneeAddress.getTownNo());
        }
        return addressFacadeDto;
    }


    /**
     * 当前单单是否需要询价
     * @param orderModel
     * @return
     */
    public boolean needCurrentEnquiry(ExpressOrderModel orderModel) {
        //港澳拦截逆向单
        if(orderModel.isHKMO() && orderModel.isInterceptReverse()){
            // 出口的逆向单需要询价
            if(FLowDirectionUtils.FLowDirection.CN2HKMO == FLowDirectionUtils.getFLowDirection(orderModel.getOrderSnapshot().getCustoms())){
                LOGGER.info("拦截逆向当前单询价-出口单-需要询价,当前单:{}",orderModel.orderNo());
                return true;
            }
            // 进口的逆向单-只有原单到付-才需要询价
            else if(FLowDirectionUtils.FLowDirection.HKMO2CN == FLowDirectionUtils.getFLowDirection(orderModel.getOrderSnapshot().getCustoms())
                    && orderModel.getOrderSnapshot().isCashOnDelivery()){
                LOGGER.info("拦截逆向当前单询价-进口原单到付-需要询价,当前单:{}",orderModel.orderNo());
                return true;
            } else {
                // 同城互寄不需要询价
                LOGGER.info("拦截逆向当前单询价-同城互寄或进口原单寄付-不需要询价,当前单:{}",orderModel.orderNo());
                return false;
            }
        }
        return true;
    }

    /**
     * 国际拦截逆向原单询价 需要使用逆向单的主产品
     * fixme 现在收入归属是按产品区分的 所以新增国际产品，
     *  // 正向逆向新建两不同产品：国际特惠送(收件地址不包含国内，所以拦截逆向 国内到国内的正向单价格本无法配置，无法支持询价)  国际特惠送逆向
     *  // 且 正向单增值产品参与询价
     *  // 且正向单的附加服务费不参与询价，原因：
     * @param facadeRequest
     * @param reverseContext
     */
    private void intlReplaceProduct(BillingEnquiryFacadeRequest facadeRequest, ExpressOrderContext reverseContext){
        List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtoList = facadeRequest.getProductFacadeDtoList().stream()
                .filter(productFacadeDto -> {return Boolean.FALSE.equals(productFacadeDto.isAttachFees());})
                .map(productFacadeDto -> {
                    if (ServiceProductTypeEnum.MAIN_PRODUCT.getCode().equals(productFacadeDto.getProductType())) {
                        IProduct mainProduct = reverseContext.getOrderModel().getProductDelegate().getMainProduct();
                        BillingEnquiryFacadeRequest.ProductFacadeDto reverseProductFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                        reverseProductFacadeDto.setProductNo(mainProduct.getProductNo());
                        reverseProductFacadeDto.setProductType(mainProduct.getProductType());
                        reverseProductFacadeDto.setParentNo(mainProduct.getParentNo());
                        reverseProductFacadeDto.setProductAttrs(mainProduct.getProductAttrs());
                        return reverseProductFacadeDto;
                    } else {
                        return productFacadeDto;
                    }
                }).collect(Collectors.toList());
        facadeRequest.setProductFacadeDtoList(productFacadeDtoList);
        LOGGER.info("国际出口拦截逆向，原单询价,产品信息:{}",JSONUtils.beanToJSONDefault(productFacadeDtoList));
    }

    /**
     * 港澳国际跨境业务-逆向单-询原单场景
     * @param refOrderContext 原单上下文
     * @param orderContext 新单上下文
     * @param billingEnquiryFacadeResponse 新单询价结果
     */
    private void crossBorderReverseOriEnquiry(ExpressOrderContext refOrderContext, ExpressOrderContext orderContext, BillingEnquiryFacadeResponse billingEnquiryFacadeResponse){
        //当前订单币种
        CurrencyCodeEnum currencyCode = billingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount().getCurrencyCode();
        //原单询价金额直接继承原单场景 不再重新询价获取场景
        //1 港澳 拒收｜清关逆向 原单到付 新单到付
        if(refOrderContext.getOrderModel().isHKMO()//原单港澳订单
                && (orderContext.getOrderModel().isRejectReverse()//新单为拒收逆向
                || orderContext.getOrderModel().isCustomsClearanceReverse())//新单为清关逆向
                && refOrderContext.getOrderModel().isCashOnDelivery()//原单到付现结
                && orderContext.getOrderModel().isCashOnDelivery()//新单到付现结
        ){
            LOGGER.info("国际港澳出口-原单询价金额直接继承原单场景,当前单:{},原单:{}",orderContext.getOrderModel().orderNo(),refOrderContext.getOrderModel().orderNo());
            //原单询价汇前金额-报价币种
            BigDecimal refOrderMoney;
            Map<String, String> extendProps = refOrderContext.getOrderModel().getFinance().getExtendProps();
            if(MapUtils.isNotEmpty(extendProps) && StringUtils.isNotBlank(extendProps.get(FinanceConstants.BEFORE_EXCHANGE_DISCOUNT_AMOUNT))){
                //原单询价汇前金额
                MoneyInfoDto beforeExchangeDiscountAmount = JSONUtils.jsonToBean(extendProps.get(FinanceConstants.BEFORE_EXCHANGE_DISCOUNT_AMOUNT), MoneyInfoDto.class);
                refOrderMoney = beforeExchangeDiscountAmount.getAmount();
                //原单询价汇前币种
                //如果金额>0 且币种和逆向单币种一致则加和处理
                if(null != refOrderMoney && refOrderMoney.compareTo(BigDecimal.ZERO) > 0
                        && beforeExchangeDiscountAmount.getCurrencyCode() == currencyCode){
                    complementReverseBillingResult(orderContext, billingEnquiryFacadeResponse, refOrderMoney, false);
                }
            }
        } else if (isHKMOExportInterceptReverse(orderContext.getOrderModel()) || isExportInterceptReverse(orderContext.getOrderModel())){
            //原单重新询价场景
            //港澳-出口-拦截逆向 || fixme 国际出口拦截逆向
            LOGGER.info("国际港澳出口-原单重新询价场景-拦截逆向,当前单:{},原单:{}",orderContext.getOrderModel().orderNo(),refOrderContext.getOrderModel().orderNo());
            BillingEnquiryFacadeResponse refBillingEnquiryFacadeResponse = originOrderEnquiry(refOrderContext, orderContext);
            //原单、新单费用合并至新单
            BigDecimal refOrderMoney = BigDecimal.ZERO;
            if (refBillingEnquiryFacadeResponse != null && refBillingEnquiryFacadeResponse.getFinanceFacadeDto() != null
                    && refBillingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount() != null
                    && refBillingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount().getAmount() != null) {
                //原单费用
                refOrderMoney = refOrderMoney.add(refBillingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount().getAmount());
            }
            complementReverseBillingResult(orderContext, billingEnquiryFacadeResponse, refOrderMoney, false);
        } else {
            LOGGER.info("国际港澳出口-默认原单不询价场景,当前单:{},原单:{}",orderContext.getOrderModel().orderNo(),refOrderContext.getOrderModel().orderNo());
            complementReverseBillingResult(orderContext, billingEnquiryFacadeResponse, BigDecimal.ZERO, false);
        }

        // 到付税金处理
        dfOriTax(refOrderContext,orderContext,billingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount().getCurrencyCode());
    }

    /**
     * 到付原单税金处理
     * @param refOrderContext
     * @param orderContext
     * @param currencyCode
     */
    private void dfOriTax(ExpressOrderContext refOrderContext, ExpressOrderContext orderContext, CurrencyCodeEnum currencyCode){
        //原单税金到付处理
        Finance finance = refOrderContext.getOrderModel().getFinance();
        if(null != finance && null != finance.getEstimatedTax()
                && null != finance.getEstimatedTax().getAmount()
                && finance.getEstimatedTax().getAmount().compareTo(BigDecimal.ZERO) > 0
                && null != finance.getTaxSettlementType()
                && SettlementTypeEnum.CASH_ON_DELIVERY.getCode().equals(finance.getTaxSettlementType())){
            LOGGER.info("国际港澳出口-税金处理,当前单:{},原单:{}",orderContext.getOrderModel().orderNo(),refOrderContext.getOrderModel().orderNo());
            //todo 税金换汇
            //汇率
            BigDecimal exchangeRate = getExchangeRate(finance.getEstimatedTax().getCurrency().getCode(), currencyCode.getCode());
            orderContext.getOrderModel().getFinance()
                    .complementTax(exchangeAmt(finance.getEstimatedTax(), exchangeRate, currencyCode)
                    , exchangeAmt(finance.getActualTax(), exchangeRate, currencyCode)
                    , finance.getTaxSettlementType());
        }
    }

    /**
     *
     * @param fromCurrency 换汇前币种
     * @param toCurrency 换汇后币种
     * @return rate 汇率
     */
    private BigDecimal getExchangeRate(String fromCurrency, String toCurrency){
        if(StringUtils.isBlank(fromCurrency) || StringUtils.isBlank(toCurrency)){
            return BigDecimal.ONE;
        }
        if(fromCurrency.equals(toCurrency)){
            return BigDecimal.ONE;
        }
        ExchangeCurrencyRequest request = new ExchangeCurrencyRequest();
        request.setBaseCurrency(fromCurrency);
        request.setToCurrency(toCurrency);
        return exchangeCurrencyFacade.exchangeCurrency(request).getExchangeRate();
    }

    /**
     * @param beforeExchangeAmt 换汇前金额
     * @param exchangeRate 汇率
     * @param currencyCode 换汇后币种
     * @return 换汇后金额
     */
    private Money exchangeAmt(Money beforeExchangeAmt, BigDecimal exchangeRate, CurrencyCodeEnum currencyCode){
        if(null == beforeExchangeAmt){
            return null;
        }
        Money afterExchangeAmt = new Money();
        afterExchangeAmt.setCurrency(currencyCode);
        //四舍五入保留两位小数
        afterExchangeAmt.setAmount(beforeExchangeAmt.getAmount().multiply(exchangeRate)
                .setScale(0, RoundingMode.HALF_UP));
        return afterExchangeAmt;
    }

    /**
     * 港澳同城改址从改址记录获取原单应收总费用，并补全到逆向单费用上
     * 参考非港澳的代码，不同之处是只从改址记录查费用
     * 备注：港澳当前只能逆向一次，并且改址只有港澳同城
     * @refOrderContext 原单（正向单）上下文
     * @orderContext 改址单上下文
     * @billingEnquiryFacadeResponse 改址单询价结果
     */
    private void getOriginOrderTotalAmountByModifyRecords(ExpressOrderContext refOrderContext, ExpressOrderContext orderContext, BillingEnquiryFacadeResponse billingEnquiryFacadeResponse) {
        //原单存在改址记录，则需根据改址记录和复重复量方询价记录，查询原单到付未支付的台账信息，金额合并继承到新单
        List<ModifyRecord> modifyRecords = refOrderContext.getOrderModel().getModifyRecordDelegate().getEnabledModifyRecords();
        BigDecimal refOrderBankReturn = BigDecimal.ZERO;
        for (ModifyRecord modifyRecord : modifyRecords) {
            BigDecimal dfAmount = BigDecimal.ZERO;
            if (ModifyRecordTypeEnum.READDRESS.getCode().equals(modifyRecord.getModifyRecordType())// 改址记录
                    || ModifyRecordTypeEnum.INTERCEPT.getCode().equals(modifyRecord.getModifyRecordType())) {// 拦截记录
                //改址记录
                ReaddressRecordDetailInfo readdressDetail = (ReaddressRecordDetailInfo) modifyRecord.getModifyRecordDetail();
                if (SettlementTypeEnum.CASH_ON_DELIVERY.getCode().equals(readdressDetail.getFinance().getSettlementType())
                        && !PaymentStatusEnum.COMPLETE_PAYMENT.getPaymentStatus().equals(readdressDetail.getFinance().getPaymentStatus())
                        && (null != readdressDetail.getFinance().getPendingMoney() && BigDecimal.ZERO.compareTo(readdressDetail.getFinance().getPendingMoney().getAmount()) < 0)) {
                    //到付&&未支付&&待支付金额大于0的改址记录的台账金额，需继承到逆向新单上
                    dfAmount = getBMerchantDfAmount(modifyRecord.getModifyRecordNo());
                    LOGGER.info("到付&&未支付&&待支付金额大于0的改址记录号:{},dfAmount{}",modifyRecord.getModifyRecordNo(),dfAmount);
                } else {
                    continue;
                }
            } else if (ModifyRecordTypeEnum.RECHECK.getCode().equals(modifyRecord.getModifyRecordType())) {
                //复重复量方询价记录记录
                dfAmount = getBMerchantDfAmount(modifyRecord.getModifyRecordNo());
                LOGGER.info("复重复量方询价记录号:{},dfAmount{}",modifyRecord.getModifyRecordNo(),dfAmount);
            }
            refOrderBankReturn = refOrderBankReturn.add(dfAmount);
        }
        // 唯一的isHKMOReaddress=true
        this.complementReverseBillingResult(orderContext, billingEnquiryFacadeResponse, refOrderBankReturn, true);

        // 税金处理
        dfOriTax(refOrderContext,orderContext,billingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount().getCurrencyCode());
    }

    /**
     * 从快照获取币种
     */
    private CurrencyCodeEnum getCurrencyCodeEnum(ExpressOrderModel orderSnapshot) {
        CurrencyCodeEnum currencyCodeEnum = CurrencyCodeEnum.CNY;
        if(orderSnapshot.isHKMO() || orderSnapshot.isIntl()){
            if(null != orderSnapshot.getConsignor()
                    && null != orderSnapshot.getConsignor().getAddress()
                    && StringUtils.isNotBlank(orderSnapshot.getConsignor().getAddress().getRegionNo())
                    && null != orderSnapshot.getConsignee()
                    && null != orderSnapshot.getConsignee().getAddress()
                    && StringUtils.isNotBlank(orderSnapshot.getConsignee().getAddress().getRegionNo())
            ){
                String startRegionNo = orderSnapshot.getConsignor().getAddress().getRegionNo();
                String endRegionNo = orderSnapshot.getConsignee().getAddress().getRegionNo();
                if(null != orderSnapshot.getFinance() && null != orderSnapshot.getFinance().getSettlementType()){
                    //换汇币种
                    // fixme 当前港澳改址改派只有同城改址，原单结算方式和改址结算方式不同不影响换汇币种；后续放开非同城改址需要考虑传改址结算方式
                    return ReceiptCurrencyUtil.getCurrency(startRegionNo, endRegionNo, orderSnapshot.getFinance().getSettlementType().getCode());
                }
            }
        }
        return currencyCodeEnum;
    }
}

