package cn.jdl.oms.express.worker.scheduler.pay;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.GetOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.orderbank.RetailOrderBankFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.pay.WaybillRefundFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderModelCreatorTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.QueryOrderBankPayDetailsResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.pay.WaybillRefundFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.pay.WaybillRefundFacadeResult;
import cn.jdl.oms.express.domain.infrs.acl.pl.pay.WaybillRefundFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.ohs.locals.redis.IRedisClient;
import cn.jdl.oms.express.domain.infrs.ohs.locals.redis.IRedisLock;
import cn.jdl.oms.express.domain.infrs.ohs.locals.redis.IRedisLockFactory;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.AbstractMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.message.SchedulerMessage;
import cn.jdl.oms.express.domain.lock.LockEntry;
import cn.jdl.oms.express.domain.spec.dict.PaymentStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.RefundStatusEnum;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.MerchantEnum;
import cn.jdl.oms.express.shared.common.dict.PDQTopicEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.InfrastructureException;
import cn.jdl.oms.express.shared.common.exception.ValidationRequestParamException;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import cn.jdl.oms.express.worker.scheduler.AbstractSchedulerHandler;
import com.jd.paq.core.Message;
import com.jd.paq.plugin.IMessage;
import com.jd.paq.plugin.PDQClientException;
import com.jd.paq.plugin.Result;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * 运单退款申请消息处理
 */
public class WaybillRefundApplyMessageHandler extends AbstractSchedulerHandler {

    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(WaybillRefundApplyMessageHandler.class);

    /**
     * 重试最大次数
     */
    private static final int MAX_RETRY_TIMES = 10;

    /**
     * 实收明细为空，重试最大次数
     */
    private static final int NO_PAY_MAX_RETRY_TIMES = 6;

    /**
     * 防并发锁过期时间，60分钟
     */
    private static final int CACHE_TIMEOUT = 60 * 60;

    /**
     * 防并发锁前缀
     * 商户号 + LOCK_PREFIX
     */
    private static final String LOCK_PREFIX = "REFUND_";

    @Resource
    private RetailOrderBankFacade retailOrderBankFacade;

    @Resource
    private GetOrderFacade getOrderFacade;

    @Resource
    private IRedisLockFactory redisLockFactory;

    @Resource
    private IRedisClient redisClient;

    @Resource
    private WaybillRefundFacadeTranslator waybillRefundFacadeTranslator;

    @Resource
    private WaybillRefundFacade waybillRefundFacade;

    @Resource
    private GetOrderModelCreatorTranslator orderModelCreatorTranslator;

    @Override
    public Result execute(IMessage iMessage) throws PDQClientException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_WORKER_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        // 任务调度执行结果对象根据实际的执行结果定义返回具体的业务结果
        Result result = new Result(Result.SUCCESS);
        result.setCode(1);
        try {
            if (iMessage instanceof Message) {
                String iMessageContent = iMessage.getMessageBody();
                SchedulerMessage schedulerMessage = JSONUtils.jsonToBean(iMessageContent, SchedulerMessage.class);
                if (null == schedulerMessage) {
                    LOGGER.info("运单退款任务调度【{}】,场景业务数据对象不存在,暂停重试", iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }

                AbstractMessageDto waybillRefundApplyMessage = (AbstractMessageDto) JSONUtils.jsonToBean(schedulerMessage.getDtoJson(), schedulerMessage.getDtoClass());
                if (waybillRefundApplyMessage == null) {
                    LOGGER.info("运单退款任务调度【{}】,场景业务数据对象不存在,暂停重试", iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }
                if (((Message) iMessage).getRedriveCount() >= MAX_RETRY_TIMES) {
                    //超过重试次数不能修改为失败
                    //refundFacade.modifyOrderRefund(refundRequest, RefundStatusEnum.REFUNDFAILED);
                    LOGGER.info("运单退款任务调度【{}】,重试次数超过" + MAX_RETRY_TIMES + "次,暂停重试,退款任务置为失败！", iMessage.getTopic());
                    result.setCode(Result.INVALID_REQUEST);
                    return result;
                }

                PDQTopicEnum pdqTopicEnum = PDQTopicEnum.getByTopic(iMessage.getTopic());
                if (null == pdqTopicEnum) {
                    LOGGER.info("运单退款任务调度【{}】,未匹配到任务队列,暂停重试", iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }

                // 退款
                refund(waybillRefundApplyMessage, ((Message) iMessage).getRedriveCount());
            }
        } catch (BusinessDomainException be) {
            //不用体现可用率
            LOGGER.error("运单退款业务异常,再次重试", be);
            result.setCode(Result.SYSTEMERROR);
            result.setReason(be.getMessage());
            return result;
        } catch (Exception e) {
            LOGGER.error("运单退款任务调度执行异常,再次重试", e);
            result.setCode(Result.SYSTEMERROR);
            result.setReason(e.getMessage());
            Profiler.functionError(callerInfo);
            return result;
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
        return result;
    }

    /**
     * 运单退款
     */
    private void refund(AbstractMessageDto waybillRefundApplyMessage, int retryTimes) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".refund"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        IRedisLock redisLock = null;
        try {
            String waybillRefundApplyMessageStr = JSONUtils.beanToJSONDefault(waybillRefundApplyMessage);
            LOGGER.info("运单退款入参：waybillRefundApplyMessage={}", waybillRefundApplyMessageStr);

            // 入参合法性校验
            validateRefundApplyMessage(waybillRefundApplyMessage);

            // 获取订单详情
            GetOrderFacadeResponse order = getOrderFacadeResponse(waybillRefundApplyMessage.getRequestProfile(), waybillRefundApplyMessage);

            // 商户ID：快运：10033（改址寄付是10117，原单是10033。查实收是查原单）
            String merchantId = MerchantEnum.FREIGHT.getMerchantId();
            if (order.getFinance() != null
                    && (PaymentTypeEnum.ONLINE_PAY.getCode().equals(order.getFinance().getPayment())
                    || PaymentTypeEnum.PAY_BEFORE_PICKUP.getCode().equals(order.getFinance().getPayment()))) {
                merchantId = MerchantEnum.FREIGHT_ONLINE_PAY_AFTER_PICKUP.getMerchantId();
            }

            LOGGER.info("orderNo={},waybillNo={},merchantId={}", order.getOrderNo(), order.getCustomOrderNo(), merchantId);

            // 防重
            redisLock = tryLock(order.getOrderNo(), order.getCustomOrderNo(), merchantId);
            if (null == redisLock) {
                LOGGER.error("运单退款-重复执行，直接返回，orderNo:{}，waybillCode:{}，merchantId:{}", order.getOrderNo(), order.getCustomOrderNo(), merchantId);
                return;
            }

            // 是否需要退款
            if (!ifNeedRefund(order)) {
                LOGGER.info("订单{}不需要退款", waybillRefundApplyMessage.getOrderNo());
                LOGGER.info("运单退款-释放锁: {}", redisLock.getRedisValue());
                redisLock.unlock();
                LOGGER.info("运单退款-释放锁成功");
                return;
            }

            // 获取运单号
            String waybillCode = order.getCustomOrderNo();

            // 调外单台账获取实收明细
            QueryOrderBankPayDetailsResponse queryOrderBankPayDetailsResponse = retailOrderBankFacade.getRefundableAmountAndPayDetails(waybillCode, merchantId);
            LOGGER.info("运单退款-外单台账获取实收明细结果{}", JSONUtils.beanToJSONDefault(queryOrderBankPayDetailsResponse));

            // 处理未获取到实收明细
            if (CollectionUtils.isEmpty(queryOrderBankPayDetailsResponse.getPayDetails())) {
                LOGGER.info("外单台账获取实收明细信息为空");
                //外单台账获取实收明细为空，则进行重试，超过重试最大次数，则
                if (retryTimes >= NO_PAY_MAX_RETRY_TIMES) {
                    if (PaymentStatusEnum.COMPLETE_PAYMENT.getStatus() == order.getFinance().getPaymentStatus()) {
                        LOGGER.error("外单台账获取实收明细为空，但是支付状态为已支付，退款入参：waybillRefundApplyMessage={}", waybillRefundApplyMessage);
                        Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_REFUND_EXCEPTION_ALARM
                                , System.currentTimeMillis()
                                        + "链路追踪ID:" + waybillRefundApplyMessage.getRequestProfile().getTraceId()
                                        + ","
                                        + "业务身份:" + waybillRefundApplyMessage.getBusinessIdentity().getBusinessUnit()
                                        + ","
                                        + "业务类型:" + waybillRefundApplyMessage.getBusinessIdentity().getBusinessType()
                                        + ","
                                        + "外单台账获取实收明细为空，但是支付状态为已支付，退款订单号:" + waybillRefundApplyMessage.getOrderNo());
                        //退款失败以接受退款结果为准
                        //refundFacade.modifyOrderRefund(refundRequest, RefundStatusEnum.REFUNDFAILED);
                    }
                    return;
                } else {
                    LOGGER.warn("运单{}外单台账未查到实收明细，进行业务重试，进行第{}次重试，最大重试次数{}", waybillCode, retryTimes, NO_PAY_MAX_RETRY_TIMES);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.EPT_REFUND_APPLY_FAIL).withCustom("外单台账未查到实收明细，且未达到重试次数，进行业务重试");
                }
            }

            // 运单退款申请
            WaybillRefundFacadeRequest waybillRefundFacadeRequest = waybillRefundFacadeTranslator.toWaybillRefundFacadeRequest(waybillRefundApplyMessage, order, queryOrderBankPayDetailsResponse);
            LOGGER.error("运单退款-运单退款入参：orderNo={}, waybillRefundFacadeRequest={}", waybillRefundApplyMessage.getOrderNo(), JSONUtils.beanToJSONDefault(waybillRefundFacadeRequest));
            WaybillRefundFacadeResult waybillRefundFacadeResult = waybillRefundFacade.refundApply(waybillRefundFacadeRequest);
            if (waybillRefundFacadeResult.isSuccess()) {
                LOGGER.info("运单退款-运单退款申请成功：orderNo={}", waybillRefundApplyMessage.getOrderNo());
            } else {
                LOGGER.error("运单退款-运单退款申请失败：orderNo={}, waybillRefundFacadeRequest={}, waybillRefundFacadeResult={}", waybillRefundApplyMessage.getOrderNo(), JSONUtils.beanToJSONDefault(waybillRefundFacadeRequest), JSONUtils.beanToJSONDefault(waybillRefundFacadeResult));
                throw new InfrastructureException(UnifiedErrorSpec.BasisOrder.EPT_REFUND_APPLY_FAIL).withCustom("运单退款申请失败");
            }

            // 运单退款持久化：退款状态更新为退款中
            boolean ifSuccessModify = waybillRefundFacade.modifyOrderRefund(waybillRefundFacadeRequest, RefundStatusEnum.REFUNDING);
            if (ifSuccessModify) {
                LOGGER.info("运单退款-持久化数据成功：orderNo={}", waybillRefundApplyMessage.getOrderNo());
            } else {
                LOGGER.error("运单退款-持久化数据失败：orderNo={}, waybillRefundFacadeRequest={}, waybillRefundFacadeResult={}", waybillRefundApplyMessage.getOrderNo(), JSONUtils.beanToJSONDefault(waybillRefundFacadeRequest), JSONUtils.beanToJSONDefault(waybillRefundFacadeResult));
            }
        } catch (ValidationRequestParamException ve) {
            //需要体现可用率关注
            Profiler.functionError(callerInfo);
            LOGGER.error("运单退款校验异常,需排查订单信息是否异常", ve);
            throw ve;
        } catch (BusinessDomainException be) {
            //不用体现可用率
            LOGGER.error("运单退款业务异常", be);
            throw be;
        } catch (InfrastructureException ie) {
            //需要体现可用率关注
            Profiler.functionError(callerInfo);
            LOGGER.error("运单退款基础设施服务异常", ie);
            throw ie;
        } catch (Exception e) {
            //需要体现可用率关注
            LOGGER.error("运单退款未知异常", e);
            Profiler.functionError(callerInfo);
            throw e;
        } finally {
            if (null != redisLock) {
                LOGGER.info("运单退款-释放锁: {}", redisLock.getRedisValue());
                redisLock.unlock();
                LOGGER.info("运单退款-释放锁成功");
            }
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 入参合法性校验
     */
    private void validateRefundApplyMessage(AbstractMessageDto waybillRefundApplyMessage) {
        LOGGER.info("运单退款参数入参合法性校验,waybillRefundApplyMessage:{}", waybillRefundApplyMessage);

        // 订单号不能为空
        if (StringUtils.isBlank(waybillRefundApplyMessage.getOrderNo())) {
            LOGGER.error("运单退款-订单号为空");
            throw new ValidationRequestParamException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("运单退款-订单号为空");
        }

        // 业务单元不能为空
        if (waybillRefundApplyMessage.getBusinessIdentity() == null) {
            LOGGER.error("运单退款-业务单元为空");
            throw new ValidationRequestParamException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("运单退款-业务单元为空");
        }

    }

    /**
     * 获取订单详情
     */
    private GetOrderFacadeResponse getOrderFacadeResponse(RequestProfile requestProfile, AbstractMessageDto waybillRefundApplyMessage) {
        GetOrderFacadeRequest getOrderFacadeRequest = new GetOrderFacadeRequest();
        getOrderFacadeRequest.setOrderNo(waybillRefundApplyMessage.getOrderNo());
        return getOrderFacade.getOrder(requestProfile, getOrderFacadeRequest);
    }

    /**
     * 退款增加并发锁
     */
    private IRedisLock tryLock(String orderNo, String waybillCode, String merchantId) {
        try {
            String lockKey = orderNo + waybillCode;
            LockEntry lockEntry = new LockEntry(lockKey, CACHE_TIMEOUT, TimeUnit.SECONDS);
            lockEntry.withPrefix(LOCK_PREFIX + merchantId);
            IRedisLock redisLock = redisLockFactory.create(redisClient, lockEntry);
            if (redisLock.tryLock()) {
                LOGGER.info("运单退款-加锁成功，orderNo:{}，waybillCode:{}", orderNo, waybillCode);
                return redisLock;
            } else {
                LOGGER.error("运单退款-加锁失败，orderNo:{}，waybillCode:{}", orderNo, waybillCode);
                return null;
            }
        } catch (Exception e) {
            LOGGER.error("运单退款-并发锁写入异常:", e);
            throw e;
        }
    }

    /**
     * 判断是否需要退款
     */
    private boolean ifNeedRefund(GetOrderFacadeResponse order) {
        // 财务信息不能为空
        if (order.getFinance() == null) {
            LOGGER.error("运单退款-财务信息为空");
            throw new ValidationRequestParamException(UnifiedErrorSpec.BasisOrder.ORDER_STATUS_VALIDATE_FAIL)
                    .withCustom("运单退款-财务信息为空");
        }

        // 判断是否已经退款或者正在退款中
        if (order.getFinance().getRefundStatus() != null) {
            RefundStatusEnum refundStatus = RefundStatusEnum.of(order.getFinance().getRefundStatus());
            if (refundStatus == RefundStatusEnum.REFUNDED || refundStatus == RefundStatusEnum.REFUNDING) {
                LOGGER.info("运单退款-当前订单状态是：{}, 不需要退款", refundStatus.getDesc());
                return false;
            }
        }

        return true;
    }

}
