package cn.jdl.oms.express.worker.scheduler.enquiry;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.bo.CustomerConfig;
import cn.jdl.oms.express.domain.dto.FinanceDetailInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceInfoDto;
import cn.jdl.oms.express.domain.dto.VolumeInfoDto;
import cn.jdl.oms.express.domain.dto.WeightInfoDto;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.CustomerConfigFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.enquiry.ComputeFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.enquiry.EnquiryFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.issue.ModifyIssueFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.GetOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.ModifyOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.orderbank.GetMerchantOrderBankFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.orderbank.OrderBankFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.org.OrderbankOrgFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.waybill.WayBillQueryFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.customer.BasicTraderResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.ComputeFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.ComputeFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.ComputeFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.FreightCreateEnquiryFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.FreightEnquiryFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.ModifyIssueFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.ModifyIssueFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderModelCreatorTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.ModifyOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.ModifyOrderFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.FreightOrderBankFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeMiddleRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankRedisOp;
import cn.jdl.oms.express.domain.infrs.acl.pl.org.OrderbankOrgFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.org.OrderbankOrgFacadeRquest;
import cn.jdl.oms.express.domain.infrs.acl.pl.org.OrderbankOrgFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.repository.OrderDataNotifyTranslator;
import cn.jdl.oms.express.domain.infrs.acl.util.FinanceUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedBusinessIdentityUtil;
import cn.jdl.oms.express.domain.infrs.ohs.locals.es.orderflow.ExpressOrderFlowService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.OrderDataFlowDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.waybill.WaybillInfoMappingUtil;
import cn.jdl.oms.express.domain.infrs.ohs.locals.promise.MakingDispatcherHandler;
import cn.jdl.oms.express.domain.infrs.ohs.locals.redis.IRedisLock;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.ModifyRepositoryMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.OrderBankPdqMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.impl.SchedulerService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.message.SchedulerMessage;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.OrderSignEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.utils.OrderDataFieldEnum;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedB2CUtil;
import cn.jdl.oms.express.domain.vo.OrderBusinessIdentity;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessTypeEnum;
import cn.jdl.oms.express.shared.common.dict.OperateTypeEnum;
import cn.jdl.oms.express.shared.common.dict.PDQTopicEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.mdc.MDCTraceConstants;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import cn.jdl.oms.express.worker.scheduler.AbstractSchedulerHandler;
import com.jd.paq.core.Message;
import com.jd.paq.plugin.IMessage;
import com.jd.paq.plugin.PDQClientException;
import com.jd.paq.plugin.Result;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * 快运异步询价台账消息处理
 */
public class FreightAsynEnquiryOrderBankHandler extends AbstractSchedulerHandler {

    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(FreightAsynEnquiryOrderBankHandler.class);

    /**
     * 查询订单防腐层
     */
    @Resource
    private GetOrderFacade getOrderFacade;

    /**
     * 修改防腐层
     */
    @Resource
    private ModifyOrderFacade modifyOrderFacade;

    /**
     * 修改防腐层对象转换器
     */
    @Resource
    private ModifyOrderFacadeTranslator modifyOrderFacadeTranslator;

    /**
     * 订单详情model转换
     */
    @Resource
    private GetOrderModelCreatorTranslator orderModelCreatorTranslator;

    /**
     * 支付机构facade
     */
    @Resource
    private OrderbankOrgFacade orderbankOrgFacade;

    /**
     * 询价facade
     */
    @Resource
    private EnquiryFacade enquiryFacade;

    /**
     * 机构信息获取防腐层转换器
     */
    @Resource
    private OrderbankOrgFacadeTranslator orderbankOrgFacadeTranslator;

    /**
     * 接单询价防腐层转换器
     */
    @Resource
    private FreightCreateEnquiryFacadeTranslator freightCreateEnquiryFacadeTranslator;

    /**
     * 台账防腐层
     */
    @Resource
    private OrderBankFacade orderBankFacade;

    /**
     * 台账防腐层转换器
     */
    @Resource
    private FreightOrderBankFacadeTranslator freightOrderBankFacadeTranslator;

    /**
     * 修改下发服务
     */
    @Resource
    private ModifyIssueFacade modifyIssueFacade;

    /**
     * 修改下发防腐层转换器
     */
    @Resource
    private ModifyIssueFacadeTranslator modifyIssueFacadeTranslator;

    /**
     * 下发履约执行层达标逻辑
     */
    @Resource
    private MakingDispatcherHandler makingDispatcherHandler;

    /**
     * 任务调度
     */
    @Resource
    private SchedulerService schedulerService;

    /**
     * 单据台账锁
     */
    @Resource
    private OrderBankRedisOp orderBankRedisOp;

    /**
     * 商家基础信息
     */
    @Resource
    private CustomerConfigFacade customerConfigFacade;

    /**
     * B商家台账查询
     */
    @Resource
    private GetMerchantOrderBankFacade getMerchantOrderBankFacade;

    /**
     * 通用计费询价防腐层
     */
    @Resource
    private ComputeFacade computeFacade;

    /**
     * 通用计费询价防腐层转换器
     */
    @Resource
    private ComputeFacadeTranslator computeFacadeTranslator;

    /**
     * 运单详情查询
     */
    @Resource
    private WayBillQueryFacade wayBillQueryFacade;

    /**
     * 记录询价记录
     */
    @Resource
    private ExpressOrderFlowService expressOrderFlowService;

    /**
     * 运单数据model转订单model
     */
    @Resource
    private WaybillInfoMappingUtil waybillInfoMappingUtil;

    /**
     * 标准产品询价防腐层转换器
     */
    @Resource
    private FreightEnquiryFacadeTranslator freightEnquiryFacadeTranslator;

    /**
     * 配置中心
     */
    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    /**
     * 重试最大次数
     */
    private static final int MAX_RETRY_TIME = 10;

    /**
     * 订单标识的拒收类型
     */
    private static final String REJECTION_TYPE = "rejectionType";

    /**
     * 2代表包裹维度拒收，也就是全收半退
     */
    private static final String REJECTION_TYPE_VALUE = "2";

    /**
     * 订单数据流水转换器
     */
    @Resource
    private OrderDataNotifyTranslator orderDataNotifyTranslator;

    /**
     * 异步询价台账消息处理处理
     *
     * @param iMessage
     * @return
     * @throws PDQClientException
     * <AUTHOR>
    @Override
    public Result execute(IMessage iMessage) throws PDQClientException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        //任务调度执行结果对象根据实际的执行结果定义返回具体的业务结果
        Result result = new Result(Result.SUCCESS);
        FreightAsynEnquiryOrderBankHandler.Lock lock = new FreightAsynEnquiryOrderBankHandler.Lock();
        MDC.put(MDCTraceConstants.TRACEID, String.valueOf(System.nanoTime()));
        try {
            if (iMessage instanceof Message) {
                int retryTime = ((Message) iMessage).getRedriveCount();
                if (retryTime >= MAX_RETRY_TIME) {
                    LOGGER.info("异步询价台账消息处理任务调度【{}】,重试次数超过{}次,暂停重试", iMessage.getTopic(), MAX_RETRY_TIME);
                    result.setCode(Result.INVALID_REQUEST);
                    return result;
                }
                String iMessageContent = iMessage.getMessageBody();
                PDQTopicEnum pdqTopicEnum = PDQTopicEnum.getByTopic(iMessage.getTopic());
                if (null == pdqTopicEnum) {
                    LOGGER.info("异步询价台账消息处理任务调度【{}】未匹配到任务队列,暂停重试", iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }
                // 重试消息体
                SchedulerMessage schedulerMessage = JSONUtils.jsonToBean(
                        iMessageContent, SchedulerMessage.class);
                if (null == schedulerMessage) {
                    LOGGER.info("异步询价台账消息处理任务调度【{}】,重试消息体不存在,暂停重试", iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }
                OrderBankPdqMessageDto messageDto = JSONUtils.jsonToBean(schedulerMessage.getDtoJson(),
                        OrderBankPdqMessageDto.class);
                if (null == messageDto) {
                    LOGGER.info("异步询价台账消息处理任务调度【{}】,场景业务数据对象不存在,暂停重试", iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }
                if (!this.asynEnquiryOrderBankHandle(messageDto, lock)) {
                    LOGGER.info("异步询价台账消息处理任务调度【{}】,场景业务数据对象不存在,暂停重试", iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }
            }
        } catch (Exception e) {
            result.setCode(Result.SYSTEMERROR);
            result.setReason(e.getMessage());
            LOGGER.info("异步询价台账消息处理任务调度【{}】执行异常", iMessage.getTopic(), e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
            if (lock.getRedisLock() != null) {
                lock.getRedisLock().unlock();
            }
            MDC.remove(MDCTraceConstants.TRACEID);
        }
        return result;
    }

    /**
     * 异步询价-台账-持久化
     *
     * @param messageDto
     * @return
     */
    private boolean asynEnquiryOrderBankHandle(OrderBankPdqMessageDto messageDto, FreightAsynEnquiryOrderBankHandler.Lock lock) throws Exception {
        if (messageDto == null || StringUtils.isBlank(messageDto.getOrderNo())) {
            return true;
        }

        LOGGER.info("开始-快运异步询价-{}", messageDto.getOrderNo());
        //获取询价台账锁，获取不到锁重试. 需要做对应的释放功能
        IRedisLock redisLock = orderBankRedisOp.getLock(messageDto.getRequestProfile(), messageDto.getBusinessIdentity(), messageDto.getOrderNo());
        if (!redisLock.tryLock()) {
            LOGGER.info("订单获取台账锁失败，需要重试");
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ASYN_ENQUIRY_ORDER_BANK_FAIL, "订单orderNo:" + messageDto.getOrderNo() + "获取台账锁失败，需要重试");
            return false;
        }
        lock.setRedisLock(redisLock);

        //获取订单详情
        GetOrderFacadeResponse orderFacadeResponse = toGetOrderFacadeResponse(messageDto.getRequestProfile(), messageDto.getOrderNo());
        if (orderFacadeResponse == null) {
            LOGGER.error("异步询价台账消息处理查询订单为空");
            /*未获取到订单详情信息需继续重试*/
            return false;
        }
        //将订单详情转换成model
        ExpressOrderContext orderContext = toExpressOrderContext(messageDto, orderFacadeResponse);
        //订单状态检查. 已取消的订单直接忽略
        ExpressOrderModel orderModel = orderContext.getOrderModel();
        if (OrderStatusEnum.CANCELED == orderModel.getOrderStatus().getOrderStatus()) {
            LOGGER.info("订单状态为已取消，忽略这个消息");
            return true;
        }

        if (UnitedBusinessIdentityUtil.isUnitedIdentity(orderContext)) {
            // 更新为真实身份继续执行流程
            UnitedBusinessIdentityUtil.convertToReal(orderContext, true);
        }

        //判断台账是否初始化
        boolean init = orderBankRedisOp.haveInit(messageDto.getRequestProfile(), messageDto.getBusinessIdentity(), messageDto.getOrderNo(), true);
        if (!init) {
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ASYN_ENQUIRY_ORDER_BANK_FAIL, "订单orderNo:" + orderModel.orderNo() + "尚未初始化台账，需要重试");
            LOGGER.info("订单尚未初始化台账，需要重试");
            return false;
        }

        // 逆向快运接单异步询价写台账
        if (isReverseTransport(orderContext)) {
            // 快运月结逆向没有COD，不需要写台账
            if (SettlementTypeEnum.MONTHLY_PAYMENT == orderModel.getFinance().getSettlementType()) {
                Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ASYN_ENQUIRY_ORDER_BANK_FAIL, "快运逆向单结算方式为月结，不需要异步询价写台账，orderNo:" + orderModel.orderNo());
                return true;
            }

            // 将原单的快照信息存入当前单
            GetOrderFacadeResponse refOrderFacadeResponse = toGetOrderFacadeResponse(messageDto.getRequestProfile(), messageDto.getRelateOrderNo());
            if (refOrderFacadeResponse == null) {
                LOGGER.error("异步询价台账消息处理查询原订单为空,原单号：{}", messageDto.getRelateOrderNo());
                return false;
            }
            ExpressOrderContext refOrderContext = toExpressOrderContext(messageDto, refOrderFacadeResponse);
            orderModel.assignSnapshot(refOrderContext.getOrderModel());
            // key:订单号  value:计费信息
            BillingEnquiryFacadeResponse currentBillingEnquiryFacadeResponse = null;
            Map<String, BillingEnquiryFacadeResponse> billMap = new HashMap<>();

            // 逆向单询价（快运只允许退一次，并且快运没有改址单）
            if (needEnquiry(orderContext)) {
                currentBillingEnquiryFacadeResponse = toBillingEnquiryFacadeResponse(orderContext);
                billMap.put(orderModel.orderNo(), currentBillingEnquiryFacadeResponse);
            }
            if (needOriginEnquiry(orderModel)) {
                //原单需要询价
                BillingEnquiryFacadeResponse refBillingEnquiryFacadeResponse = null;
                if (orderModel.isFreightB2C()) {
                    // 快运B2C逆向单当前只有月结，实际走不到此处
                    refBillingEnquiryFacadeResponse = toBillingEnquiryFacadeResponse(refOrderContext);
                } else if (orderModel.isFreightC2C()) {
                    refBillingEnquiryFacadeResponse = originOrderEnquiry(refOrderContext.getOrderModel(), orderModel);
                }

                billMap.put(refOrderContext.getOrderModel().orderNo(), refBillingEnquiryFacadeResponse);
                if (currentBillingEnquiryFacadeResponse == null) {
                    currentBillingEnquiryFacadeResponse = refBillingEnquiryFacadeResponse;
                }
                if (SettlementTypeEnum.CASH_ON_PICK == refOrderContext.getOrderModel().getFinance().getSettlementType()) {
                    //如果原单寄付，且逆向单费用重新计入了原单费用
                    orderModel.putOrderSign(this, OrderSignEnum.ORIGINAL_ORDER_NEED_REFUND.getCode(), OrderConstants.ORIGINAL_ORDER_NEED_REFUND_FLAG_YES);
                }
            }

            // 退单和原单都不询价
            if (currentBillingEnquiryFacadeResponse == null) {
                Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ASYN_ENQUIRY_ORDER_BANK_FAIL, "退单不询价，原单非到付不询价，orderNo:" + orderModel.orderNo());
                return true;
            }

            // 更新补全财务信息
            freightEnquiryFacadeTranslator.complementSumBillingResult(orderContext, billMap, currentBillingEnquiryFacadeResponse);
            LOGGER.info("补全计费明细信息结束.billing: {}", JSONUtils.beanToJSONDefault(orderModel.getFinance()));

            //查询商家基础资料
            BasicTraderResponse basicTraderResponse = customerConfigFacade.getCustomerConfig(orderModel.getCustomer().getAccountNo());
            CustomerConfig customerConfig = new CustomerConfig();
            customerConfig.setCustomerId(basicTraderResponse.getCustomerId());
            customerConfig.setCustomerName(basicTraderResponse.getCustomerName());
            orderContext.setCustomerConfig(customerConfig);

            //查询支付机构id和名称
            OrderbankOrgFacadeResponse orderbankOrg = null;
            if (orderModel.isHKMO() && SettlementTypeEnum.CASH_ON_DELIVERY == orderModel.getFinance().getSettlementType()) {
                //港澳到付按收件人获取资金归属组织
                orderbankOrg = orderbankOrgFacade.getHMCashOnDeliveryOrgByAddress(orderContext);
            } else {
                OrderbankOrgFacadeRquest orderbankOrgFacadeRquest = orderbankOrgFacadeTranslator.toOrderbankOrgFacadeRquest(orderContext);
                orderbankOrg = orderbankOrgFacade.getOrderbankOrg(orderbankOrgFacadeRquest);
            }
            if (orderbankOrg == null) {
                LOGGER.error("获取机构信息失败");
                return false;
            }
            orderModel.complement().complementFinanceCollectionOrg(this, parseCollectionOrgModelCreator(
                    orderbankOrg.getOrgId(), orderbankOrg.getOrgName()));
            //台账入参转换
            OrderBankFacadeMiddleRequest middleRequest = freightOrderBankFacadeTranslator.toReverseReAddressOrderBankFacadeRequest(orderContext);
            modifyOrderBank(messageDto, orderContext, middleRequest);

            if (UnitedBusinessIdentityUtil.isUnitedIdentity(orderContext)) {
                // 更新为真实身份继续执行流程
                UnitedBusinessIdentityUtil.convertToOriginal(orderContext);
            }

            //下发ofc  仅有财务的
            issueOrder(orderContext);
            //持久化, 以及异常重试
            //仅有财务的
            ModifyOrderFacadeRequest facadeRequest = null;
            try {
                facadeRequest = modifyOrderFacadeTranslator.toReverseOrChangeAddressOrderFacadeRequest(orderContext);
                modifyOrderFacade.modifyOrder(orderModel.requestProfile(), facadeRequest);
            } catch (Exception e) {
                LOGGER.error("快运异步询价台账持久化防腐层异常", e);
                //触发重试
                if (orderModel.requestProfile() != null && facadeRequest != null) {
                    produceRetryMq(orderModel.requestProfile(), facadeRequest);
                }
            }
        } else {
            // 正向快运修改异步询价写台账（揽收后改址）
            // 将原单的快照信息存入当前单
            GetOrderFacadeResponse refOrderFacadeResponse = toGetOrderFacadeResponse(messageDto.getRequestProfile(), messageDto.getOrderNo());
            if (refOrderFacadeResponse == null) {
                LOGGER.error("异步询价台账消息处理查询原订单为空,原单号：{}", messageDto.getRelateOrderNo());
                return false;
            }
            ExpressOrderContext refOrderContext = toExpressOrderContext(messageDto, refOrderFacadeResponse);
            orderModel.assignSnapshot(refOrderContext.getOrderModel());
            // 非月结需要询价
            if (!SettlementTypeEnum.MONTHLY_PAYMENT.getCode().equals(orderModel.getFinance().getSettlementType().getCode())) {
                // 根据产品来区分调用标准产品计费还是通用计费
                if (modifyNeedStandardCompute(orderContext)) {
                    // 快运零担是通用计费
                    callStandardCompute(orderContext);
                } else {
                    // 特快重货，特惠零担、特快零担是标准产品计费
                    callStandardProduct(orderContext);
                }
                if (orderModel.getOrderSnapshot().isJDLToDPDelivery()) {
                    //京东转德邦，需要按结算方式、支付环节纬度计算多方计费总额
                    orderModel.complement().complementMultiPartiesTotalAmounts(this, FinanceUtil.computeMPTotalAmountsGroupSettleAndStage(orderModel, orderModel.getOrderSnapshot(), null, false, false));
                }
            }

            //查询商家基础资料
            BasicTraderResponse basicTraderResponse = customerConfigFacade.getCustomerConfig(orderModel.getCustomer().getAccountNo());
            CustomerConfig customerConfig = new CustomerConfig();
            customerConfig.setCustomerId(basicTraderResponse.getCustomerId());
            customerConfig.setCustomerName(basicTraderResponse.getCustomerName());
            orderContext.setCustomerConfig(customerConfig);

            //查询支付机构id和名称
            OrderbankOrgFacadeRquest orderbankOrgFacadeRquest = orderbankOrgFacadeTranslator.toOrderbankOrgFacadeRquest(orderContext);
            OrderbankOrgFacadeResponse orderbankOrg = orderbankOrgFacade.getOrderbankOrg(orderbankOrgFacadeRquest);
            if (orderbankOrg == null) {
                LOGGER.error("获取机构信息失败");
                return false;
            }
            orderModel.complement().complementFinanceCollectionOrg(this, parseCollectionOrgModelCreator(
                    orderbankOrg.getOrgId(), orderbankOrg.getOrgName()));

            //台账入参转换
            OrderBankFacadeMiddleRequest middleRequest = freightOrderBankFacadeTranslator.toModifyOrderBankFacadeRequest(orderContext);
            modifyOrderBank(messageDto, orderContext, middleRequest);

            if (UnitedBusinessIdentityUtil.isUnitedIdentity(orderContext)) {
                // 更新为真实身份继续执行流程
                UnitedBusinessIdentityUtil.convertToOriginal(orderContext);
            }

            //下发ofc  仅有财务的
            issueOrder(orderContext);

            //持久化, 以及异常重试
            //仅有财务的
            ModifyOrderFacadeRequest facadeRequest = null;
            try {
                facadeRequest = modifyOrderFacadeTranslator.toReverseOrChangeAddressOrderFacadeRequest(orderContext);
                modifyOrderFacade.modifyOrder(orderModel.requestProfile(), facadeRequest);
            } catch (Exception e) {
                LOGGER.error("快运异步询价台账持久化防腐层异常", e);
                //触发重试
                if (orderModel.requestProfile() != null && facadeRequest != null) {
                    produceRetryMq(orderModel.requestProfile(), facadeRequest);
                }
            }
            try {
                if (CollectionUtils.isNotEmpty(orderModel.getFinance().getMultiPartiesTotalAmounts())) {
                    //发送多方计费金额变更通知
                    OrderDataFlowDto dto = orderDataNotifyTranslator.toGeneralOrderDataFlowDto(orderContext.getOrderModel(), OrderConstants.NO_VAL, OrderDataFieldEnum.MULTI_PARTIES_TOTAL_AMOUNTS);
                    if (null != dto) {
                        expressOrderFlowService.sendOrderDataRecordMq(dto);
                    }
                }
            } catch (Exception e) {
                LOGGER.error("发送多方计费金额变更通知异常", e);
                //TODO 自定义告警
            }
        }
        LOGGER.info("结束-快运异步询价-{}", messageDto.getOrderNo());
        return true;
    }

    /**
     * 原单是否需要询价
     *
     * @param orderModel
     * @return
     */
    public boolean needOriginEnquiry(ExpressOrderModel orderModel) {
        if (orderModel.isHKMO()) {
            LOGGER.info("是否需要计算原单费用，港澳订单，原单不需要询价");
            return false;
        }

        if (SettlementTypeEnum.CASH_ON_DELIVERY == orderModel.getOrderSnapshot().getFinance().getSettlementType()) {
            //原单到付，原单需要询价
            LOGGER.info("是否需要计算原单费用，原单结算方式为到付，原单需要询价");
            return true;
        }

        if (orderModel.isFreightC2C()) {
            //拦截逆向，如果原单是寄付，需要询价
            if (isInterceptReverse(orderModel.getOrderSnapshot()) && SettlementTypeEnum.CASH_ON_PICK == orderModel.getOrderSnapshot().getFinance().getSettlementType()) {
                //原单寄付，原单需要询价
                LOGGER.info("是否需要计算原单费用，拦截逆向且原单结算方式为寄付，原单需要询价");
                return true;

            }
        }
        return false;
    }

    /**
     * 是否拦截逆向
     * 客户拒收，不是拦截逆向；
     * 非客户拒收，就是拦截逆向（fixme 半收不是客户拒收，也会叠加正向单费用）
     *
     * @return
     */
    private boolean isInterceptReverse(ExpressOrderModel originOrderModel) {
        if (OrderStatusEnum.CUSTOMER_REJECTED == originOrderModel.getOrderStatus().getOrderStatus()) {
            return false;
        }
        return true;
    }

    /**
     * 调用台账接口修改台账
     *
     * @param messageDto
     * @param orderContext
     * @param middleRequest
     */
    private void modifyOrderBank(OrderBankPdqMessageDto messageDto, ExpressOrderContext orderContext, OrderBankFacadeMiddleRequest middleRequest) {
        LOGGER.info("订单号：{}，modifyOrderBank写台账中间入参是：{}", orderContext.getOrderModel().orderNo(), JSONUtils.beanToJSONDefault(middleRequest));
        // pos寄付修改
        if (middleRequest.getPosJfYun() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = freightOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setPosJfYun(middleRequest.getPosJfYun());
            LOGGER.info("订单号：{}，PosJfYun写台账入参是：{}", orderContext.getOrderModel().orderNo(), JSONUtils.beanToJSONDefault(middleRequest));
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, FreightAsynEnquiryOrderBankHandler.class);
            LOGGER.info("订单号：{}，PosJfYun写台账出参是：{}", orderContext.getOrderModel().orderNo(), JSONUtils.beanToJSONDefault(orderBankFacadeResponse));
            if (orderBankFacadeResponse != null) {
                freightOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }

        // pos到付修改
        if (middleRequest.getPosYun() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = freightOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setPosYun(middleRequest.getPosYun());
            LOGGER.info("订单号：{}，PosYun写台账入参是：{}", orderContext.getOrderModel().orderNo(), JSONUtils.beanToJSONDefault(middleRequest));
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, FreightAsynEnquiryOrderBankHandler.class);
            LOGGER.info("订单号：{}，PosYun写台账出参是：{}", orderContext.getOrderModel().orderNo(), JSONUtils.beanToJSONDefault(orderBankFacadeResponse));
            if (orderBankFacadeResponse != null) {
                freightOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }

        // B商家到付修改
        if (middleRequest.getBMerchantDfModify() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = freightOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setBMerchantModify(middleRequest.getBMerchantDfModify());
            LOGGER.info("订单号：{}，BMerchantDfModify写台账入参是：{}", orderContext.getOrderModel().orderNo(), JSONUtils.beanToJSONDefault(middleRequest));
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, FreightAsynEnquiryOrderBankHandler.class);
            LOGGER.info("订单号：{}，BMerchantDfModify写台账出参是：{}", orderContext.getOrderModel().orderNo(), JSONUtils.beanToJSONDefault(orderBankFacadeResponse));
            if (orderBankFacadeResponse != null) {
                freightOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }

        // B商家代收货款修改
        if (middleRequest.getBMerchantCodModify() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = freightOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setBMerchantModify(middleRequest.getBMerchantCodModify());
            LOGGER.info("订单号：{}，BMerchantCodModify写台账入参是：{}", orderContext.getOrderModel().orderNo(), JSONUtils.beanToJSONDefault(middleRequest));
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, FreightAsynEnquiryOrderBankHandler.class);
            LOGGER.info("订单号：{}，BMerchantCodModify写台账出参是：{}", orderContext.getOrderModel().orderNo(), JSONUtils.beanToJSONDefault(orderBankFacadeResponse));
            if (orderBankFacadeResponse != null) {
                freightOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }

        // B商家寄付修改
        if (middleRequest.getBMerchantJfModify() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = freightOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setBMerchantModify(middleRequest.getBMerchantJfModify());
            LOGGER.info("订单号：{}，BMerchantJfModify写台账入参是：{}", orderContext.getOrderModel().orderNo(), JSONUtils.beanToJSONDefault(middleRequest));
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, FreightAsynEnquiryOrderBankHandler.class);
            LOGGER.info("订单号：{}，BMerchantJfModify写台账出参是：{}", orderContext.getOrderModel().orderNo(), JSONUtils.beanToJSONDefault(orderBankFacadeResponse));
            if (orderBankFacadeResponse != null) {
                freightOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }

        // 外单台账新增
        if (middleRequest.getOtsCreate() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = freightOrderBankFacadeTranslator.toCommonModifyOrderBankFacadeRequest(orderContext.getOrderModel(), orderContext.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setOtsCreate(middleRequest.getOtsCreate());
            LOGGER.info("订单号：{}，OtsCreate写台账入参是：{}", orderContext.getOrderModel().orderNo(), JSONUtils.beanToJSONDefault(middleRequest));
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, this.getClass());
            LOGGER.info("订单号：{}，OtsCreate写台账出参是：{}", orderContext.getOrderModel().orderNo(), JSONUtils.beanToJSONDefault(orderBankFacadeResponse));
            if (orderBankFacadeResponse != null) {
                freightOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }
    }

    /**
     * 持久化异常处理
     */
    private void produceRetryMq(RequestProfile modifyRequestProfile, ModifyOrderFacadeRequest modifyOrderFacadeRequest) throws ParseException {
        //持久化消息
        ModifyRepositoryMessageDto modifyRepositoryMessageDto = new ModifyRepositoryMessageDto();
        modifyRepositoryMessageDto.setRequestProfile(modifyRequestProfile);
        modifyRepositoryMessageDto.setModifyOrderFacadeRequest(modifyOrderFacadeRequest);

        SchedulerMessage schedulerMessage = new SchedulerMessage();
        schedulerMessage.setDtoJson(JSONUtils.beanToJSONDefault(modifyRepositoryMessageDto));
        schedulerMessage.setDtoClass(ModifyRepositoryMessageDto.class);
        schedulerService.addSchedulerTask(PDQTopicEnum.REVERSE_REPOSITORY_RETRY, schedulerMessage,
                FreightAsynEnquiryOrderBankHandler.class.getSimpleName());
    }

    /**
     * 下发ofc
     *
     * @param orderContext
     * @throws ParseException
     */
    private void issueOrder(ExpressOrderContext orderContext) throws ParseException {
        Set<String> promiseUnits = makingDispatcherHandler.execute(orderContext);
        Optional.ofNullable(promiseUnits).orElseThrow(() ->
                new BusinessDomainException(UnifiedErrorSpec.BasisOrder.INTERNAL_ERROR).withCustom("下发履约执行层打标无法识别")
        );
        //下发履约层复制给业务身份对象信息
        promiseUnits.forEach(promiseUnit -> orderContext.getOrderModel().getBusinessIdentity().setFulfillmentUnit(promiseUnit));
        //订单领域模型
        promiseUnits.forEach(promiseUnit -> orderContext.getOrderModel().getOrderBusinessIdentity().setFulfillmentUnit(promiseUnit));
        //下发
        ModifyIssueFacadeRequest modifyIssueFacadeRequest = modifyIssueFacadeTranslator.toReverseOrChangeAddressIssueFacadeRequest(
                orderContext);
        modifyIssueFacade.modifyIssue(modifyIssueFacadeRequest, orderContext.getOrderModel().getOrderBusinessIdentity());
    }

    /**
     * 询价
     */
    private BillingEnquiryFacadeResponse toBillingEnquiryFacadeResponse(ExpressOrderContext orderContext) {
        // 港澳
        if (orderContext.getOrderModel().isHKMO()) {
            BillingEnquiryFacadeRequest billingEnquiryFacadeRequest = freightEnquiryFacadeTranslator.toCreateSceneEnquiryRequest(orderContext);
            return enquiryFacade.billingEnquiry(billingEnquiryFacadeRequest);
        }

        // 快运零担是通用计费
        if (needStandardCompute(orderContext)) {
            ComputeFacadeRequest computeFacadeRequest = null;
            if (expressUccConfigCenter.isFreightNewEnquiryProcessSwitch()) {
                LOGGER.info("快运询价-新流程");
                if (OrderTypeEnum.RETURN_ORDER == orderContext.getOrderModel().getOrderType()) {
                    // 逆向单：重量体积数量，取下单数据
                    computeFacadeRequest = computeFacadeTranslator.toReverseCreateEnquiry(orderContext.getOrderModel());
                } else {
                    // 正向单：重量体积数量，取复核数据
                    computeFacadeRequest = computeFacadeTranslator.toReverseOriginOrderEnquiry(orderContext.getOrderModel());
                }
            } else {
                LOGGER.info("快运询价-旧流程");
                computeFacadeRequest = computeFacadeTranslator.toReverseCreateEnquiry(orderContext.getOrderModel());
            }
            ComputeFacadeResponse computeFacadeResponse = computeFacade.computeFreights(computeFacadeRequest);
            FinanceInfoDto financeInfoDto = computeFacadeTranslator.buildFinanceInfoDto(orderContext, computeFacadeResponse);
            BillingEnquiryFacadeResponse billingEnquiryFacadeResponse = new BillingEnquiryFacadeResponse();
            billingEnquiryFacadeResponse.setFinanceFacadeDto(toFinanceFacadeDto(financeInfoDto));
            return billingEnquiryFacadeResponse;
        }
        // 特快重货，特惠零担、特快零担是标准产品计费
        BillingEnquiryFacadeRequest billingEnquiryFacadeRequest = null;
        if (expressUccConfigCenter.isFreightNewEnquiryProcessSwitch()) {
            LOGGER.info("快运询价-新流程");
            if (OrderTypeEnum.RETURN_ORDER == orderContext.getOrderModel().getOrderType()) {
                // 逆向单：重量体积数量，取下单数据
                billingEnquiryFacadeRequest = freightCreateEnquiryFacadeTranslator.toBillingEnquiryFacadeRequest(orderContext.getOrderModel());
            } else {
                // 正向单：重量体积数量，取复核数据
                billingEnquiryFacadeRequest = freightCreateEnquiryFacadeTranslator.toReverseOriginOrderEnquiry(orderContext.getOrderModel());
            }
        } else {
            LOGGER.info("快运询价-旧流程");
            billingEnquiryFacadeRequest = freightCreateEnquiryFacadeTranslator.toBillingEnquiryFacadeRequest(orderContext.getOrderModel());
        }
        return enquiryFacade.billingEnquiry(billingEnquiryFacadeRequest);
    }

    /**
     * 逆向单原单询价
     *
     * @param orderModel        原单
     * @param reverseOrderModel 逆向单
     * @return
     */
    private BillingEnquiryFacadeResponse originOrderEnquiry(ExpressOrderModel orderModel, ExpressOrderModel reverseOrderModel) {
        //拒收类型
        String rejectionType = null;
        if (reverseOrderModel.getOrderSign() != null) {
            rejectionType = reverseOrderModel.getOrderSign().get(OrderSignEnum.REJECTION_TYPE.getCode());
        }
        BillingEnquiryFacadeRequest billingEnquiryFacadeRequest = freightCreateEnquiryFacadeTranslator.toReverseOriginOrderEnquiry(orderModel, reverseOrderModel, rejectionType);
        return enquiryFacade.billingEnquiry(billingEnquiryFacadeRequest);
    }

    /**
     * 通用计费询价的响应转为标准产品计费询价的响应
     * 原因：后续要叠加原单和退单的费用
     */
    private BillingEnquiryFacadeResponse.FinanceFacadeDto toFinanceFacadeDto(FinanceInfoDto financeInfoDto) {
        //折前金额
        BillingEnquiryFacadeResponse.MoneyFacadeDto preAmount = new BillingEnquiryFacadeResponse.MoneyFacadeDto();
        preAmount.setAmount(financeInfoDto.getPreAmount().getAmount());
        preAmount.setCurrencyCode(financeInfoDto.getPreAmount().getCurrencyCode());

        //折后金额
        BillingEnquiryFacadeResponse.MoneyFacadeDto discountAmount = new BillingEnquiryFacadeResponse.MoneyFacadeDto();
        discountAmount.setAmount(financeInfoDto.getDiscountAmount().getAmount());
        discountAmount.setCurrencyCode(financeInfoDto.getDiscountAmount().getCurrencyCode());

        //计费重量
        WeightInfoDto billingWeight = new WeightInfoDto();
        billingWeight.setValue(financeInfoDto.getBillingWeight().getValue());
        billingWeight.setUnit(financeInfoDto.getBillingWeight().getUnit());

        //计费体积
        VolumeInfoDto billingVolume = new VolumeInfoDto();
        billingVolume.setValue(financeInfoDto.getBillingVolume().getValue());
        billingVolume.setUnit(financeInfoDto.getBillingVolume().getUnit());

        //费用明细
        List<BillingEnquiryFacadeResponse.FinanceDetailFacadeDto> financeDetailFacadeDtoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(financeInfoDto.getFinanceDetailInfos())) {
            for (FinanceDetailInfoDto financeDetailInfoDto : financeInfoDto.getFinanceDetailInfos()) {
                //折前金额
                BillingEnquiryFacadeResponse.MoneyFacadeDto detailPreAmount = new BillingEnquiryFacadeResponse.MoneyFacadeDto();
                detailPreAmount.setAmount(financeDetailInfoDto.getPreAmount().getAmount());
                detailPreAmount.setCurrencyCode(financeDetailInfoDto.getPreAmount().getCurrencyCode());
                //折后金额
                BillingEnquiryFacadeResponse.MoneyFacadeDto detailDiscountAmount = new BillingEnquiryFacadeResponse.MoneyFacadeDto();
                detailDiscountAmount.setAmount(financeDetailInfoDto.getDiscountAmount().getAmount());
                detailDiscountAmount.setCurrencyCode(financeDetailInfoDto.getDiscountAmount().getCurrencyCode());

                BillingEnquiryFacadeResponse.FinanceDetailFacadeDto detailInfoDto = new BillingEnquiryFacadeResponse.FinanceDetailFacadeDto();
                detailInfoDto.setPreAmount(detailPreAmount);
                detailInfoDto.setDiscountAmount(detailDiscountAmount);
                detailInfoDto.setCostNo(financeDetailInfoDto.getCostNo());
                detailInfoDto.setCostName(financeDetailInfoDto.getCostName());
                detailInfoDto.setRemark(financeDetailInfoDto.getRemark());

                financeDetailFacadeDtoList.add(detailInfoDto);
            }
        }

        BillingEnquiryFacadeResponse.FinanceFacadeDto financeFacadeDto = new BillingEnquiryFacadeResponse.FinanceFacadeDto();
        financeFacadeDto.setPreAmount(preAmount);
        financeFacadeDto.setDiscountAmount(discountAmount);
        financeFacadeDto.setBillingWeight(billingWeight);
        financeFacadeDto.setBillingVolume(billingVolume);
        financeFacadeDto.setFinanceDetailFacadeDtoList(financeDetailFacadeDtoList);
        return financeFacadeDto;
    }

    /**
     * 组装context
     *
     * @param messageDto
     * @param orderFacadeResponse
     * @return
     */
    private ExpressOrderContext toExpressOrderContext(OrderBankPdqMessageDto messageDto, GetOrderFacadeResponse orderFacadeResponse) {
        ExpressOrderModelCreator orderModelCreator = orderModelCreatorTranslator.toExpressOrderModelCreator(orderFacadeResponse);
        ExpressOrderModel orderModel = new ExpressOrderModel(orderModelCreator);
        orderModel.withRequestProfile(messageDto.getRequestProfile());
        ExpressOrderContext orderContext = new ExpressOrderContext(orderModel.getOrderBusinessIdentity(), messageDto.getRequestProfile(), messageDto.getBusinessIdentity().getBusinessScene());
        orderContext.setOrderModel(orderModel);
        return orderContext;
    }
    
    /**
     * 查询订单详情
     *
     * @param profile
     * @param orderNo
     * @return
     */
    private GetOrderFacadeResponse toGetOrderFacadeResponse(RequestProfile profile, String orderNo) {
        GetOrderFacadeRequest facadeRequest = new GetOrderFacadeRequest();
        facadeRequest.setOrderNo(orderNo);
        return getOrderFacade.getOrder(profile, facadeRequest);
    }

    private ExpressOrderModelCreator parseCollectionOrgModelCreator(String orgId, String orgName) {
        ExpressOrderModelCreator creator = new ExpressOrderModelCreator();
        FinanceInfoDto financeInfoDto = new FinanceInfoDto();
        financeInfoDto.setCollectionOrgNo(orgId);
        financeInfoDto.setCollectionOrgName(orgName);
        creator.setFinanceInfo(financeInfoDto);
        return creator;
    }



    /**
     * 用于传输lock，避免在上下文中增加lock信息
     */
    private static class Lock {
        private IRedisLock redisLock;

        IRedisLock getRedisLock() {
            return redisLock;
        }

        void setRedisLock(IRedisLock redisLock) {
            this.redisLock = redisLock;
        }
    }

    /**
     * 判断逆向单是否需要询价
     */
    private boolean needEnquiry(ExpressOrderContext context) {
        ExpressOrderModel orderModel = context.getOrderModel();
        // 逆向单是全收半退并且重量体积全为零，不需要询价写台账
        boolean flag = !(isHalfReceive(orderModel) && isZeroVolumeAndZeroWeight(orderModel));
        if (flag) {
            LOGGER.info("订单号：{}，逆向单需要询价写台账", orderModel.orderNo());
        } else {
            LOGGER.info("订单号：{}，逆向单是全收半退并且重量体积全为零，不需要询价写台账：{}", orderModel.orderNo(), JSONUtils.beanToJSONDefault(orderModel));
        }
        return flag;
    }

    /**
     * 判断是否全收半退
     */
    private boolean isHalfReceive(ExpressOrderModel orderModel) {
        return MapUtils.isNotEmpty(orderModel.getOrderSign())
                && REJECTION_TYPE_VALUE.equals(orderModel.getOrderSign().get(REJECTION_TYPE));
    }

    /**
     * 判断是否体积重量都为零
     */
    private boolean isZeroVolumeAndZeroWeight(ExpressOrderModel orderModel) {
        if (orderModel.getCargoDelegate() == null) {
            return true;
        }
        return isNullOrZero(orderModel.getCargoDelegate().totalCargoVolume())
                && isNullOrZero(orderModel.getCargoDelegate().totalCargoWeight());
    }

    /**
     * 判断BigDecimal是否为null或者零
     */
    private boolean isNullOrZero(BigDecimal target) {
        return target == null || BigDecimal.ZERO.compareTo(target) == 0;
    }

    /**
     * 判断是否调用通用计费询价接口
     */
    private boolean needStandardCompute(ExpressOrderContext expressOrderContext) {
        // 快运零担是通用计费
        return expressOrderContext.getOrderModel().getProductDelegate().ofProductNo(ProductEnum.KYLD.getCode()) != null;
    }

    /**
     * 判断是否逆向快运
     */
    private boolean isReverseTransport(ExpressOrderContext orderContext) {
        ExpressOrderModel orderModel = orderContext.getOrderModel();
        OrderBusinessIdentity orderBusinessIdentity = orderModel.getOrderBusinessIdentity();

        if (orderModel.isFreight()) {
            return BusinessTypeEnum.REVERSE_TRANSPORT.getCode().equals(orderBusinessIdentity.getBusinessType());
        } else if (UnitedB2CUtil.isUnitedFreightB2C(orderModel)) {
            // 融合B2C传的是快递业务类型
            return BusinessTypeEnum.REVERSE_ORDER.getCode().equals(orderBusinessIdentity.getBusinessType());
        }

        return false;
    }

    /**
     * 调用标准产品询价接口
     * 揽收后改增值服务
     */
    private void callStandardProduct(ExpressOrderContext expressOrderContext) {
        // 转换成标准产品询价接口参数
        BillingEnquiryFacadeRequest billingEnquiryFacadeRequest = freightEnquiryFacadeTranslator.toModifyEnquiryRequest(expressOrderContext);
        LOGGER.info("orderNo:{} ,转换成标准产品询价参数 是 {}", expressOrderContext.getOrderModel().orderNo(), JSONUtils.beanToJSONDefault(billingEnquiryFacadeRequest));
        // 调用标准产品计费接口
        BillingEnquiryFacadeResponse billingEnquiryFacadeResponse = enquiryFacade.billingEnquiry(billingEnquiryFacadeRequest);
        // 补全计费结果
        freightEnquiryFacadeTranslator.complementBillingResult(expressOrderContext, billingEnquiryFacadeResponse);
    }

    /**
     * 调用通用计费询价接口
     * 揽收后改增值服务
     */
    private void callStandardCompute(ExpressOrderContext expressOrderContext) {
        // 转换成通用计费询价接口参数
        ComputeFacadeRequest computeFacadeRequest = computeFacadeTranslator.toModifyEnquiryRequest(expressOrderContext);
        LOGGER.info("orderNo:{} ,转换成通用计费询价接口参数 是 {}", expressOrderContext.getOrderModel().orderNo(), JSONUtils.beanToJSONDefault(computeFacadeRequest));
        // 调用通用计费询价接口
        ComputeFacadeResponse computeFacadeResponse = computeFacade.computeFreights(computeFacadeRequest);
        // 补全计费结果
        computeFacadeTranslator.complementFinanceInfo(expressOrderContext, computeFacadeResponse);
    }

    /**
     * 判断是否调用通用计费询价接口
     */
    private boolean modifyNeedStandardCompute(ExpressOrderContext expressOrderContext) {
        // 判断是否有未删除的快运零担
        Product product = expressOrderContext.getOrderModel().getProductDelegate().ofProductNo(ProductEnum.KYLD.getCode());
        return product != null && OperateTypeEnum.DELETE != product.getOperateType();
    }
}
