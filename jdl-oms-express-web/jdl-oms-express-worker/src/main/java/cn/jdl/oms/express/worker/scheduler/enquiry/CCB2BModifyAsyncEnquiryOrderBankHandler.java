package cn.jdl.oms.express.worker.scheduler.enquiry;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.bo.CustomerConfig;
import cn.jdl.oms.express.domain.bo.PresortExtend;
import cn.jdl.oms.express.domain.dto.DiscountInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceDetailInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceInfoDto;
import cn.jdl.oms.express.domain.dto.MoneyInfoDto;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.CustomerConfigFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.enquiry.B2COrderBankOrgFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.enquiry.EnquiryFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.enquiry.SurchargeFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.issue.ModifyIssueFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.GetOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.ModifyOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.orderbank.OrderBankFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.org.OrderbankOrgFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.site.SiteInfoFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.customer.BasicTraderResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.CCB2BModifyEnquiryFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.SurchargeFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.SurchargeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.SurchargeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.ModifyIssueFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.ModifyIssueFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderModelCreatorTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.ModifyOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.ModifyOrderFacadeTranslator;
import cn.jdl.oms.express.cc.b2b.infrs.acl.pl.orderbank.CCB2BOrderBankFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeMiddleRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankRedisOp;
import cn.jdl.oms.express.domain.infrs.acl.pl.org.OrderbankOrgFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.org.OrderbankOrgFacadeRquest;
import cn.jdl.oms.express.domain.infrs.acl.pl.org.OrderbankOrgFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.site.SiteInfoFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.site.SiteInfoFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.site.SiteInfoFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.ohs.locals.promise.MakingDispatcherHandler;
import cn.jdl.oms.express.domain.infrs.ohs.locals.redis.IRedisLock;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.ModifyRepositoryMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.OrderBankPdqMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.impl.SchedulerService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.message.SchedulerMessage;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.CurrencyCodeEnum;
import cn.jdl.oms.express.domain.spec.dict.DeliveryTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.EnquiryTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.PickupTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.spec.dict.ServiceProductTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.model.IProduct;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.FlowConstants;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.PDQTopicEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.mdc.MDCTraceConstants;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import cn.jdl.oms.express.worker.message.constant.QILLComputeServiceComputeErrorStatusConstants;
import cn.jdl.oms.express.worker.scheduler.AbstractSchedulerHandler;
import com.jd.paq.core.Message;
import com.jd.paq.plugin.IMessage;
import com.jd.paq.plugin.PDQClientException;
import com.jd.paq.plugin.Result;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static cn.jdl.oms.express.shared.common.constant.EnquiryConstants.CALC_PRICE_ITEM_LIST;

/**
 * @ProjectName：jdl-oms-express
 * @Package： cn.jdl.oms.express.worker.scheduler.enquiry
 * @ClassName: CCB2BModifyAsyncEnquiryOrderBankHandler
 * @Description:
 * @Author： jiangwei279
 * @CreateDate 2023/7/17 21:50
 * @Copyright: Copyright (c)2023 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version：V1.0
 */
public class CCB2BModifyAsyncEnquiryOrderBankHandler extends AbstractSchedulerHandler {

    /**
     * log
     */
    Logger LOGGER = LoggerFactory.getLogger(CCB2BModifyAsyncEnquiryOrderBankHandler.class);

    //查询订单防腐层
    @Resource
    private GetOrderFacade getOrderFacade;

    //修改防腐层
    @Resource
    private ModifyOrderFacade modifyOrderFacade;

    //修改防腐层对象转换器
    @Resource
    private ModifyOrderFacadeTranslator modifyOrderFacadeTranslator;

    /**
     * 订单详情model转换
     */
    @Resource
    private GetOrderModelCreatorTranslator orderModelCreatorTranslator;

    /**
     * 支付机构facade
     */
    @Resource
    private B2COrderBankOrgFacade b2COrderBankOrgFacade;

    /**
     * 询价facade
     */
    @Resource
    private EnquiryFacade enquiryFacade;

    /**
     * 机构信息获取防腐层转换器
     */
    @Resource
    private OrderbankOrgFacadeTranslator orderbankOrgFacadeTranslator;

    @Resource
    private CCB2BModifyEnquiryFacadeTranslator ccB2bModifyEnquiryFacadeTranslator;

    /**
     * 台账防腐层
     */
    @Resource
    private OrderBankFacade orderBankFacade;

    @Resource
    private CCB2BOrderBankFacadeTranslator ccB2BOrderBankFacadeTranslator;

    /**
     * 修改下发服务
     */
    @Resource
    private ModifyIssueFacade modifyIssueFacade;

    @Resource
    private ModifyIssueFacadeTranslator modifyIssueFacadeTranslator;

    /**
     * 下发履约执行层达标逻辑
     */
    @Resource
    private MakingDispatcherHandler makingDispatcherHandler;

    /**
     * 任务调度
     */
    @Resource
    private SchedulerService schedulerService;

    /**
     * 单据台账锁
     */
    @Resource
    private OrderBankRedisOp orderBankRedisOp;

    /**
     * 商家基础信息
     */
    @Resource
    private CustomerConfigFacade customerConfigFacade;

    /**
     * 产品中心-附加费查询
     */
    @Resource
    private SurchargeFacade surchargeFacade;
    /**
     * 产品中心-附加费查询
     */
    @Resource
    private SurchargeFacadeTranslator surchargeFacadeTranslator;

    /**
     * 机构信息获取防腐层
     */
    @Resource
    OrderbankOrgFacade orderbankOrgFacade;

    /**
     * 网点信息查询
     */
    @Resource
    private SiteInfoFacade siteInfoFacade;

    /**
     * 网点信息查询防腐层转换器
     */
    @Resource
    private SiteInfoFacadeTranslator siteInfoFacadeTranslator;

    /**
     * ucc
     */
    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    /**
     * 重试最大次数
     */
    private static final int MAX_RETRY_TIME = 10;

    /**
     * 修改异步询价台账消息处理处理
     *
     * @param iMessage
     * @return
     * @throws PDQClientException
     * <AUTHOR>
    @Override
    public Result execute(IMessage iMessage) throws PDQClientException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        //任务调度执行结果对象根据实际的执行结果定义返回具体的业务结果
        Result result = new Result(Result.SUCCESS);
        CCB2BModifyAsyncEnquiryOrderBankHandler.Lock lock = new CCB2BModifyAsyncEnquiryOrderBankHandler.Lock();
        MDC.put(MDCTraceConstants.TRACEID, String.valueOf(System.nanoTime()));
        try {
            if (iMessage instanceof Message) {
                int retryTime = ((Message) iMessage).getRedriveCount();
                if (retryTime >= MAX_RETRY_TIME) {
                    LOGGER.info("冷链B2B修改异步询价台账消息处理任务调度【{}】,重试次数超过{}次,暂停重试", iMessage.getTopic(), MAX_RETRY_TIME);
                    result.setCode(Result.INVALID_REQUEST);
                    return result;
                }
                String iMessageContent = iMessage.getMessageBody();
                PDQTopicEnum pdqTopicEnum = PDQTopicEnum.getByTopic(iMessage.getTopic());
                if (null == pdqTopicEnum) {
                    LOGGER.info("冷链B2B修改异步询价台账消息处理任务调度【{}】", iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }
                // 重试消息体
                SchedulerMessage schedulerMessage = JSONUtils.jsonToBean(
                        iMessageContent, SchedulerMessage.class);
                if (null == schedulerMessage) {
                    LOGGER.info("冷链B2B修改异步询价台账消息处理任务调度【{}】", iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }
                OrderBankPdqMessageDto messageDto = JSONUtils.jsonToBean(schedulerMessage.getDtoJson(),
                        OrderBankPdqMessageDto.class);
                if (null == messageDto) {
                    LOGGER.info("冷链B2B修改异步询价台账消息处理任务调度【{}】", iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }
                if (!this.asyncEnquiryOrderBankHandle(messageDto, lock)) {
                    LOGGER.info("冷链B2B修改异步询价台账消息处理任务调度【{}】", iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }
            }
        } catch (Exception e) {
            if(expressUccConfigCenter.isStopRetryQlLLComputeServiceComputeSwitch() && e instanceof BusinessDomainException &&
                    (((BusinessDomainException) e).subMessage().startsWith(QILLComputeServiceComputeErrorStatusConstants.MISSING_CLIENT_PRICE_QUOTATION) ||
                            ((BusinessDomainException) e).subMessage().startsWith(QILLComputeServiceComputeErrorStatusConstants.MISSING_ITEM_PRICE_QUOTATION))) {
                LOGGER.info("冷链B2B异步询价台账消息处理任务调度【{}】执行异常, 下游数据缺失， 不必重试", iMessage.getTopic(), e);
                result.setCode(Result.INVALID_REQUEST);
            } else {
                LOGGER.info("冷链B2B异步询价台账消息处理任务调度【{}】执行异常", iMessage.getTopic(), e);
                result.setCode(Result.SYSTEMERROR);
            }
            result.setReason(e.getMessage());
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
            if (lock.getRedisLock() != null) {
                lock.getRedisLock().unlock();
            }
            MDC.remove(MDCTraceConstants.TRACEID);
        }
        return result;
    }

    public boolean asyncEnquiryOrderBankHandle(OrderBankPdqMessageDto messageDto, CCB2BModifyAsyncEnquiryOrderBankHandler.Lock lock) throws ParseException {
        if (messageDto == null || StringUtils.isBlank(messageDto.getOrderNo())) {
            return true;
        }

        LOGGER.info("冷链B2B修改异步询价开始：orderNo:{}", messageDto.getOrderNo());
        //获取询价台账锁，获取不到锁重试. 需要做对应的释放功能
        IRedisLock redisLock = orderBankRedisOp.getLock(messageDto.getRequestProfile(), messageDto.getBusinessIdentity(), messageDto.getOrderNo());
        if (!redisLock.tryLock()) {
            LOGGER.info("冷链B2B修改订单获取台账锁失败，需要重试");
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ASYN_ENQUIRY_ORDER_BANK_FAIL, "订单orderNo:" + messageDto.getOrderNo() + "获取台账锁失败，需要重试");
            return false;
        }
        lock.setRedisLock(redisLock);

        //获取订单详情
        GetOrderFacadeResponse orderFacadeResponse = toGetOrderFacadeResponse(messageDto.getRequestProfile(), messageDto.getOrderNo());
        if (orderFacadeResponse == null) {
            LOGGER.error("冷链B2B修改异步询价台账消息处理查询订单为空");
            /*未获取到订单详情信息需继续重试*/
            return false;
        }
        //将订单详情转换成model
        ExpressOrderContext orderContext = toExpressOrderContext(messageDto, orderFacadeResponse);
        //订单状态检查. 已取消的订单直接忽略
        ExpressOrderModel orderModel = orderContext.getOrderModel();
        if (OrderStatusEnum.CANCELED == orderModel.getOrderStatus().getOrderStatus()) {
            LOGGER.info("订单状态为已取消，忽略这个消息");
            return true;
        }
        if (orderModel.getFinance().getBillingVolume() == null || orderModel.getFinance().getBillingVolume().getValue() == null
                || orderModel.getFinance().getBillingWeight() == null || orderModel.getFinance().getBillingWeight().getValue() == null) {
            LOGGER.info("订单计费体积或重量为空，需要重试");
            return false;
        }
        //判断台账是否初始化
        boolean init = orderBankRedisOp.haveInit(messageDto.getRequestProfile(), messageDto.getBusinessIdentity(), messageDto.getOrderNo(), true);
        if (!init) {
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ASYN_ENQUIRY_ORDER_BANK_FAIL, "订单orderNo:" + orderModel.orderNo() + "尚未初始化台账，需要重试");
            LOGGER.info("订单尚未初始化台账，需要重试");
            return false;
        }

        //已支付的不操作台账
        if (PaymentStatusEnum.COMPLETE_PAYMENT == orderModel.getFinance().getPaymentStatus()) {
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ASYN_ENQUIRY_ORDER_BANK_FAIL, "已支付的不操作台账,orderNo:" + orderModel.orderNo());
            return true;
        }

        //结算方式是月结, 且无cod, 不写账
        if (SettlementTypeEnum.MONTHLY_PAYMENT == orderModel.getFinance().getSettlementType()
                && orderModel.getProductDelegate().getCodProducts().isEmpty()) {
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ASYN_ENQUIRY_ORDER_BANK_FAIL, "结算方式是月结, 且无cod的不操作台账,orderNo:" + orderModel.orderNo());
            return true;
        }

        //当前单询价
        BillingEnquiryFacadeResponse billingEnquiryFacadeResponse = toBillingEnquiryFacadeResponse(orderContext);
        // key:订单号  value:计费信息
        Map<String, BillingEnquiryFacadeResponse> billMap = new HashMap<>();
        billMap.put(orderModel.orderNo(), billingEnquiryFacadeResponse);
        // 更新补全财务信息
        complementBillingResult(orderContext, billMap, billingEnquiryFacadeResponse);
        LOGGER.info("冷链B2B修改异步询价补全计费明细信息结束.billing: {}", JSONUtils.beanToJSONDefault(orderModel.getFinance()));

        //获取机构信息
        if (orderContext.getCustomerConfig() == null) {
            String accountNo = orderContext.getOrderModel().getCustomer().getAccountNo();
            if (StringUtils.isNotBlank(accountNo)) {
                BasicTraderResponse basicTraderResponse = customerConfigFacade.getCustomerConfig(accountNo);
                CustomerConfig customerConfig = new CustomerConfig();
                customerConfig.setTraderSign(basicTraderResponse.getTraderSign());
                customerConfig.setCustomerId(basicTraderResponse.getCustomerId());
                customerConfig.setCustomerName(basicTraderResponse.getCustomerName());
                customerConfig.setSignedCompany(basicTraderResponse.getSignedCompany());
                customerConfig.setSignedOrg(basicTraderResponse.getSignedOrg());
                orderContext.setCustomerConfig(customerConfig);
            }
        }
        OrderbankOrgFacadeRquest orderbankOrgFacadeRquest = orderbankOrgFacadeTranslator.toOrderbankOrgFacadeRquest(orderContext);
        OrderbankOrgFacadeResponse orderBankOrg = getOrderBankOrg(orderContext, orderbankOrgFacadeRquest);
        if (orderBankOrg == null) {
            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.ORG_QUERY_FAIL).withCustom("获取机构信息失败");
        }
        orderContext.getOrderModel().complement().complementFinanceCollectionOrg(this, parseCollectionOrgModelCreator(
                orderBankOrg.getOrgId(), orderBankOrg.getOrgName()));//台账入参转换

        //调整台账
        OrderBankFacadeMiddleRequest middleRequest = ccB2BOrderBankFacadeTranslator.toReverseReAddressOrderBankFacadeRequest(orderContext);
        modifyOrderBank(messageDto, orderContext, middleRequest);
        //下发ofc，仅有财务的
        issueOrder(orderContext);
        //持久化, 以及异常重试，仅有财务的
        try {
            ModifyOrderFacadeRequest facadeRequest = modifyOrderFacadeTranslator.toReverseOrChangeAddressOrderFacadeRequest(orderContext);
            modifyOrderFacade.modifyOrder(orderModel.requestProfile(), facadeRequest);
        } catch (Exception e) {
            LOGGER.error("冷链B2B异步询价持久化防腐层异常", e);
            //触发重试
            produceRetryMq(orderModel.requestProfile(), orderContext);
        }
        return true;
    }

    /**
     * 调用台账接口修改台账
     *
     * @param messageDto
     * @param orderContext
     * @param middleRequest
     */
    private void modifyOrderBank(OrderBankPdqMessageDto messageDto, ExpressOrderContext orderContext, OrderBankFacadeMiddleRequest middleRequest) {
        if (middleRequest.getBMerchantDfModify() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = ccB2BOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setBMerchantModify(middleRequest.getBMerchantDfModify());
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, B2CAsynEnquiryOrderBankHandler.class);
            if (orderBankFacadeResponse != null) {
                ccB2BOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }
        if (middleRequest.getBMerchantCodModify() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = ccB2BOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setBMerchantModify(middleRequest.getBMerchantCodModify());
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, B2CAsynEnquiryOrderBankHandler.class);
            if (orderBankFacadeResponse != null) {
                ccB2BOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }
        if (middleRequest.getBMerchantJfModify() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = ccB2BOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setBMerchantModify(middleRequest.getBMerchantJfModify());
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, B2CAsynEnquiryOrderBankHandler.class);
            if (orderBankFacadeResponse != null) {
                ccB2BOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }
        if (middleRequest.getPosJfYun() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = ccB2BOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setPosJfYun(middleRequest.getPosJfYun());
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, B2CAsynEnquiryOrderBankHandler.class);
            if (orderBankFacadeResponse != null) {
                ccB2BOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }
        if (middleRequest.getPosYun() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = ccB2BOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setPosYun(middleRequest.getPosYun());
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, B2CAsynEnquiryOrderBankHandler.class);
            if (orderBankFacadeResponse != null) {
                ccB2BOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }
    }

    /**
     * 持久化异常处理
     *
     * @param orderContext
     */
    private void produceRetryMq(RequestProfile requestProfile, ExpressOrderContext orderContext) throws ParseException {
        SchedulerMessage schedulerMessage = new SchedulerMessage();
        //持久化消息
        ModifyRepositoryMessageDto modifyRepositoryMessageDto = this.toMessageDto(requestProfile, orderContext);
        schedulerMessage.setDtoJson(JSONUtils.beanToJSONDefault(modifyRepositoryMessageDto));
        schedulerMessage.setDtoClass(ModifyRepositoryMessageDto.class);
        schedulerService.addSchedulerTask(PDQTopicEnum.REVERSE_REPOSITORY_RETRY, schedulerMessage,
                FlowConstants.EXPRESS_ORDER_REVERSE_REPOSITORY_FLOW_CODE);
    }

    /**
     * 下发ofc
     *
     * @param orderContext
     * @throws ParseException
     */
    private void issueOrder(ExpressOrderContext orderContext) throws ParseException {
        Set<String> promiseUnits = makingDispatcherHandler.execute(orderContext);
        Optional.ofNullable(promiseUnits).orElseThrow(() ->
                new BusinessDomainException(UnifiedErrorSpec.BasisOrder.INTERNAL_ERROR).withCustom("下发履约执行层打标无法识别")
        );
        //下发履约层复制给业务身份对象信息
        promiseUnits.forEach(promiseUnit -> orderContext.getOrderModel().getBusinessIdentity().setFulfillmentUnit(promiseUnit));
        //订单领域模型
        promiseUnits.forEach(promiseUnit -> orderContext.getOrderModel().getOrderBusinessIdentity().setFulfillmentUnit(promiseUnit));
        //下发
        ModifyIssueFacadeRequest modifyIssueFacadeRequest = modifyIssueFacadeTranslator.toReverseOrChangeAddressIssueFacadeRequest(
                orderContext);
        modifyIssueFacade.modifyIssue(modifyIssueFacadeRequest, orderContext.getOrderModel().getOrderBusinessIdentity());
    }

    /**
     * 询价
     *
     * @param orderContext
     * @return
     */
    private BillingEnquiryFacadeResponse toBillingEnquiryFacadeResponse(ExpressOrderContext orderContext) {
        BillingEnquiryFacadeRequest billingEnquiryFacadeRequest = ccB2bModifyEnquiryFacadeTranslator.toBillingEnquiryFacadeRequest(orderContext);
        //若复核重量体积有值则优先使用复核重量体积询价
        resetCargoFacadeDto(billingEnquiryFacadeRequest, orderContext.getOrderModel());
//        List<Product> products = getSurcharge(orderContext);
//        if (CollectionUtils.isNotEmpty(products)) {
//            // 获取高峰期附加费
//            billingEnquiryFacadeRequest.getProductFacadeDtoList().addAll(peakPeriodToProductFacadeDto(products));
//        }
        if (orderContext.getOrderModel().getFinance() != null && EnquiryTypeEnum.FIXED_PRICE == orderContext.getOrderModel().getFinance().getEnquiryType()) {
            complementFixedPriceFacadeRequest(orderContext, billingEnquiryFacadeRequest);
            return enquiryFacade.fixedPriceCompute(billingEnquiryFacadeRequest);
        } else {
            Map<String, Object> extendProps = complementEnquiryPriceFacadeRequest(orderContext, billingEnquiryFacadeRequest);
            resetSelfDeliveryOrPickupAddress(extendProps, billingEnquiryFacadeRequest);
            return enquiryFacade.billingEnquiry(billingEnquiryFacadeRequest);
        }
    }

    /**
     * 组装context
     *
     * @param messageDto
     * @param orderFacadeResponse
     * @return
     */
    private ExpressOrderContext toExpressOrderContext(OrderBankPdqMessageDto messageDto, GetOrderFacadeResponse orderFacadeResponse) {
        ExpressOrderModelCreator orderModelCreator = orderModelCreatorTranslator.toExpressOrderModelCreator(orderFacadeResponse);
        ExpressOrderModel orderModel = new ExpressOrderModel(orderModelCreator);
        orderModel.withRequestProfile(messageDto.getRequestProfile());
        ExpressOrderContext orderContext = new ExpressOrderContext(messageDto.getBusinessIdentity(), messageDto.getRequestProfile(), messageDto.getBusinessIdentity().getBusinessScene());
        orderContext.setOrderModel(orderModel);
        orderModel.assignSnapshot(orderModel);
        return orderContext;
    }

    /**
     * 查询订单详情
     *
     * @param profile
     * @param orderNo
     * @return
     */
    private GetOrderFacadeResponse toGetOrderFacadeResponse(RequestProfile profile, String orderNo) {
        GetOrderFacadeRequest facadeRequest = new GetOrderFacadeRequest();
        facadeRequest.setOrderNo(orderNo);
        return getOrderFacade.getOrder(profile, facadeRequest);
    }

    private ExpressOrderModelCreator parseCollectionOrgModelCreator(String orgId, String orgName) {
        ExpressOrderModelCreator creater = new ExpressOrderModelCreator();
        FinanceInfoDto financeInfoDto = new FinanceInfoDto();
        financeInfoDto.setCollectionOrgNo(orgId);
        financeInfoDto.setCollectionOrgName(orgName);
        creater.setFinanceInfo(financeInfoDto);
        return creater;
    }

    /**
     * 原单财务数据根据计费结果重算
     *
     * @param orderContext
     * @param billMap      所有计费
     * @param currentOrder 当前单计费
     */
    public void complementBillingResult(ExpressOrderContext orderContext, Map<String, BillingEnquiryFacadeResponse> billMap, BillingEnquiryFacadeResponse currentOrder) {
        ExpressOrderModelCreator modelCreator = new ExpressOrderModelCreator();
        FinanceInfoDto financeInfoDto = new FinanceInfoDto();

        BigDecimal preMoney = new BigDecimal(0);
        BigDecimal disMoney = new BigDecimal(0);
        CurrencyCodeEnum preCodeEnum = null;
        CurrencyCodeEnum disCodeEnum = null;
        //费用明细 key：CostNo
        Map<String, FinanceDetailInfoDto> detailInfoDtoMap = new HashMap<>();
        StringBuilder financeRemark = new StringBuilder();
        for (Map.Entry<String, BillingEnquiryFacadeResponse> entry : billMap.entrySet()) {
            // 费用总额
            preMoney = preMoney.add(entry.getValue().getFinanceFacadeDto().getPreAmount().getAmount());
            preCodeEnum = entry.getValue().getFinanceFacadeDto().getPreAmount().getCurrencyCode();

            disMoney = disMoney.add(entry.getValue().getFinanceFacadeDto().getDiscountAmount().getAmount());
            disCodeEnum = entry.getValue().getFinanceFacadeDto().getDiscountAmount().getCurrencyCode();
            financeRemark.append(entry.getKey()).append(":").append(entry.getValue().getFinanceFacadeDto().getDiscountAmount().getAmount()).append(",");
            // 费用明细
            for (BillingEnquiryFacadeResponse.FinanceDetailFacadeDto detailFacadeDto : entry.getValue().getFinanceFacadeDto().getFinanceDetailFacadeDtoList()) {
                //费用明细按CostNo分组
                FinanceDetailInfoDto detailInfoDto = detailInfoDtoMap.get(detailFacadeDto.getCostNo());
                if (detailInfoDto == null) {
                    detailInfoDto = new FinanceDetailInfoDto();
                }
                //费用明细整合
                toFinanceDetailInfoDto(detailInfoDto, detailFacadeDto);
                LOGGER.info("费用明细整合出参detailInfoDto：{}", JSONUtils.beanToJSONDefault(detailInfoDto));

                if (CollectionUtils.isNotEmpty(detailFacadeDto.getDiscountInfoFacadeDtos())) {
                    // 费用折扣
                    Map<String, DiscountInfoDto> discountInfoDtoMap = new HashMap<>();
                    for (BillingEnquiryFacadeResponse.DiscountInfoFacadeDto discountInfoFacadeDto : detailFacadeDto.getDiscountInfoFacadeDtos()) {
                        //费用折扣按DiscountNo分组
                        DiscountInfoDto discountInfoDto = discountInfoDtoMap.get(discountInfoFacadeDto.getDiscountNo());
                        if (discountInfoDto == null) {
                            discountInfoDto = new DiscountInfoDto();
                        }
                        //折扣整合
                        toDiscountInfoFacadeDto(discountInfoDto, discountInfoFacadeDto);
                        discountInfoDtoMap.put(discountInfoDto.getDiscountNo(), discountInfoDto);
                    }
                    List<DiscountInfoDto> discountInfoDtoList = new ArrayList<>(discountInfoDtoMap.values());
                    detailInfoDto.setDiscountInfoDtos(discountInfoDtoList);
                }

                //明细remark
                StringBuilder detailRemark;
                if (StringUtils.isNotBlank(detailInfoDto.getRemark())) {
                    detailRemark = new StringBuilder(detailInfoDto.getRemark());
                } else {
                    detailRemark = new StringBuilder();
                }
                detailRemark.append(entry.getKey()).append(":").append(detailInfoDto.getDiscountAmount().getAmount()).append(",");
                detailInfoDto.setRemark(detailRemark.toString());

                detailInfoDto.setExtendProps(new HashMap<>());
                // 价格项明细
                if (detailFacadeDto.getExtendProps() != null && detailFacadeDto.getExtendProps().containsKey(CALC_PRICE_ITEM_LIST)) {
                    detailInfoDto.getExtendProps().put(CALC_PRICE_ITEM_LIST, JSONUtils.beanToJSONDefault(detailFacadeDto.getExtendProps().get(CALC_PRICE_ITEM_LIST)));
                }

                detailInfoDtoMap.put(detailInfoDto.getCostNo(), detailInfoDto);
            }
        }
        LOGGER.info("费用明细整合出参detailInfoDtoMap：{}", JSONUtils.mapToJson(detailInfoDtoMap));
        List<FinanceDetailInfoDto> financeDetailInfoDtoList = new ArrayList<>(detailInfoDtoMap.values());
        //折前金额
        MoneyInfoDto preAmount = new MoneyInfoDto();
        preAmount.setAmount(preMoney);
        preAmount.setCurrencyCode(preCodeEnum);
        financeInfoDto.setPreAmount(preAmount);
        //折后金额
        MoneyInfoDto discountAmount = new MoneyInfoDto();
        discountAmount.setAmount(disMoney);
        discountAmount.setCurrencyCode(disCodeEnum);
        financeInfoDto.setDiscountAmount(discountAmount);
        //计费重量
        financeInfoDto.setBillingWeight(currentOrder.getFinanceFacadeDto().getBillingWeight());
        //计费体积
        financeInfoDto.setBillingVolume(currentOrder.getFinanceFacadeDto().getBillingVolume());
        financeInfoDto.setBillingMode(currentOrder.getFinanceFacadeDto().getBillingMode());
        //费用明细
        financeInfoDto.setFinanceDetailInfos(financeDetailInfoDtoList);
//        toFinanceDetailInfoDto(currentOrder.getFinanceFacadeDto().getFinanceDetailFacadeDtoList(),detailRemark.toString())
        //总优惠金额
        MoneyInfoDto totalDiscountAmount = new MoneyInfoDto();
        totalDiscountAmount.setAmount(preMoney.subtract(disMoney));
        totalDiscountAmount.setCurrencyCode(preCodeEnum);
        financeInfoDto.setTotalDiscountAmount(totalDiscountAmount);
        financeInfoDto.setRemark(financeRemark.toString());
        modelCreator.setFinanceInfo(financeInfoDto);
        orderContext.getOrderModel().complement().complementFinanceInfo(this, modelCreator);
    }


    private void toFinanceDetailInfoDto(FinanceDetailInfoDto detailInfoDto, BillingEnquiryFacadeResponse.FinanceDetailFacadeDto detailFacadeDto) {
        //折前金额
        MoneyInfoDto detailPreAmount = new MoneyInfoDto();
        BigDecimal preNewMoney = detailFacadeDto.getPreAmount() != null ? detailFacadeDto.getPreAmount().getAmount() : null;
        BigDecimal preMoneySum = detailInfoDto.getPreAmount() != null ? detailInfoDto.getPreAmount().getAmount().add(preNewMoney != null ? preNewMoney : BigDecimal.ZERO) : preNewMoney;
        detailPreAmount.setAmount(preMoneySum);
        detailPreAmount.setCurrencyCode(detailFacadeDto.getPreAmount() != null ? detailFacadeDto.getPreAmount().getCurrencyCode() : null);
        detailInfoDto.setPreAmount(detailPreAmount);
        //折后金额
        MoneyInfoDto detailDiscountAmount = new MoneyInfoDto();
        BigDecimal disNewMoney = detailFacadeDto.getDiscountAmount() != null ? detailFacadeDto.getDiscountAmount().getAmount() : null;
        BigDecimal disMoneySum = detailInfoDto.getDiscountAmount() != null ? detailInfoDto.getDiscountAmount().getAmount().add(disNewMoney != null ? disNewMoney : BigDecimal.ZERO) : disNewMoney;
        detailDiscountAmount.setAmount(disMoneySum);
        detailDiscountAmount.setCurrencyCode(detailFacadeDto.getDiscountAmount() != null ? detailFacadeDto.getDiscountAmount().getCurrencyCode() : null);
        detailInfoDto.setDiscountAmount(detailDiscountAmount);
        detailInfoDto.setCostNo(detailFacadeDto.getCostNo());
        detailInfoDto.setCostName(detailFacadeDto.getCostName());
        detailInfoDto.setProductNo(detailFacadeDto.getProductNo());
    }

    private void toDiscountInfoFacadeDto(DiscountInfoDto discountInfoDto, BillingEnquiryFacadeResponse.DiscountInfoFacadeDto discountInfoFacadeDto) {
        discountInfoDto.setDiscountNo(discountInfoFacadeDto.getDiscountNo());
        discountInfoDto.setDiscountType(discountInfoFacadeDto.getDiscountType());

        Money disAmount = new Money();
        BigDecimal discountNewMoney = discountInfoFacadeDto.getDiscountedAmount() != null ? discountInfoFacadeDto.getDiscountedAmount().getAmount() : null;
        BigDecimal discountSumMoney = discountInfoDto.getDiscountedAmount() != null ? discountInfoDto.getDiscountedAmount().getAmount().add(discountNewMoney != null ? discountNewMoney : BigDecimal.ZERO) : discountNewMoney;
        disAmount.setAmount(discountSumMoney);
        disAmount.setCurrency(discountInfoFacadeDto.getDiscountedAmount().getCurrencyCode());
        discountInfoDto.setDiscountedAmount(disAmount);
    }

    /**
     * 获取机构编码
     *
     * @param context
     * @param orderbankOrgFacadeRquest
     * @return
     */
    private OrderbankOrgFacadeResponse getOrderBankOrg(ExpressOrderContext context, OrderbankOrgFacadeRquest orderbankOrgFacadeRquest) {
        OrderbankOrgFacadeResponse orderBankOrg = null;
        if (isLLKBProduct(context)) {
            orderBankOrg = orderbankOrgFacade.getOrderBankOrgCCB2BKB(orderbankOrgFacadeRquest);
        } else {
            orderBankOrg = orderbankOrgFacade.getOrderbankOrg((orderbankOrgFacadeRquest));
        }
        return orderBankOrg;
    }

    /**
     * 是否是冷链卡班产品
     *
     * @param context
     * @return
     */
    private boolean isLLKBProduct(ExpressOrderContext context) {
        IProduct product = context.getOrderModel().getProductDelegate().getMainProduct();
        if (product != null && ProductEnum.LLKB.getCode().equals(product.getProductNo())) {
            return true;
        }
        return false;
    }

    /**
     * 功能: 转换消息, 大报文
     *
     * @param:
     * @return:
     * @throw:
     * @description: todo, 使用jss存储大报文
     * @author: liufarui
     * @date: 2021/6/22 14:36
     */
    private ModifyRepositoryMessageDto toMessageDto(RequestProfile requestProfile, ExpressOrderContext context) throws ParseException {
        ModifyRepositoryMessageDto messageDto = new ModifyRepositoryMessageDto();
        messageDto.setRequestProfile(requestProfile);
        messageDto.setModifyOrderFacadeRequest(modifyOrderFacadeTranslator.toModifyOrderFacadeRequest(context));
        return messageDto;
    }

    /**
     * 用于传输lock，避免在上下文中增加lock信息
     */
    private static class Lock {
        private IRedisLock redisLock;

        IRedisLock getRedisLock() {
            return redisLock;
        }

        void setRedisLock(IRedisLock redisLock) {
            this.redisLock = redisLock;
        }
    }

    /**
     * 补全青龙冷链一口价实时计算请求
     *
     * @param expressOrderContext
     * @param billingEnquiryFacadeRequest
     */
    private void complementFixedPriceFacadeRequest(ExpressOrderContext expressOrderContext, BillingEnquiryFacadeRequest billingEnquiryFacadeRequest) {
        String presortExtendString = expressOrderContext.getOrderModel().getExtendProps().get(OrderConstants.PRESORT_EXTEND);
        Optional.ofNullable(presortExtendString).ifPresent(s -> {
            PresortExtend presortExtend = JSONUtils.jsonToBean(s, PresortExtend.class);
            if (presortExtend.getStartStation() != null && presortExtend.getStartStation().getDmsId() != null) {
                SiteInfoFacadeRequest startSiteInfoFacadeRequest = siteInfoFacadeTranslator.toSiteInfoFacadeRequest(presortExtend.getStartStation().getDmsId());
                SiteInfoFacadeResponse startSiteInfoResponse = getSiteInfoResponse(startSiteInfoFacadeRequest);
                if (startSiteInfoResponse != null) {
                    BillingEnquiryFacadeRequest.SiteInfo startSiteInfo = new BillingEnquiryFacadeRequest.SiteInfo();
                    startSiteInfo.setSiteProvinceId(startSiteInfoResponse.getProvinceNo());
                    startSiteInfo.setSiteCityId(startSiteInfoResponse.getCityNo());
                    startSiteInfo.setSiteCountryId(startSiteInfoResponse.getCountyNo());
                    billingEnquiryFacadeRequest.getExtendProps().put(OrderConstants.startSiteInfo, startSiteInfo);
                }
            }
            if (presortExtend.getEndStation() != null && presortExtend.getEndStation().getDmsId() != null) {
                SiteInfoFacadeRequest endSiteInfoFacadeRequest = siteInfoFacadeTranslator.toSiteInfoFacadeRequest(presortExtend.getEndStation().getDmsId());
                SiteInfoFacadeResponse endSiteInfoResponse = getSiteInfoResponse(endSiteInfoFacadeRequest);
                if (endSiteInfoResponse != null) {
                    BillingEnquiryFacadeRequest.SiteInfo endSiteInfo = new BillingEnquiryFacadeRequest.SiteInfo();
                    endSiteInfo.setSiteProvinceId(endSiteInfoResponse.getProvinceNo());
                    endSiteInfo.setSiteCityId(endSiteInfoResponse.getCityNo());
                    endSiteInfo.setSiteCountryId(endSiteInfoResponse.getCountyNo());
                    billingEnquiryFacadeRequest.getExtendProps().put(OrderConstants.endSiteInfo, endSiteInfo);
                }
            }
        });
    }

    public List<BillingEnquiryFacadeRequest.ProductFacadeDto> peakPeriodToProductFacadeDto(List<Product> products) {
        List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(products)) {
            for (IProduct product : products) {
                BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                productFacadeDto.setProductNo(product.getProductNo());
                productFacadeDto.setProductType(product.getProductType());
                productFacadeDto.setParentNo(product.getParentNo());
                productFacadeDto.setProductAttrs(product.getProductAttrs());
                productFacadeDtos.add(productFacadeDto);
            }
        }
        return productFacadeDtos;
    }

    /**
     * 获取转运中心信息
     * @param siteInfoFacadeRequest
     * @return
     */
    private SiteInfoFacadeResponse getSiteInfoResponse(SiteInfoFacadeRequest siteInfoFacadeRequest) {
        try {
            return siteInfoFacade.getBaseSiteInfoBySiteId(siteInfoFacadeRequest);
        } catch (Throwable e) {
            LOGGER.error("转运中心信息获取异常,不影响主流程:{}", JSONUtils.beanToJSONDefault(siteInfoFacadeRequest), e);
            return null;
        }
    }

    /**
     * 补全标准实时计算请求
     * @param expressOrderContext
     * @param billingEnquiryFacadeRequest
     */
    private Map<String, Object> complementEnquiryPriceFacadeRequest(ExpressOrderContext expressOrderContext, BillingEnquiryFacadeRequest billingEnquiryFacadeRequest) {
        Map<String, Object> extendProps = new HashMap<>();
        String presortExtendString = expressOrderContext.getOrderModel().getExtendProps().get(OrderConstants.PRESORT_EXTEND);
        Optional.ofNullable(presortExtendString).ifPresent(s -> {
            PresortExtend presortExtend = JSONUtils.jsonToBean(s, PresortExtend.class);
            if(presortExtend != null && presortExtend.getStartStation() != null && presortExtend.getStartStation().getDmsId() != null) {
                SiteInfoFacadeRequest startSiteInfoFacadeRequest = siteInfoFacadeTranslator.toSiteInfoFacadeRequest(presortExtend.getStartStation().getDmsId());
                SiteInfoFacadeResponse startSiteInfoResponse = getSiteInfoResponse(startSiteInfoFacadeRequest);
                if (startSiteInfoResponse != null) {
                    BillingEnquiryFacadeRequest.SiteInfo startSiteInfo = new BillingEnquiryFacadeRequest.SiteInfo();
                    startSiteInfo.setSiteProvinceId(startSiteInfoResponse.getProvinceNo());
                    startSiteInfo.setSiteCityId(startSiteInfoResponse.getCityNo());
                    startSiteInfo.setSiteCountryId(startSiteInfoResponse.getCountyNo());
                    extendProps.put(OrderConstants.startSiteInfo, startSiteInfo);
                }
            }
            if(presortExtend != null && presortExtend.getEndStation() != null && presortExtend.getEndStation().getDmsId() != null) {
                SiteInfoFacadeRequest endSiteInfoFacadeRequest = siteInfoFacadeTranslator.toSiteInfoFacadeRequest(presortExtend.getEndStation().getDmsId());
                SiteInfoFacadeResponse endSiteInfoResponse = getSiteInfoResponse(endSiteInfoFacadeRequest);
                if (endSiteInfoResponse != null) {
                    BillingEnquiryFacadeRequest.SiteInfo endSiteInfo = new BillingEnquiryFacadeRequest.SiteInfo();
                    endSiteInfo.setSiteProvinceId(endSiteInfoResponse.getProvinceNo());
                    endSiteInfo.setSiteCityId(endSiteInfoResponse.getCityNo());
                    endSiteInfo.setSiteCountryId(endSiteInfoResponse.getCountyNo());
                    extendProps.put(OrderConstants.endSiteInfo, endSiteInfo);
                }
            }
        });
        return extendProps;
    }

    /**
     * 重置自提自送地址信息
     */
    private void resetSelfDeliveryOrPickupAddress(Map<String, Object> extendProps, BillingEnquiryFacadeRequest billingEnquiryFacadeRequest) {
        LOGGER.info("自提自送场景使用转运中心地址替换接单地址替换前:{},extendProps:{}", JSONUtils.beanToJSONDefault(billingEnquiryFacadeRequest), JSONUtils.beanToJSONDefault(extendProps));
        //自送场景
        if (billingEnquiryFacadeRequest.getShipmentFacadeDto() != null
                && PickupTypeEnum.SELF_DELIVERY == billingEnquiryFacadeRequest.getShipmentFacadeDto().getPickupType()) {
            if (extendProps.get(OrderConstants.startSiteInfo) != null) {
                BillingEnquiryFacadeRequest.SiteInfo startSiteInfo = (BillingEnquiryFacadeRequest.SiteInfo) extendProps.get(OrderConstants.startSiteInfo);
                billingEnquiryFacadeRequest.getConsignorFacadeDto().getAddressFacadeDto().setProvinceNoGis(startSiteInfo.getSiteProvinceId());
                billingEnquiryFacadeRequest.getConsignorFacadeDto().getAddressFacadeDto().setCityNoGis(startSiteInfo.getSiteCityId());
                billingEnquiryFacadeRequest.getConsignorFacadeDto().getAddressFacadeDto().setCountyNoGis(startSiteInfo.getSiteCountryId());
                LOGGER.info("自送场景使用转运中心地址替换接单地址替换后:{}", JSONUtils.beanToJSONDefault(billingEnquiryFacadeRequest));
            }
        }
        //自提场景
        if (billingEnquiryFacadeRequest.getShipmentFacadeDto() != null
                && DeliveryTypeEnum.SELF_PICKUP == billingEnquiryFacadeRequest.getShipmentFacadeDto().getDeliveryType()) {
            if (extendProps.get(OrderConstants.endSiteInfo) != null) {
                BillingEnquiryFacadeRequest.SiteInfo endSiteInfo = (BillingEnquiryFacadeRequest.SiteInfo) extendProps.get(OrderConstants.endSiteInfo);
                billingEnquiryFacadeRequest.getConsigneeFacadeDto().getAddressFacadeDto().setProvinceNoGis(endSiteInfo.getSiteProvinceId());
                billingEnquiryFacadeRequest.getConsigneeFacadeDto().getAddressFacadeDto().setCityNoGis(endSiteInfo.getSiteCityId());
                billingEnquiryFacadeRequest.getConsigneeFacadeDto().getAddressFacadeDto().setCountyNoGis(endSiteInfo.getSiteCountryId());
                LOGGER.info("自提场景使用转运中心地址替换接单地址替换后:{}", JSONUtils.beanToJSONDefault(billingEnquiryFacadeRequest));
            }
        }
    }

    /**
     * 重置cargoFacade若复核重量体积有，则填充
     */
    private void resetCargoFacadeDto(BillingEnquiryFacadeRequest billingEnquiryFacadeRequest, ExpressOrderModel orderModel) {
        if (null == orderModel.getRecheckVolume() || null == orderModel.getRecheckVolume().getValue()
                || null == orderModel.getRecheckWeight() || null == orderModel.getRecheckWeight().getValue()) {
            LOGGER.info("订单复核体积或重量为空，跳过复核重量体积填充逻辑");
            return;
        }

        if (billingEnquiryFacadeRequest.getCargoFacadeDto() != null) {
            billingEnquiryFacadeRequest.getCargoFacadeDto().setTotalCargoWeight(orderModel.getRecheckWeight().getValue());
            billingEnquiryFacadeRequest.getCargoFacadeDto().setTotalCargoVolume(orderModel.getRecheckVolume().getValue());
        }
    }
}
