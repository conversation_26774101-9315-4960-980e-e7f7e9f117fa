package cn.jdl.oms.express.worker.scheduler.enquiry;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.bo.CustomerConfig;
import cn.jdl.oms.express.domain.bo.PresortExtend;
import cn.jdl.oms.express.domain.dto.DiscountInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceDetailInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceInfoDto;
import cn.jdl.oms.express.domain.dto.MoneyInfoDto;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.CustomerConfigFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.enquiry.B2COrderBankOrgFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.enquiry.EnquiryFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.enquiry.SurchargeFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.issue.ModifyIssueFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.GetOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.ModifyOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.orderbank.OrderBankFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.site.SiteInfoFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.customer.BasicTraderResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.CCB2BReverseEnquiryFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.SurchargeFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.ModifyIssueFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.ModifyIssueFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderModelCreatorTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.ModifyOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.ModifyOrderFacadeTranslator;
import cn.jdl.oms.express.cc.b2b.infrs.acl.pl.orderbank.CCB2BOrderBankFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeMiddleRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankRedisOp;
import cn.jdl.oms.express.domain.infrs.acl.pl.org.OrderbankOrgFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.org.OrderbankOrgFacadeRquest;
import cn.jdl.oms.express.domain.infrs.acl.pl.org.OrderbankOrgFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.site.SiteInfoFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.site.SiteInfoFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.site.SiteInfoFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.ohs.locals.promise.MakingDispatcherHandler;
import cn.jdl.oms.express.domain.infrs.ohs.locals.redis.IRedisLock;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.ModifyRepositoryMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.OrderBankPdqMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.impl.SchedulerService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.message.SchedulerMessage;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.CurrencyCodeEnum;
import cn.jdl.oms.express.domain.spec.dict.EnquiryTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.FlowConstants;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.PDQTopicEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.mdc.MDCTraceConstants;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import cn.jdl.oms.express.worker.message.constant.QILLComputeServiceComputeErrorStatusConstants;
import cn.jdl.oms.express.worker.scheduler.AbstractSchedulerHandler;
import com.jd.paq.core.Message;
import com.jd.paq.plugin.IMessage;
import com.jd.paq.plugin.PDQClientException;
import com.jd.paq.plugin.Result;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static cn.jdl.oms.express.shared.common.constant.EnquiryConstants.CALC_PRICE_ITEM_LIST;

/**
 * @ProjectName：jdl-oms-express
 * @Package： cn.jdl.oms.express.worker.scheduler.enquiry
 * @ClassName: CCB2BReverseAsyncEnquiryOrderBankHandler
 * @Description: 冷链B2B异步询价台账消息处理
 * @Author： jiangwei279
 * @CreateDate 2023/7/17 15:19
 * @Copyright: Copyright (c)2023 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version：V1.0
 */
public class CCB2BReverseAsyncEnquiryOrderBankHandler extends AbstractSchedulerHandler {

    /**
     * log
     */
    Logger LOGGER = LoggerFactory.getLogger(CCB2BReverseAsyncEnquiryOrderBankHandler.class);

    //查询订单防腐层
    @Resource
    private GetOrderFacade getOrderFacade;

    //修改防腐层
    @Resource
    private ModifyOrderFacade modifyOrderFacade;

    //修改防腐层对象转换器
    @Resource
    private ModifyOrderFacadeTranslator modifyOrderFacadeTranslator;

    /**
     * 订单详情model转换
     */
    @Resource
    private GetOrderModelCreatorTranslator orderModelCreatorTranslator;

    /**
     * 支付机构facade
     */
    @Resource
    private B2COrderBankOrgFacade b2COrderBankOrgFacade;

    /**
     * 询价facade
     */
    @Resource
    private EnquiryFacade enquiryFacade;

    /**
     * 机构信息获取防腐层转换器
     */
    @Resource
    private OrderbankOrgFacadeTranslator orderbankOrgFacadeTranslator;

    @Resource
    private CCB2BReverseEnquiryFacadeTranslator ccb2BReverseEnquiryFacadeTranslator;

    /**
     * 台账防腐层
     */
    @Resource
    private OrderBankFacade orderBankFacade;

    @Resource
    private CCB2BOrderBankFacadeTranslator ccB2BOrderBankFacadeTranslator;

    /**
     * 修改下发服务
     */
    @Resource
    private ModifyIssueFacade modifyIssueFacade;

    @Resource
    private ModifyIssueFacadeTranslator modifyIssueFacadeTranslator;

    /**
     * 下发履约执行层达标逻辑
     */
    @Resource
    private MakingDispatcherHandler makingDispatcherHandler;

    /**
     * 任务调度
     */
    @Resource
    private SchedulerService schedulerService;

    /**
     * 单据台账锁
     */
    @Resource
    private OrderBankRedisOp orderBankRedisOp;

    /**
     * 商家基础信息
     */
    @Resource
    private CustomerConfigFacade customerConfigFacade;

    /**
     * 产品中心-附加费查询
     */
    @Resource
    private SurchargeFacade surchargeFacade;
    /**
     * 产品中心-附加费查询
     */
    @Resource
    private SurchargeFacadeTranslator surchargeFacadeTranslator;

    /**
     * 网点信息查询
     */
    @Resource
    private SiteInfoFacade siteInfoFacade;

    /**
     * 网点信息查询防腐层转换器
     */
    @Resource
    private SiteInfoFacadeTranslator siteInfoFacadeTranslator;

    /**
     * ucc
     */
    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    /**
     * 重试最大次数
     */
    private static final int MAX_RETRY_TIME = 10;

    /**
     * 异步询价台账消息处理处理
     *
     * @param iMessage
     * @return
     * @throws PDQClientException
     * <AUTHOR>
    @Override
    public Result execute(IMessage iMessage) throws PDQClientException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        //任务调度执行结果对象根据实际的执行结果定义返回具体的业务结果
        Result result = new Result(Result.SUCCESS);
        CCB2BReverseAsyncEnquiryOrderBankHandler.Lock lock = new CCB2BReverseAsyncEnquiryOrderBankHandler.Lock();
        MDC.put(MDCTraceConstants.TRACEID, String.valueOf(System.nanoTime()));
        try {
            if (iMessage instanceof Message) {
                int retryTime = ((Message) iMessage).getRedriveCount();
                if (retryTime >= MAX_RETRY_TIME) {
                    LOGGER.info("冷链B2B异步询价台账消息处理任务调度【{}】,重试次数超过{}次,暂停重试", iMessage.getTopic(), MAX_RETRY_TIME);
                    result.setCode(Result.INVALID_REQUEST);
                    return result;
                }
                String iMessageContent = iMessage.getMessageBody();
                PDQTopicEnum pdqTopicEnum = PDQTopicEnum.getByTopic(iMessage.getTopic());
                if (null == pdqTopicEnum) {
                    LOGGER.info("冷链B2B异步询价台账消息处理任务调度【{}】", iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }
                // 重试消息体
                SchedulerMessage schedulerMessage = JSONUtils.jsonToBean(
                        iMessageContent, SchedulerMessage.class);
                if (null == schedulerMessage) {
                    LOGGER.info("冷链B2B异步询价台账消息处理任务调度【{}】", iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }
                OrderBankPdqMessageDto messageDto = JSONUtils.jsonToBean(schedulerMessage.getDtoJson(),
                        OrderBankPdqMessageDto.class);
                if (null == messageDto) {
                    LOGGER.info("冷链B2B异步询价台账消息处理任务调度【{}】", iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }
                if (!this.asyncEnquiryOrderBankHandle(messageDto, lock)) {
                    LOGGER.info("冷链B2B异步询价台账消息处理任务调度【{}】", iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }
            }
        } catch (Exception e) {
            if(expressUccConfigCenter.isStopRetryQlLLComputeServiceComputeSwitch() && e instanceof BusinessDomainException &&
                    (((BusinessDomainException) e).subMessage().startsWith(QILLComputeServiceComputeErrorStatusConstants.MISSING_CLIENT_PRICE_QUOTATION) ||
                            ((BusinessDomainException) e).subMessage().startsWith(QILLComputeServiceComputeErrorStatusConstants.MISSING_ITEM_PRICE_QUOTATION))) {
                LOGGER.info("冷链B2B异步询价台账消息处理任务调度【{}】执行异常, 下游数据缺失， 不必重试", iMessage.getTopic(), e);
                result.setCode(Result.INVALID_REQUEST);
            } else {
                LOGGER.info("冷链B2B异步询价台账消息处理任务调度【{}】执行异常", iMessage.getTopic(), e);
                result.setCode(Result.SYSTEMERROR);
            }
            result.setReason(e.getMessage());
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
            if (lock.getRedisLock() != null) {
                lock.getRedisLock().unlock();
            }
            MDC.remove(MDCTraceConstants.TRACEID);
        }
        return result;
    }

    public boolean asyncEnquiryOrderBankHandle(OrderBankPdqMessageDto messageDto, CCB2BReverseAsyncEnquiryOrderBankHandler.Lock lock) throws ParseException {
        if (messageDto == null || StringUtils.isBlank(messageDto.getOrderNo())) {
            return true;
        }

        LOGGER.info("冷链B2B异步询价开始：orderNo:{}", messageDto.getOrderNo());
        //获取询价台账锁，获取不到锁重试. 需要做对应的释放功能
        IRedisLock redisLock = orderBankRedisOp.getLock(messageDto.getRequestProfile(), messageDto.getBusinessIdentity(), messageDto.getOrderNo());
        if (!redisLock.tryLock()) {
            LOGGER.info("冷链B2B订单获取台账锁失败，需要重试");
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ASYN_ENQUIRY_ORDER_BANK_FAIL, "订单orderNo:" + messageDto.getOrderNo() + "获取台账锁失败，需要重试");
            return false;
        }
        lock.setRedisLock(redisLock);

        //获取订单详情
        GetOrderFacadeResponse orderFacadeResponse = toGetOrderFacadeResponse(messageDto.getRequestProfile(), messageDto.getOrderNo());
        if (orderFacadeResponse == null) {
            LOGGER.error("冷链B2B异步询价台账消息处理查询订单为空");
            /*未获取到订单详情信息需继续重试*/
            return false;
        }
        //将订单详情转换成model
        ExpressOrderContext orderContext = toExpressOrderContext(messageDto, orderFacadeResponse);
        //订单状态检查. 已取消的订单直接忽略
        ExpressOrderModel orderModel = orderContext.getOrderModel();
        if (OrderStatusEnum.CANCELED == orderModel.getOrderStatus().getOrderStatus()) {
            LOGGER.info("冷链B2B订单状态为已取消，忽略这个消息");
            return true;
        }
        //判断台账是否初始化
        boolean init = orderBankRedisOp.haveInit(messageDto.getRequestProfile(), messageDto.getBusinessIdentity(), messageDto.getOrderNo(), true);
        if (!init) {
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ASYN_ENQUIRY_ORDER_BANK_FAIL, "订单orderNo:" + orderModel.orderNo() + "尚未初始化台账，需要重试");
            LOGGER.info("冷链B2B订单尚未初始化台账，需要重试");
            return false;
        }

        //已支付的不操作台账
        if (PaymentStatusEnum.COMPLETE_PAYMENT == orderModel.getFinance().getPaymentStatus()) {
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ASYN_ENQUIRY_ORDER_BANK_FAIL, "已支付的不操作台账,orderNo:" + orderModel.orderNo());
            return true;
        }

        //结算方式是月结, 且无cod, 不写账
        if (SettlementTypeEnum.MONTHLY_PAYMENT == orderModel.getFinance().getSettlementType()
                && orderModel.getProductDelegate().getCodProducts().isEmpty()) {
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ASYN_ENQUIRY_ORDER_BANK_FAIL, "结算方式是月结, 且无cod的不操作台账,orderNo:" + orderModel.orderNo());
            return true;
        }

        //补全订单信息, 1.原单订单快照
        //原单信息
        GetOrderFacadeResponse refOrderFacadeResponse = toGetOrderFacadeResponse(messageDto.getRequestProfile(), messageDto.getRelateOrderNo());
        if (refOrderFacadeResponse == null) {
            LOGGER.error("冷链B2B异步询价台账消息处理查询原订单为空,原单号：{}", messageDto.getRelateOrderNo());
            return false;
        }
        ExpressOrderContext refOrderContext = toExpressOrderContext(messageDto, refOrderFacadeResponse);
        //将原单的快照信息存入当前单
        orderModel.assignSnapshot(refOrderContext.getOrderModel());

        //当前单询价
        BillingEnquiryFacadeResponse billingEnquiryFacadeResponse = toBillingEnquiryFacadeResponse(orderContext);
        // key:订单号  value:计费信息
        Map<String, BillingEnquiryFacadeResponse> billMap = new HashMap<>();
        billMap.put(orderModel.orderNo(), billingEnquiryFacadeResponse);
        //todo: 逆向逻辑待盘逻辑
        //逆向单询价(逆向次数原则上不受限，有大对象风险)
        if (OrderTypeEnum.RETURN_ORDER == orderModel.getOrderType()) {
            LOGGER.info("冷链B2B逆向单补全计费明细开始");
            // B2C存在多次逆向的场景，每一次逆向，当最新的逆向单是到付现结时，最新逆向单在接单后调计费接口询价，同时获取所有原单信息（取最原始的正向单及最新一次逆向之前的所有逆向单，
            // 且只取结算方式为到付现结的单子）并依次调询价接口重新询价，新单和所有原单均询价成功后，需要加和所有原单运费（所有原单折后金额）并写在最新单运费（最新单折后金额）上落库，
            // 且所有原单的费用明细均需落在新单的费用明细上（相同费用编码的费用需加和）。
            // 所有单据询价完成后，在最新单的订单财务表的“备注”中落所有单（含所有原单及最新逆向单）的每一单的折后金额（形式为“订单号1：折后金额1；订单号2：折后金额2”；…），
            // 在最新单的费用明细表的“备注”中落每一项费用下所有单（含所有原单及最新逆向单）的每一单的该费用的折后金额（形式为“订单号1：折后金额1；订单号2：折后金额2”；…
            GetOrderFacadeResponse subOrderFacadeResponse = refOrderFacadeResponse;
            ExpressOrderContext subOrderContext = null;
            BillingEnquiryFacadeResponse subBillingEnquiryFacadeResponse = null;
            do {
                // 当前单(相对), 补全订单信息
                subOrderContext = toExpressOrderContext(messageDto, subOrderFacadeResponse);
                /*// 1.商家基础资料
                subOrderContext.setCustomerConfig(customerConfig);
                // 2.支付机构id和名称
                orderContext.getOrderModel().complement().complementFinanceCollectionOrg(this, parseCollectionOrgModelCreator(
                        orderbankOrg.getOrgId(), orderbankOrg.getOrgName()));*/
                // 当前单关联单(相对)
                GetOrderFacadeResponse subRefOrderFacadeResponse = null;
                if (OrderTypeEnum.DELIVERY != subOrderContext.getOrderModel().getOrderType()) {
                    subRefOrderFacadeResponse = toGetOrderFacadeResponse(messageDto.getRequestProfile(), subOrderContext.getOrderModel().getRefOrderInfoDelegate().getOriginalOrderNo());
                }
                LOGGER.info("逆向单-中间过程询价, subOrderContext:{}", JSONUtils.beanToJSONDefault(subOrderContext));
                // 询价
                if (SettlementTypeEnum.CASH_ON_DELIVERY.equals(subOrderContext.getOrderModel().getFinance().getSettlementType())) {
                    if (OrderTypeEnum.DELIVERY == subOrderContext.getOrderModel().getOrderType()) {
                        // 回到第一单
                        subBillingEnquiryFacadeResponse = toBillingEnquiryFacadeResponse(subOrderContext);
                    } else {
                        // 3.关联单快照存入当前单(相对)
                        ExpressOrderContext subRefOrderContext = toExpressOrderContext(messageDto, subRefOrderFacadeResponse);
                        subOrderContext.getOrderModel().assignSnapshot(subRefOrderContext.getOrderModel());
                        subBillingEnquiryFacadeResponse = toBillingEnquiryFacadeResponse(subOrderContext);
                    }
                    billMap.put(subOrderContext.getOrderModel().orderNo(), subBillingEnquiryFacadeResponse);
                }
                // 更新当前单(相对)subOrderFacadeResponse
                subOrderFacadeResponse = subRefOrderFacadeResponse;
            } while (OrderTypeEnum.RETURN_ORDER == subOrderContext.getOrderModel().getOrderType());
            LOGGER.info("逆向单完成全量计费获取. {}", JSONUtils.mapToJson(billMap));
        }
        // 更新补全财务信息
        complementBillingResult(orderContext, billMap, billingEnquiryFacadeResponse);
        LOGGER.info("冷链B2B补全计费明细信息结束.billing: {}", JSONUtils.beanToJSONDefault(orderModel.getFinance()));
        //查询商家基础资料
        BasicTraderResponse basicTraderResponse = customerConfigFacade.getCustomerConfig(orderModel.getCustomer().getAccountNo());
        CustomerConfig customerConfig = new CustomerConfig();
        customerConfig.setCustomerId(basicTraderResponse.getCustomerId());
        customerConfig.setCustomerName(basicTraderResponse.getCustomerName());
        orderContext.setCustomerConfig(customerConfig);

        //查询支付机构id和名称
        OrderbankOrgFacadeRquest orderbankOrgFacadeRquest = orderbankOrgFacadeTranslator.toOrderbankOrgFacadeRquest(orderContext);
        OrderbankOrgFacadeResponse orderbankOrg = b2COrderBankOrgFacade.getOrderBankOrg(orderbankOrgFacadeRquest);
        if (orderbankOrg == null) {
            LOGGER.error("获取机构信息失败");
            return false;
        }
        orderModel.complement().complementFinanceCollectionOrg(this, parseCollectionOrgModelCreator(
                orderbankOrg.getOrgId(), orderbankOrg.getOrgName()));
        //台账入参转换
        OrderBankFacadeMiddleRequest middleRequest = ccB2BOrderBankFacadeTranslator.toReverseReAddressOrderBankFacadeRequest(orderContext);
        modifyOrderBank(messageDto, orderContext, middleRequest);
        //下发ofc  仅有财务的
        issueOrder(orderContext);
        //持久化, 以及异常重试
        //仅有财务的
        try {
            ModifyOrderFacadeRequest facadeRequest = modifyOrderFacadeTranslator.toReverseOrChangeAddressOrderFacadeRequest(orderContext);
            modifyOrderFacade.modifyOrder(orderModel.requestProfile(), facadeRequest);
        } catch (Exception e) {
            LOGGER.error("冷链B2B单持久化防腐层异常", e);
            //触发重试
            produceRetryMq(orderModel.requestProfile(), orderContext);
        }
        return true;
    }

    /**
     * 调用台账接口修改台账
     *
     * @param messageDto
     * @param orderContext
     * @param middleRequest
     */
    private void modifyOrderBank(OrderBankPdqMessageDto messageDto, ExpressOrderContext orderContext, OrderBankFacadeMiddleRequest middleRequest) {
        if (middleRequest.getBMerchantDfModify() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = ccB2BOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setBMerchantModify(middleRequest.getBMerchantDfModify());
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, B2CAsynEnquiryOrderBankHandler.class);
            if (orderBankFacadeResponse != null) {
                ccB2BOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }
        if (middleRequest.getBMerchantCodModify() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = ccB2BOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setBMerchantModify(middleRequest.getBMerchantCodModify());
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, B2CAsynEnquiryOrderBankHandler.class);
            if (orderBankFacadeResponse != null) {
                ccB2BOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }
        if (middleRequest.getBMerchantJfModify() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = ccB2BOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setBMerchantModify(middleRequest.getBMerchantJfModify());
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, B2CAsynEnquiryOrderBankHandler.class);
            if (orderBankFacadeResponse != null) {
                ccB2BOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }
        if (middleRequest.getPosJfYun() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = ccB2BOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setPosJfYun(middleRequest.getPosJfYun());
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, B2CAsynEnquiryOrderBankHandler.class);
            if (orderBankFacadeResponse != null) {
                ccB2BOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }
        if (middleRequest.getPosYun() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = ccB2BOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setPosYun(middleRequest.getPosYun());
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, B2CAsynEnquiryOrderBankHandler.class);
            if (orderBankFacadeResponse != null) {
                ccB2BOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }
    }

    /**
     * 持久化异常处理
     *
     * @param orderContext
     */
    private void produceRetryMq(RequestProfile requestProfile, ExpressOrderContext orderContext) throws ParseException {
        SchedulerMessage schedulerMessage = new SchedulerMessage();
        //持久化消息
        ModifyRepositoryMessageDto modifyRepositoryMessageDto = this.toMessageDto(requestProfile, orderContext);
        schedulerMessage.setDtoJson(JSONUtils.beanToJSONDefault(modifyRepositoryMessageDto));
        schedulerMessage.setDtoClass(ModifyRepositoryMessageDto.class);
        schedulerService.addSchedulerTask(PDQTopicEnum.REVERSE_REPOSITORY_RETRY, schedulerMessage,
                FlowConstants.EXPRESS_ORDER_REVERSE_REPOSITORY_FLOW_CODE);
    }

    /**
     * 下发ofc
     *
     * @param orderContext
     * @throws ParseException
     */
    private void issueOrder(ExpressOrderContext orderContext) throws ParseException {
        Set<String> promiseUnits = makingDispatcherHandler.execute(orderContext);
        Optional.ofNullable(promiseUnits).orElseThrow(() ->
                new BusinessDomainException(UnifiedErrorSpec.BasisOrder.INTERNAL_ERROR).withCustom("下发履约执行层打标无法识别")
        );
        //下发履约层复制给业务身份对象信息
        promiseUnits.forEach(promiseUnit -> orderContext.getOrderModel().getBusinessIdentity().setFulfillmentUnit(promiseUnit));
        //订单领域模型
        promiseUnits.forEach(promiseUnit -> orderContext.getOrderModel().getOrderBusinessIdentity().setFulfillmentUnit(promiseUnit));
        //下发
        ModifyIssueFacadeRequest modifyIssueFacadeRequest = modifyIssueFacadeTranslator.toReverseOrChangeAddressIssueFacadeRequest(
                orderContext);
        modifyIssueFacade.modifyIssue(modifyIssueFacadeRequest, orderContext.getOrderModel().getOrderBusinessIdentity());
    }

    /**
     * 询价，逆向单原单询价时，需要传入逆向单新单支付信息
     *
     * @param orderContext      原单
     * @return
     */
    private BillingEnquiryFacadeResponse toBillingEnquiryFacadeResponse(ExpressOrderContext orderContext) {
        BillingEnquiryFacadeRequest billingEnquiryFacadeRequest = ccb2BReverseEnquiryFacadeTranslator.toBillingEnquiryFacadeRequest(orderContext);
        if(orderContext.getOrderModel().getFinance() != null && EnquiryTypeEnum.FIXED_PRICE == orderContext.getOrderModel().getFinance().getEnquiryType()) {
            complementFixedPriceFacadeRequest(orderContext, billingEnquiryFacadeRequest);
            return enquiryFacade.fixedPriceCompute(billingEnquiryFacadeRequest);
        } else {
            return enquiryFacade.billingEnquiry(billingEnquiryFacadeRequest);
        }
    }

    /**
     * 组装context
     *
     * @param messageDto
     * @param orderFacadeResponse
     * @return
     */
    private ExpressOrderContext toExpressOrderContext(OrderBankPdqMessageDto messageDto, GetOrderFacadeResponse orderFacadeResponse) {
        ExpressOrderModelCreator orderModelCreator = orderModelCreatorTranslator.toExpressOrderModelCreator(orderFacadeResponse);
        ExpressOrderModel orderModel = new ExpressOrderModel(orderModelCreator);
        orderModel.withRequestProfile(messageDto.getRequestProfile());
        ExpressOrderContext orderContext = new ExpressOrderContext(messageDto.getBusinessIdentity(), messageDto.getRequestProfile(), messageDto.getBusinessIdentity().getBusinessScene());
        orderContext.setOrderModel(orderModel);
        return orderContext;
    }

    /**
     * 查询订单详情
     *
     * @param profile
     * @param orderNo
     * @return
     */
    private GetOrderFacadeResponse toGetOrderFacadeResponse(RequestProfile profile, String orderNo) {
        GetOrderFacadeRequest facadeRequest = new GetOrderFacadeRequest();
        facadeRequest.setOrderNo(orderNo);
        return getOrderFacade.getOrder(profile, facadeRequest);
    }

    private ExpressOrderModelCreator parseCollectionOrgModelCreator(String orgId, String orgName) {
        ExpressOrderModelCreator creater = new ExpressOrderModelCreator();
        FinanceInfoDto financeInfoDto = new FinanceInfoDto();
        financeInfoDto.setCollectionOrgNo(orgId);
        financeInfoDto.setCollectionOrgName(orgName);
        creater.setFinanceInfo(financeInfoDto);
        return creater;
    }

    /**
     * 原单财务数据根据计费结果重算
     *
     * @param orderContext
     * @param billMap      所有计费
     * @param currentOrder 当前单计费
     */
    public void complementBillingResult(ExpressOrderContext orderContext, Map<String, BillingEnquiryFacadeResponse> billMap, BillingEnquiryFacadeResponse currentOrder) {
        ExpressOrderModelCreator modelCreator = new ExpressOrderModelCreator();
        FinanceInfoDto financeInfoDto = new FinanceInfoDto();

        BigDecimal preMoney = new BigDecimal(0);
        BigDecimal disMoney = new BigDecimal(0);
        CurrencyCodeEnum preCodeEnum = null;
        CurrencyCodeEnum disCodeEnum = null;
        //费用明细 key：CostNo
        Map<String, FinanceDetailInfoDto> detailInfoDtoMap = new HashMap<>();
        StringBuilder financeRemark = new StringBuilder();
        for (Map.Entry<String, BillingEnquiryFacadeResponse> entry : billMap.entrySet()) {
            // 费用总额
            preMoney = preMoney.add(entry.getValue().getFinanceFacadeDto().getPreAmount().getAmount());
            preCodeEnum = entry.getValue().getFinanceFacadeDto().getPreAmount().getCurrencyCode();

            disMoney = disMoney.add(entry.getValue().getFinanceFacadeDto().getDiscountAmount().getAmount());
            disCodeEnum = entry.getValue().getFinanceFacadeDto().getDiscountAmount().getCurrencyCode();
            financeRemark.append(entry.getKey()).append(":").append(entry.getValue().getFinanceFacadeDto().getDiscountAmount().getAmount()).append(",");
            // 费用明细
            for (BillingEnquiryFacadeResponse.FinanceDetailFacadeDto detailFacadeDto : entry.getValue().getFinanceFacadeDto().getFinanceDetailFacadeDtoList()) {
                //费用明细按CostNo分组
                FinanceDetailInfoDto detailInfoDto = detailInfoDtoMap.get(detailFacadeDto.getCostNo());
                if (detailInfoDto == null) {
                    detailInfoDto = new FinanceDetailInfoDto();
                }
                //费用明细整合
                toFinanceDetailInfoDto(detailInfoDto, detailFacadeDto);
                LOGGER.info("费用明细整合出参detailInfoDto：{}", JSONUtils.beanToJSONDefault(detailInfoDto));

                if (CollectionUtils.isNotEmpty(detailFacadeDto.getDiscountInfoFacadeDtos())) {
                    // 费用折扣
                    Map<String, DiscountInfoDto> discountInfoDtoMap = new HashMap<>();
                    for (BillingEnquiryFacadeResponse.DiscountInfoFacadeDto discountInfoFacadeDto : detailFacadeDto.getDiscountInfoFacadeDtos()) {
                        //费用折扣按DiscountNo分组
                        DiscountInfoDto discountInfoDto = discountInfoDtoMap.get(discountInfoFacadeDto.getDiscountNo());
                        if (discountInfoDto == null) {
                            discountInfoDto = new DiscountInfoDto();
                        }
                        //折扣整合
                        toDiscountInfoFacadeDto(discountInfoDto, discountInfoFacadeDto);
                        discountInfoDtoMap.put(discountInfoDto.getDiscountNo(), discountInfoDto);
                    }
                    List<DiscountInfoDto> discountInfoDtoList = new ArrayList<>(discountInfoDtoMap.values());
                    detailInfoDto.setDiscountInfoDtos(discountInfoDtoList);
                }

                //明细remark
                StringBuilder detailRemark;
                if (StringUtils.isNotBlank(detailInfoDto.getRemark())) {
                    detailRemark = new StringBuilder(detailInfoDto.getRemark());
                } else {
                    detailRemark = new StringBuilder();
                }
                detailRemark.append(entry.getKey()).append(":").append(detailInfoDto.getDiscountAmount().getAmount()).append(",");
                detailInfoDto.setRemark(detailRemark.toString());

                detailInfoDto.setExtendProps(new HashMap<>());
                // 价格项明细
                if (detailFacadeDto.getExtendProps() != null && detailFacadeDto.getExtendProps().containsKey(CALC_PRICE_ITEM_LIST)) {
                    detailInfoDto.getExtendProps().put(CALC_PRICE_ITEM_LIST, JSONUtils.beanToJSONDefault(detailFacadeDto.getExtendProps().get(CALC_PRICE_ITEM_LIST)));
                }

                detailInfoDtoMap.put(detailInfoDto.getCostNo(), detailInfoDto);
            }
        }
        LOGGER.info("费用明细整合出参detailInfoDtoMap：{}", JSONUtils.mapToJson(detailInfoDtoMap));
        List<FinanceDetailInfoDto> financeDetailInfoDtoList = new ArrayList<>(detailInfoDtoMap.values());
        //折前金额
        MoneyInfoDto preAmount = new MoneyInfoDto();
        preAmount.setAmount(preMoney);
        preAmount.setCurrencyCode(preCodeEnum);
        financeInfoDto.setPreAmount(preAmount);
        //折后金额
        MoneyInfoDto discountAmount = new MoneyInfoDto();
        discountAmount.setAmount(disMoney);
        discountAmount.setCurrencyCode(disCodeEnum);
        financeInfoDto.setDiscountAmount(discountAmount);
        //计费重量
        financeInfoDto.setBillingWeight(currentOrder.getFinanceFacadeDto().getBillingWeight());
        //计费体积
        financeInfoDto.setBillingVolume(currentOrder.getFinanceFacadeDto().getBillingVolume());
        financeInfoDto.setBillingMode(currentOrder.getFinanceFacadeDto().getBillingMode());
        //费用明细
        financeInfoDto.setFinanceDetailInfos(financeDetailInfoDtoList);
//        toFinanceDetailInfoDto(currentOrder.getFinanceFacadeDto().getFinanceDetailFacadeDtoList(),detailRemark.toString())
        //总优惠金额
        MoneyInfoDto totalDiscountAmount = new MoneyInfoDto();
        totalDiscountAmount.setAmount(preMoney.subtract(disMoney));
        totalDiscountAmount.setCurrencyCode(preCodeEnum);
        financeInfoDto.setTotalDiscountAmount(totalDiscountAmount);
        financeInfoDto.setRemark(financeRemark.toString());
        modelCreator.setFinanceInfo(financeInfoDto);
        orderContext.getOrderModel().complement().complementFinanceInfo(this, modelCreator);
    }

    private List<FinanceDetailInfoDto> toFinanceDetailInfoDto(List<BillingEnquiryFacadeResponse.FinanceDetailFacadeDto> detailFacadeDtos, String remark) {
        List<FinanceDetailInfoDto> financeDetailInfoDtos = new ArrayList<>();
        for (BillingEnquiryFacadeResponse.FinanceDetailFacadeDto detailFacadeDto : detailFacadeDtos) {
            FinanceDetailInfoDto financeDetailInfoDto = new FinanceDetailInfoDto();
            financeDetailInfoDto.setPointsInfoDto(detailFacadeDto.getPointsInfoDto());
            financeDetailInfoDto.setCostName(detailFacadeDto.getCostName());
            financeDetailInfoDto.setCostNo(detailFacadeDto.getCostNo());
            //折后金额
            MoneyInfoDto detailDiscountAmount = new MoneyInfoDto();
            detailDiscountAmount.setAmount(detailFacadeDto.getDiscountAmount() != null ? detailFacadeDto.getDiscountAmount().getAmount() : null);
            detailDiscountAmount.setCurrencyCode(detailFacadeDto.getDiscountAmount() != null ? detailFacadeDto.getDiscountAmount().getCurrencyCode() : null);
            financeDetailInfoDto.setDiscountAmount(detailDiscountAmount);
            //折前金额
            MoneyInfoDto detailPreAmount = new MoneyInfoDto();
            detailPreAmount.setAmount(detailFacadeDto.getPreAmount() != null ? detailFacadeDto.getPreAmount().getAmount() : null);
            detailPreAmount.setCurrencyCode(detailFacadeDto.getPreAmount() != null ? detailFacadeDto.getPreAmount().getCurrencyCode() : null);
            financeDetailInfoDto.setPreAmount(detailPreAmount);
            financeDetailInfoDto.setDiscountInfoDtos(toDiscountInfoFacadeDto(detailFacadeDto.getDiscountInfoFacadeDtos()));
            financeDetailInfoDto.setProductNo(detailFacadeDto.getProductNo());
            financeDetailInfoDto.setProductName(detailFacadeDto.getProductName());
            financeDetailInfoDto.setRemark(remark);
            financeDetailInfoDtos.add(financeDetailInfoDto);
        }
        return financeDetailInfoDtos;
    }

    private List<DiscountInfoDto> toDiscountInfoFacadeDto(List<BillingEnquiryFacadeResponse.DiscountInfoFacadeDto> discountInfoFacadeDto) {
        List<DiscountInfoDto> discountInfoDtos = new ArrayList<>();
        for (BillingEnquiryFacadeResponse.DiscountInfoFacadeDto facadeDto : discountInfoFacadeDto) {
            DiscountInfoDto discountInfoDto = new DiscountInfoDto();
            discountInfoDto.setDiscountNo(facadeDto.getDiscountNo());
            discountInfoDto.setDiscountType(facadeDto.getDiscountType());
            Money disAmount = new Money();
            disAmount.setAmount(facadeDto.getDiscountedAmount() != null ? facadeDto.getDiscountedAmount().getAmount() : null);
            disAmount.setCurrency(facadeDto.getDiscountedAmount() != null ? facadeDto.getDiscountedAmount().getCurrencyCode() : null);
            discountInfoDto.setDiscountedAmount(disAmount);
            discountInfoDtos.add(discountInfoDto);
        }
        return discountInfoDtos;
    }

    private void toFinanceDetailInfoDto(FinanceDetailInfoDto detailInfoDto, BillingEnquiryFacadeResponse.FinanceDetailFacadeDto detailFacadeDto) {
        //折前金额
        MoneyInfoDto detailPreAmount = new MoneyInfoDto();
        BigDecimal preNewMoney = detailFacadeDto.getPreAmount() != null ? detailFacadeDto.getPreAmount().getAmount() : null;
        BigDecimal preMoneySum = detailInfoDto.getPreAmount() != null ? detailInfoDto.getPreAmount().getAmount().add(preNewMoney != null ? preNewMoney : BigDecimal.ZERO) : preNewMoney;
        detailPreAmount.setAmount(preMoneySum);
        detailPreAmount.setCurrencyCode(detailFacadeDto.getPreAmount() != null ? detailFacadeDto.getPreAmount().getCurrencyCode() : null);
        detailInfoDto.setPreAmount(detailPreAmount);
        //折后金额
        MoneyInfoDto detailDiscountAmount = new MoneyInfoDto();
        BigDecimal disNewMoney = detailFacadeDto.getDiscountAmount() != null ? detailFacadeDto.getDiscountAmount().getAmount() : null;
        BigDecimal disMoneySum = detailInfoDto.getDiscountAmount() != null ? detailInfoDto.getDiscountAmount().getAmount().add(disNewMoney != null ? disNewMoney : BigDecimal.ZERO) : disNewMoney;
        detailDiscountAmount.setAmount(disMoneySum);
        detailDiscountAmount.setCurrencyCode(detailFacadeDto.getDiscountAmount() != null ? detailFacadeDto.getDiscountAmount().getCurrencyCode() : null);
        detailInfoDto.setDiscountAmount(detailDiscountAmount);
        detailInfoDto.setCostNo(detailFacadeDto.getCostNo());
        detailInfoDto.setCostName(detailFacadeDto.getCostName());
        detailInfoDto.setProductNo(detailFacadeDto.getProductNo());
    }

    private void toDiscountInfoFacadeDto(DiscountInfoDto discountInfoDto, BillingEnquiryFacadeResponse.DiscountInfoFacadeDto discountInfoFacadeDto) {
        discountInfoDto.setDiscountNo(discountInfoFacadeDto.getDiscountNo());
        discountInfoDto.setDiscountType(discountInfoFacadeDto.getDiscountType());

        Money disAmount = new Money();
        BigDecimal discountNewMoney = discountInfoFacadeDto.getDiscountedAmount() != null ? discountInfoFacadeDto.getDiscountedAmount().getAmount() : null;
        BigDecimal discountSumMoney = discountInfoDto.getDiscountedAmount() != null ? discountInfoDto.getDiscountedAmount().getAmount().add(discountNewMoney != null ? discountNewMoney : BigDecimal.ZERO) : discountNewMoney;
        disAmount.setAmount(discountSumMoney);
        disAmount.setCurrency(discountInfoFacadeDto.getDiscountedAmount().getCurrencyCode());
        discountInfoDto.setDiscountedAmount(disAmount);
    }

    /**
     * 功能: 转换消息, 大报文
     *
     * @param:
     * @return:
     * @throw:
     * @description: todo, 使用jss存储大报文
     * @author: liufarui
     * @date: 2021/6/22 14:36
     */
    private ModifyRepositoryMessageDto toMessageDto(RequestProfile requestProfile, ExpressOrderContext context){
        ModifyRepositoryMessageDto messageDto = new ModifyRepositoryMessageDto();
        messageDto.setRequestProfile(requestProfile);
        messageDto.setModifyOrderFacadeRequest(modifyOrderFacadeTranslator.toModifyOrderFacadeRequest(context));
        return messageDto;
    }

    /**
     * 用于传输lock，避免在上下文中增加lock信息
     */
    private static class Lock {
        private IRedisLock redisLock;

        IRedisLock getRedisLock() {
            return redisLock;
        }

        void setRedisLock(IRedisLock redisLock) {
            this.redisLock = redisLock;
        }
    }

    /**
     * 补全青龙冷链一口价实时计算请求
     * @param expressOrderContext
     * @param billingEnquiryFacadeRequest
     */
    private void complementFixedPriceFacadeRequest(ExpressOrderContext expressOrderContext, BillingEnquiryFacadeRequest billingEnquiryFacadeRequest) {
        String presortExtendString = expressOrderContext.getOrderModel().getExtendProps().get(OrderConstants.PRESORT_EXTEND);
        Optional.ofNullable(presortExtendString).ifPresent(s -> {
            PresortExtend presortExtend = JSONUtils.jsonToBean(s, PresortExtend.class);
            if(presortExtend.getStartStation() != null &&  presortExtend.getStartStation().getDmsId() != null) {
                SiteInfoFacadeRequest startSiteInfoFacadeRequest = siteInfoFacadeTranslator.toSiteInfoFacadeRequest(presortExtend.getStartStation().getDmsId());
                SiteInfoFacadeResponse startSiteInfoResponse = siteInfoFacade.getBaseSiteInfoBySiteId(startSiteInfoFacadeRequest);
                if (startSiteInfoResponse != null) {
                    BillingEnquiryFacadeRequest.SiteInfo startSiteInfo = new BillingEnquiryFacadeRequest.SiteInfo();
                    startSiteInfo.setSiteProvinceId(startSiteInfoResponse.getProvinceNo());
                    startSiteInfo.setSiteCityId(startSiteInfoResponse.getCityNo());
                    startSiteInfo.setSiteCountryId(startSiteInfoResponse.getCountyNo());
                    billingEnquiryFacadeRequest.getExtendProps().put(OrderConstants.startSiteInfo, startSiteInfo);
                }
            }
            if(presortExtend.getEndStation() != null &&  presortExtend.getEndStation().getDmsId() != null) {
                SiteInfoFacadeRequest endSiteInfoFacadeRequest = siteInfoFacadeTranslator.toSiteInfoFacadeRequest(presortExtend.getEndStation().getDmsId());
                SiteInfoFacadeResponse endSiteInfoResponse = siteInfoFacade.getBaseSiteInfoBySiteId(endSiteInfoFacadeRequest);
                if (endSiteInfoResponse != null) {
                    BillingEnquiryFacadeRequest.SiteInfo endSiteInfo = new BillingEnquiryFacadeRequest.SiteInfo();
                    endSiteInfo.setSiteProvinceId(endSiteInfoResponse.getProvinceNo());
                    endSiteInfo.setSiteCityId(endSiteInfoResponse.getCityNo());
                    endSiteInfo.setSiteCountryId(endSiteInfoResponse.getCountyNo());
                    billingEnquiryFacadeRequest.getExtendProps().put(OrderConstants.endSiteInfo, endSiteInfo);
                }
            }
        });
    }
}
