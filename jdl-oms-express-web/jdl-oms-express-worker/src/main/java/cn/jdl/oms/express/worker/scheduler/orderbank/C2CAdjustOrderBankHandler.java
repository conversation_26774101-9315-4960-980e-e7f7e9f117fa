package cn.jdl.oms.express.worker.scheduler.orderbank;

import cn.jdl.oms.express.domain.infrs.acl.facade.orderbank.OrderBankFacade;
import cn.jdl.oms.express.c2c.infrs.acl.pl.orderbank.C2COrderBankFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankRedisOp;
import cn.jdl.oms.express.domain.infrs.ohs.locals.redis.IRedisLock;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.OrderBankAdjustPdqMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.message.SchedulerMessage;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.PDQTopicEnum;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import cn.jdl.oms.express.worker.scheduler.AbstractSchedulerHandler;
import com.jd.paq.core.Message;
import com.jd.paq.plugin.IMessage;
import com.jd.paq.plugin.PDQClientException;
import com.jd.paq.plugin.Result;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;

/**
 * @Package： cn.jdl.oms.express.worker.scheduler.orderbank
 * @ClassName: C2CAdjustOrderBankHandler
 * @Description: 调整台账Handler
 * @Author： zhangqi
 * @CreateDate 2021/4/8 11:55
 * @Copyright: Copyright (c)2021 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version： V1.0
 */
public class C2CAdjustOrderBankHandler extends AbstractSchedulerHandler {
    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(C2CAdjustOrderBankHandler.class);

    /**
     * 台账facade
     */
    @Resource
    private OrderBankFacade orderBankFacade;

    /**
     * 询价台账redisOp
     */
    @Resource
    private OrderBankRedisOp orderBankRedisOp;

    /**
     * 转换器
     */
    @Resource
    private C2COrderBankFacadeTranslator c2cOrderBankFacadeTranslator;

    /**
     * 重试最大次数
     */
    private final static int MAX_RETRY_TIMES = 0;

    /**
     * @param iMessage
     * @return
     * @throws PDQClientException
     */
    @Override
    public Result execute(IMessage iMessage) throws PDQClientException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_WORKER_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        //台账调整任务调度执行结果对象根据实际的执行结果定义返回具体的业务结果
        Result result = new Result(Result.SYSTEMERROR);
        IRedisLock redisLock = null;
        try {
            // 设置消息体为重试消息
            if (iMessage instanceof Message) {
                LOGGER.info("台账调整任务调度【" + iMessage.getTopic() + "】开始执行");
                int retryTime = ((Message) iMessage).getRedriveCount();
                if (retryTime > MAX_RETRY_TIMES) {
                    LOGGER.info("台账调整任务调度【" + iMessage.getTopic() + "】,重试次数超过" + MAX_RETRY_TIMES + "次,暂停重试");
                    result.setCode(Result.INVALID_REQUEST);
                    return result;
                }
                String iMessageContent = iMessage.getMessageBody();
                PDQTopicEnum pdqTopicEnum = PDQTopicEnum.getByTopic(iMessage.getTopic());
                if (null == pdqTopicEnum) {
                    LOGGER.info("台账调整任务调度【" + iMessage.getTopic() + "】,未匹配到任务队列,暂停重试");
                    return result;
                }
                // 重试消息体
                SchedulerMessage schedulerMessage = JSONUtils.jsonToBean(
                        iMessageContent, SchedulerMessage.class);
                if (null == schedulerMessage) {
                    LOGGER.info("台账调整任务调度【" + iMessage.getTopic() + "】,重试消息体不存在,暂停重试");
                    return result;
                }
                OrderBankAdjustPdqMessageDto orderBankAdjustPdqMessage = (OrderBankAdjustPdqMessageDto) JSONUtils.jsonToBean(schedulerMessage.getDtoJson(), schedulerMessage.getDtoClass());
                if (orderBankAdjustPdqMessage == null) {
                    LOGGER.info("台账调整任务调度【" + iMessage.getTopic() + "】,场景业务数据对象不存在,暂停重试");
                    return result;
                }
                LOGGER.info("台账调整任务 topic:【" + iMessage.getTopic()
                        + "】orderNo:【" + orderBankAdjustPdqMessage.getOrderNo()
                        + "】requestProfile:" + JSONUtils.beanToJSONDefault(orderBankAdjustPdqMessage.getRequestProfile()) + "】开始");

                //询价台账是否初始化,未初始化直接失败
                if (!orderBankRedisOp.haveInit(orderBankAdjustPdqMessage.getRequestProfile(), orderBankAdjustPdqMessage.getBusinessIdentity(), orderBankAdjustPdqMessage.getOrderNo(), true)) {
                    LOGGER.info("台账调整任务调度【{" + iMessage.getTopic() + "}】,订单{" + orderBankAdjustPdqMessage.getOrderNo() + "}询价台账未初始化，失败重试");
                    Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_WORKER_ORDER_BANK_EXCEPTION_ALARM_MONITOR
                            , System.currentTimeMillis()
                            , "台账调整：" +
                                    "租户:" + orderBankAdjustPdqMessage.getRequestProfile().getTenantId()
                                    + ","
                                    + "订单号:" + orderBankAdjustPdqMessage.getOrderNo()
                                    + ","
                                    + "台账未初始化");
                    return result;
                }
                redisLock = orderBankRedisOp.getLock(orderBankAdjustPdqMessage.getRequestProfile(), orderBankAdjustPdqMessage.getBusinessIdentity(), orderBankAdjustPdqMessage.getOrderNo());
                //获取询价台账锁
                if (!redisLock.tryLock()) {
                    LOGGER.info("台账调整任务调度【{" + iMessage.getTopic() + "}】,订单{" + orderBankAdjustPdqMessage.getOrderNo() + "}未获取到询价台账锁，失败重试");
                    Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_WORKER_ORDER_BANK_EXCEPTION_ALARM_MONITOR
                            , System.currentTimeMillis()
                            , "台账调整：" +
                                    "租户:" + orderBankAdjustPdqMessage.getRequestProfile().getTenantId()
                                    + ","
                                    + "订单号:" + orderBankAdjustPdqMessage.getOrderNo()
                                    + ","
                                    + "未获取到询价台账锁");
                    return result;
                }

                //pos 到付调整
                if (orderBankAdjustPdqMessage.getPosYun() != null) {
                    OrderBankFacadeRequest facadeRequest = toCommonOrderBankFacadeRequestAdjust(orderBankAdjustPdqMessage.getOrderBankFacadeRequest());
                    facadeRequest.setPosYun(orderBankAdjustPdqMessage.getPosYun());
                    orderBankFacade.saveOrUpdate(facadeRequest, this.getClass());
                }
                //pos 寄付调整
                if (orderBankAdjustPdqMessage.getPosJfYun() != null) {
                    OrderBankFacadeRequest facadeRequest = toCommonOrderBankFacadeRequestAdjust(orderBankAdjustPdqMessage.getOrderBankFacadeRequest());
                    facadeRequest.setPosJfYun(orderBankAdjustPdqMessage.getPosJfYun());
                    orderBankFacade.saveOrUpdate(facadeRequest, this.getClass());
                }
                //B商家修改 cod调整
                if (orderBankAdjustPdqMessage.getBMerchantCodModify() != null) {
                    OrderBankFacadeRequest facadeRequest = toCommonOrderBankFacadeRequestAdjust(orderBankAdjustPdqMessage.getOrderBankFacadeRequest());
                    facadeRequest.setBMerchantModify(orderBankAdjustPdqMessage.getBMerchantCodModify());
                    orderBankFacade.saveOrUpdate(facadeRequest, this.getClass());
                }
                //B商家修改 到付调整
                if (orderBankAdjustPdqMessage.getBMerchantDfModify() != null) {
                    OrderBankFacadeRequest facadeRequest = toCommonOrderBankFacadeRequestAdjust(orderBankAdjustPdqMessage.getOrderBankFacadeRequest());
                    facadeRequest.setBMerchantModify(orderBankAdjustPdqMessage.getBMerchantDfModify());
                    orderBankFacade.saveOrUpdate(facadeRequest, this.getClass());
                }
                //B商家修改 寄付调整
                if (orderBankAdjustPdqMessage.getBMerchantJfModify() != null) {
                    OrderBankFacadeRequest facadeRequest = toCommonOrderBankFacadeRequestAdjust(orderBankAdjustPdqMessage.getOrderBankFacadeRequest());
                    facadeRequest.setBMerchantModify(orderBankAdjustPdqMessage.getBMerchantJfModify());
                    orderBankFacade.saveOrUpdate(facadeRequest, this.getClass());
                }
                //外单调整
                if (orderBankAdjustPdqMessage.getOtsCreate() != null) {
                    OrderBankFacadeRequest facadeRequest = toCommonOrderBankFacadeRequestAdjust(orderBankAdjustPdqMessage.getOrderBankFacadeRequest());
                    facadeRequest.setOtsCreate(orderBankAdjustPdqMessage.getOtsCreate());
                    orderBankFacade.saveOrUpdate(facadeRequest, this.getClass());
                }

                result = new Result(Result.SUCCESS);
                LOGGER.info("台账调整任务 topic:【" + iMessage.getTopic()
                        + "】orderNo:【" + orderBankAdjustPdqMessage.getOrderNo()
                        + "】requestProfile:" + JSONUtils.beanToJSONDefault(orderBankAdjustPdqMessage.getRequestProfile()) + "】完成");
            }
        } catch (Exception e) {
            result.setCode(Result.SYSTEMERROR);
            LOGGER.error("台账调整任务调度执行异常,再次重试", e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
            if (redisLock != null) {
                redisLock.unlock();
            }
        }
        return result;

    }

    /**
     * 台账调整公共参数
     *
     * @param orderBankFacadeRequest
     */
    private OrderBankFacadeRequest toCommonOrderBankFacadeRequestAdjust(OrderBankFacadeRequest orderBankFacadeRequest) {
        //调整不需要的台账操作
        OrderBankFacadeRequest orderBankFacadeRequestClear = new OrderBankFacadeRequest();
        orderBankFacadeRequestClear.setWaybillNo(orderBankFacadeRequest.getWaybillNo());
        orderBankFacadeRequestClear.setOrgId(orderBankFacadeRequest.getOrgId());
        orderBankFacadeRequestClear.setOrgName(orderBankFacadeRequest.getOrgName());
        orderBankFacadeRequestClear.setConsigneeInfo(orderBankFacadeRequest.getConsigneeInfo());
        orderBankFacadeRequestClear.setConsignorInfo(orderBankFacadeRequest.getConsignorInfo());
        orderBankFacadeRequestClear.setUUid(orderBankFacadeRequest.getUUid());
        return orderBankFacadeRequestClear;
    }
}
