package cn.jdl.oms.express.worker.scheduler.invoice;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.infrs.acl.facade.invoice.InvoiceFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.GetOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.orderbank.RetailOrderBankFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.invoice.InvoiceFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.invoice.PushInvoiceFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderModelCreatorTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.QueryOrderBankPayDetailsResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.QueryOrderBankResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.BmsUtils;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.MerchantUtils;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedBusinessIdentityUtil;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.AbstractMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.message.SchedulerMessage;
import cn.jdl.oms.express.domain.infrs.ohs.locals.ump.UmpUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.PDQTopicEnum;
import cn.jdl.oms.express.shared.common.exception.InfrastructureException;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import cn.jdl.oms.express.worker.scheduler.AbstractSchedulerHandler;
import com.jd.bms.settlement.enums.BizTypeEnum;
import com.jd.paq.core.Message;
import com.jd.paq.plugin.IMessage;
import com.jd.paq.plugin.PDQClientException;
import com.jd.paq.plugin.Result;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * 快运发票信息推送handler
 */
public class FreightPushInvoiceInfoHandler extends AbstractSchedulerHandler {
    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(FreightPushInvoiceInfoHandler.class);

    /**
     * 重试最大次数
     */
    private final static int MAX_RETRY_TIMES = 10;

    @Resource
    private UmpUtil umpUtil;

    /**
     * 获取订单详情服务
     */
    @Resource
    private GetOrderFacade getOrderFacade;

    /**
     * 获取订单详情防腐层请求转换
     */
    @Resource
    private GetOrderFacadeTranslator getOrderFacadeTranslator;

    /**
     * 订单详情model转换
     */
    @Resource
    private GetOrderModelCreatorTranslator orderModelCreatorTranslator;

    /**
     * 开票服务
     */
    @Resource
    private InvoiceFacade invoiceFacade;

    /**
     * 开票服务防腐层转换器
     */
    @Resource
    InvoiceFacadeTranslator invoiceFacadeTranslator;

    /**
     * 开票服务业务异常
     */
    private static final String BUSINESS_ERR = "2";

    /**
     * 零售外单台账查询防腐层
     */
    @Resource
    private RetailOrderBankFacade retailOrderBankFacade;

    /**
     * 发票信息推送pdq处理执行入口
     *
     * @param iMessage
     * @return
     * @throws PDQClientException
     */
    @Override
    public Result execute(IMessage iMessage) throws PDQClientException {
        CallerInfo callerInfo = umpUtil.registerInfo(this.getClass().getName() + ".execute");
        Result result = new Result(Result.SYSTEMERROR);
        try {
            // 设置消息体为重试消息
            if (iMessage instanceof Message) {
                LOGGER.info("开票信息推送任务调度{}开始执行", iMessage.getTopic());

                // 重试策略为5min间隔，产品要求重试(N=3)天，共重试次数(7 * 24 * 60 / 30 = 336 次)
                if (((Message) iMessage).getRedriveCount() >= MAX_RETRY_TIMES) {
                    LOGGER.info("开票信息推送任务调度{},重试次数超过{}次,暂停重试", iMessage.getTopic(), MAX_RETRY_TIMES);
                    Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_PUSH_INVOICE_FAIL, "订单开票信息推送达最大次数！消息信息:" + JSONUtils.beanToJSONDefault(iMessage));
                    result.setCode(Result.INVALID_REQUEST);
                    return result;
                }
                String iMessageContent = iMessage.getMessageBody();
                PDQTopicEnum pdqTopicEnum = PDQTopicEnum.getByTopic(iMessage.getTopic());
                if (null == pdqTopicEnum) {
                    LOGGER.info("开票信息推送任务调度{},未匹配到任务队列,暂停重试", iMessage.getTopic());
                    result.setCode(Result.INVALID_REQUEST);
                    return result;
                }
                // 重试消息体
                SchedulerMessage schedulerMessage = JSONUtils.jsonToBean(iMessageContent, SchedulerMessage.class);
                if (null == schedulerMessage) {
                    LOGGER.info("开票信息推送任务调度{},重试消息体不存在,暂停重试", iMessage.getTopic());
                    result.setCode(Result.INVALID_REQUEST);
                    return result;
                }
                AbstractMessageDto messageDto = (AbstractMessageDto) JSONUtils.jsonToBean(schedulerMessage.getDtoJson(), schedulerMessage.getDtoClass());
                if (null == messageDto || null == messageDto.getOrderNo() || null == messageDto.getRequestProfile()) {
                    LOGGER.info("开票信息推送任务调度{},场景业务数据对象不存在,暂停重试", iMessage.getTopic());
                    result.setCode(Result.INVALID_REQUEST);
                    return result;
                }

                // 解析message 获取信息
                RequestProfile requestProfile = messageDto.getRequestProfile();
                requestProfile.setTraceId(requestProfile.getTraceId() + "_" + pdqTopicEnum.getTopic());

                // 查询订单详情
                ExpressOrderModel orderModel = this.getOrderInfo(requestProfile, messageDto.getOrderNo());
                UnitedBusinessIdentityUtil.convertModelToReal(orderModel);

                if (OrderTypeEnum.SERVICE_ENQUIRY_ORDER == orderModel.getOrderType()) {
                    //原单
                    ExpressOrderModel originOrderModel = this.getOrderInfo(requestProfile, orderModel.getRefOrderInfoDelegate().getOriginalOrderNo());

                    // 构建请求&推送开票信息
                    PushInvoiceFacadeRequest request = invoiceFacadeTranslator.toFreightServiceEnquiryRequest(orderModel, originOrderModel);
                    invoiceFacade.pushOpenInvoiceDetail(request);

                }

                // 港澳信息开票
                if (orderModel.isHKMO()) {
                    PushInvoiceFacadeRequest request = this.pushInvoiceHKMO(orderModel);
                    invoiceFacade.pushOpenInvoiceDetail(request);
                }

                result = new Result(Result.SUCCESS);
                LOGGER.info("开票信息推送完成");
            }
        } catch (InfrastructureException e) {
            // 调用开票服务业务异常
            LOGGER.error("开票信息推送失败业务异常: {}", e.fullMessage());
            // 业务无需重试异常
            if (BUSINESS_ERR.equals(e.subCode())) {
                result.setCode(Result.INVALID_REQUEST);
                Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_PUSH_INVOICE_FAIL,
                        "开票信息推送失败，暂停重试，需人工介入! 业务异常信息: " + e.fullMessage() +
                                ", 消息id: " + iMessage.getMessageId());
            } else {
                LOGGER.error("开票信息推送执行业务异常,再次重试: {}", e.fullMessage());
                umpUtil.functionError(callerInfo);
            }
        } catch (Exception e) {
            LOGGER.error("开票信息推送执行异常,再次重试", e);
            umpUtil.functionError(callerInfo);
        } finally {
            umpUtil.registerInfoEnd(callerInfo);
        }
        return result;
    }

    /**
     * 查询订单详情，并转换为领域模型
     *
     * @param requestProfile
     * @param orderNo
     * @return
     */
    private ExpressOrderModel getOrderInfo(RequestProfile requestProfile, String orderNo) {
        // 查询订单最新数据
        GetOrderFacadeRequest getOrderRequest = new GetOrderFacadeRequest();
        getOrderRequest.setOrderNo(orderNo);
        GetOrderFacadeResponse facadeResponse = getOrderFacade.getOrder(requestProfile, getOrderRequest);
        //将订单详情转换成model
        ExpressOrderModelCreator orderModelCreator = orderModelCreatorTranslator.toExpressOrderModelCreator(facadeResponse);
        return new ExpressOrderModel(orderModelCreator);
    }

    /**
     * 走港澳订单开票逻辑
     *
     * @return
     */
    private PushInvoiceFacadeRequest pushInvoiceHKMO(ExpressOrderModel orderModel) {
        // 判断结算方式
        SettlementTypeEnum settlementTypeEnum = orderModel.getFinance().getSettlementType();
        Integer settlementType = BmsUtils.getSettlementType(settlementTypeEnum);
        if (null == settlementType) {
            LOGGER.info("非现结场景，无需推送开票信息");
            return null;
        }

        PushInvoiceFacadeRequest.FinanceFacade financeFacade = new PushInvoiceFacadeRequest.FinanceFacade();
        // 结算方式
        financeFacade.setSettlementType(settlementType);

        // 根据结算方式和始发目的流向 获取merchantId
        String merchantId = MerchantUtils.getCustomsMerchantId(orderModel);
        if (null == merchantId) {
            LOGGER.error("未获取到merchantId，无法开票");
            return null;
        }

        // 调外单台账获取实收明细
        QueryOrderBankPayDetailsResponse payDetailsResponse = retailOrderBankFacade.queryOrderBankPayDetails(orderModel.getCustomOrderNo(), merchantId);
        // 查询外单台账总账 获取开票金额&币种
        QueryOrderBankResponse response = retailOrderBankFacade.queryOrderBank(orderModel.getCustomOrderNo(), merchantId, payDetailsResponse.getMerchantOrderNo());
        // 可开票总额
        if (null == response.getRealPayPrice() || BigDecimal.ZERO.equals(response.getRealPayPrice())) {
            // 实收金额目前不会小于0
            throw new RuntimeException("实收查询失败--无金额信息");
        }
        financeFacade.setDiscountAmount(response.getRealPayPrice());
        // 币种 币种转换 外单 -> 物流财务
        // 外单台账在港澳项目中使用的币种枚举和物流内部计费系统值统一，是137那套
        financeFacade.setCurrencyType(response.getCurrency());

        // 业务类型
        BizTypeEnum bizType = BmsUtils.getBizType(orderModel); // todo
        if (null == bizType) {
            LOGGER.error("未获取到对应的bizType, 订单号: {}", orderModel.orderNo());
            return null;
        }
        // 构建请求&推送开票信息
        return invoiceFacadeTranslator.toPushInvoiceFacadeRequest(orderModel, financeFacade, bizType);

    }

}
