package cn.jdl.oms.express.worker.scheduler.orderbank;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.GetOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.orderbank.OrderBankFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.orderbank.RetailOrderBankFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderModelCreatorTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.FreightOrderBankFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankMapper;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankRedisOp;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.bdue.ReceiveTypeEnum;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.MerchantUtils;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedBusinessIdentityUtil;
import cn.jdl.oms.express.domain.infrs.ohs.locals.redis.IRedisLock;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.OrderBankClearPdqMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.message.SchedulerMessage;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.MerchantEnum;
import cn.jdl.oms.express.shared.common.dict.PDQTopicEnum;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import cn.jdl.oms.express.worker.scheduler.AbstractSchedulerHandler;
import com.jd.paq.core.Message;
import com.jd.paq.plugin.IMessage;
import com.jd.paq.plugin.PDQClientException;
import com.jd.paq.plugin.Result;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * @Package： cn.jdl.oms.express.worker.scheduler.orderbank
 * @ClassName: C2cCancelOrderBankHandler
 * @Description: 取消台账Handler
 * @Author： zhangqi
 * @CreateDate 2021/4/8 11:55
 * @Copyright: Copyright (c)2021 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version： V1.0
 */
public class C2CClearOrderBankHandler extends AbstractSchedulerHandler {
    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(C2CClearOrderBankHandler.class);

    /**
     * 台账facade
     */
    @Resource
    private OrderBankFacade orderBankFacade;

    /**
     * 询价台账redisOp
     */
    @Resource
    private OrderBankRedisOp orderBankRedisOp;

    /**
     * 转换器
     */
    @Resource
    private OrderBankFacadeTranslator orderBankFacadeTranslator;

    @Resource
    private FreightOrderBankFacadeTranslator freightOrderBankFacadeTranslator;

    /**
     * 外单实收查询
     */
    @Resource
    private RetailOrderBankFacade retailOrderBankFacade;

    /**
     * 订单详情查询
     */
    @Resource
    private GetOrderFacade getOrderFacade;

    @Resource
    private GetOrderModelCreatorTranslator getOrderModelCreatorTranslator;
    /**
     * 重试最大次数
     */
    private final static int MAX_RETRY_TIMES = 0;

    //POS寄付特殊错误码：修改应收失败,订单已经开始支付
    private final static String POS_JF_PAYED_ERROR_CODE = "111113";

    /**
     * @param iMessage
     * @return
     * @throws PDQClientException
     */
    @Override
    public Result execute(IMessage iMessage) throws PDQClientException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_WORKER_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        //台账清理任务调度执行结果对象根据实际的执行结果定义返回具体的业务结果
        Result result = new Result(Result.SYSTEMERROR);
        String topic = iMessage.getTopic();
        IRedisLock redisLock = null;
        try {
            // 设置消息体为重试消息
            if (iMessage instanceof Message) {
                LOGGER.info("台账清理任务调度【{}】开始执行", topic);
                int retryTime = ((Message) iMessage).getRedriveCount();
                if (retryTime > MAX_RETRY_TIMES) {
                    LOGGER.info("台账清理任务调度【{}】,重试次数超过{}次,暂停重试", topic, retryTime);
                    result.setCode(Result.INVALID_REQUEST);
                    return result;
                }
                String iMessageContent = iMessage.getMessageBody();
                PDQTopicEnum pdqTopicEnum = PDQTopicEnum.getByTopic(topic);
                if (null == pdqTopicEnum) {
                    LOGGER.info("台账清理任务调度【{}】,未匹配到任务队列,暂停重试", topic);
                    return result;
                }
                // 重试消息体
                SchedulerMessage schedulerMessage = JSONUtils.jsonToBean(
                        iMessageContent, SchedulerMessage.class);
                if (null == schedulerMessage) {
                    LOGGER.info("台账清理任务调度【{}】,重试消息体不存在,暂停重试", topic);
                    return result;
                }
                OrderBankClearPdqMessageDto orderBankClearPdqMessage = (OrderBankClearPdqMessageDto) JSONUtils.jsonToBean(schedulerMessage.getDtoJson(), schedulerMessage.getDtoClass());
                if (orderBankClearPdqMessage == null) {
                    LOGGER.info("台账清理任务调度【{}】,场景业务数据对象不存在,暂停重试", topic);
                    return result;
                }
                String orderNo = orderBankClearPdqMessage.getOrderNo();
                RequestProfile requestProfile = orderBankClearPdqMessage.getRequestProfile();
                LOGGER.info("台账清理任务 topic:【{}】orderNo:【{}】开始", topic, orderNo);

                //询价台账是否初始化,未初始化直接失败
                if (!orderBankRedisOp.haveInit(requestProfile, orderBankClearPdqMessage.getBusinessIdentity(), orderNo, true)) {
                    LOGGER.info("台账清理任务调度【{}】,订单{}询价台账未初始化，失败重试", topic, orderNo);
                    Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_WORKER_ORDER_BANK_EXCEPTION_ALARM_MONITOR
                            , System.currentTimeMillis()
                            , "台账清理： 租户:" + requestProfile.getTenantId() + ", 订单号:" + orderNo
                                    + ", 台账未初始化");
                    return result;
                }
                redisLock = orderBankRedisOp.getLock(requestProfile, orderBankClearPdqMessage.getBusinessIdentity(), orderNo);
                //获取询价台账锁
                if (!redisLock.tryLock()) {
                    LOGGER.info("台账清理任务调度【{}】,订单{}未获取到询价台账锁，失败重试", topic, orderNo);
                    Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_WORKER_ORDER_BANK_EXCEPTION_ALARM_MONITOR
                            , System.currentTimeMillis()
                            , "台账清理： 租户:" + requestProfile.getTenantId() + ", 订单号:" + orderNo
                                    + ", 未获取到询价台账锁");
                    return result;
                }

                // 查询订单详情
                String waybillNo = orderBankClearPdqMessage.getOrderBankFacadeRequest().getWaybillNo();
                ExpressOrderModel orderModel;
                if (StringUtils.isNotBlank(orderNo)) {
                    orderModel = this.getOrderInfo(requestProfile, orderNo, null);
                } else {
                    LOGGER.info("台账清理,orderNo为空,使用waybillNo查询订单详情,waybillNo:{}", waybillNo);
                    orderModel = this.getOrderInfo(requestProfile, null, waybillNo);
                }
                if (orderModel == null || StringUtils.isBlank(orderModel.orderNo())) {
                    LOGGER.info("订单信息不存在，消息不消费");
                    result.setCode(Result.SUCCESS);
                    return result;
                }
                UnitedBusinessIdentityUtil.convertModelToReal(orderModel);

                MerchantEnum merchantIdEnum = MerchantUtils.getMerchantIdEnum(orderModel);
                if (null == merchantIdEnum) {
                    LOGGER.error("台账清理任务 topic:【{}】orderNo:【{}】外单台账merchantId获取失败", topic, orderNo);
                    Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_WORKER_ORDER_BANK_EXCEPTION_ALARM_MONITOR
                            , System.currentTimeMillis()
                            , "台账清理： 租户:" + requestProfile.getTenantId() + ", 订单号:" + orderNo
                                    + ", 外单台账merchantId获取失败！");
                    result.setCode(Result.INVALID_REQUEST);
                    return result;
                }
                //查询外单台账是否已经有实收
                if (retailOrderBankFacade.isRetailOrderBank(waybillNo, merchantIdEnum.getMerchantId())) {
                    LOGGER.info("台账清理任务 topic:【{}】orderNo:【{}】waybillNo：【{}】外单已存在实收，不允许清账！", topic, orderNo, waybillNo);
                    Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_WORKER_ORDER_BANK_EXCEPTION_ALARM_MONITOR
                            , System.currentTimeMillis()
                            , "台账清理： 租户:" + requestProfile.getTenantId() + ", 订单号:" + orderNo
                                    + ", 外单已存在实收，不执行清账！");
                    result.setCode(Result.SUCCESS);
                    return result;
                }
                // 查询外单台账是否已经有实收-兜底处理-默认用10024 再查询一次
                if (retailOrderBankFacade.isRetailOrderBank(waybillNo, MerchantEnum.CASH_ON_PICK.getMerchantId())) {
                    LOGGER.info("台账清理任务 topic:【{}】orderNo:【{}】waybillNo：【{}】外单已存在实收，不允许清账--命中兜底处理逻辑，请核查MerchantId！", topic, orderNo, waybillNo);
                    Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_WORKER_ORDER_BANK_EXCEPTION_ALARM_MONITOR
                            , System.currentTimeMillis()
                            , "台账清理： 租户:" + requestProfile.getTenantId() + ", 订单号:" + orderNo
                                    + ", 外单已存在实收，不执行清账！");
                    result.setCode(Result.SUCCESS);
                    return result;
                }

                OrderBankFacadeRequest orderBankFacadeRequest = orderBankClearPdqMessage.getOrderBankFacadeRequest();
                if (OrderTypeEnum.SERVICE_ENQUIRY_ORDER == orderModel.getOrderType() && !orderModel.isSelfPickupTemporaryStorageOrder()) {
                    // 服务询价单通用转换需要查询原单
                    String originalOrderNo = orderModel.getRefOrderInfoDelegate().getOriginalOrderNo();
                    ExpressOrderModel originOrderModel = this.getOrderInfo(requestProfile, originalOrderNo, null);
                    // 从原单补齐信息
                    orderBankFacadeRequest.setOrgId(originOrderModel.getFinance().getCollectionOrgNo());
                    orderBankFacadeRequest.setConsignorInfo(OrderBankMapper.INSTANCE.toConsignorInfo(originOrderModel.getConsignor()));
                    orderBankFacadeRequest.setConsigneeInfo(OrderBankMapper.INSTANCE.toConsigneeInfo(originOrderModel.getConsignee()));

                    // 服务询价单POS转换
                    orderBankFacadeRequest.setPosYun(freightOrderBankFacadeTranslator.toServiceEnquiryPosYun(originOrderModel, orderModel, BigDecimal.ZERO));
                }
                OrderBankFacadeRequest orderBankFacadeRequestClear = toCommonOrderBankFacadeRequestClear(orderBankFacadeRequest);

                //pos 到付清理
                if (orderBankClearPdqMessage.isClearPosYun()) {
                    orderBankFacadeRequestClear.setPosJfYun(null);
                    orderBankFacadeRequestClear.setBMerchantModify(null);
                    //含有特殊逻辑使用外部参数传入
                    orderBankFacadeRequestClear.setPosYun(orderBankFacadeRequest.getPosYun());
                    orderBankFacade.saveOrUpdate(orderBankFacadeRequestClear, this.getClass());
                }
                //pos 寄付清理
                if (orderBankClearPdqMessage.isClearPosJfYun()) {
                    try {
                        orderBankFacadeRequestClear.setPosYun(null);
                        orderBankFacadeRequestClear.setBMerchantModify(null);
                        //含有特殊逻辑使用外部参数传入
                        orderBankFacadeRequestClear.setPosJfYun(orderBankFacadeRequest.getPosJfYun());
                        orderBankFacade.saveOrUpdate(orderBankFacadeRequestClear, this.getClass());
                    } catch (BusinessDomainException e) {
                        if (POS_JF_PAYED_ERROR_CODE.equals(e.subCode())) {
                            LOGGER.info("pos寄付清理失败命中111113场景，错误信息：{}，不需要重试，按成功处理", e.subMessage());
                            return new Result(Result.SUCCESS);
                        }
                    }
                }
                //B商家修改 cod清理
                if (orderBankClearPdqMessage.isClearBMerchantCod()) {
                    orderBankFacadeRequestClear.setPosYun(null);
                    orderBankFacadeRequestClear.setPosJfYun(null);
                    OrderBankFacadeRequest.BMerchantModify bMerchantModifyCod = new OrderBankFacadeRequest.BMerchantModify();
                    bMerchantModifyCod.setBMerchantDueDetailInfo(orderBankFacadeTranslator.generateZeroBMerchantDueDetailInfo(ReceiveTypeEnum.RECEIVE_TYPE_HuoKuan));
                    orderBankFacadeRequestClear.setBMerchantModify(bMerchantModifyCod);
                    orderBankFacade.saveOrUpdate(orderBankFacadeRequestClear, this.getClass());
                }
                //B商家修改 到付清理
                if (orderBankClearPdqMessage.isClearBMerchantDf()) {
                    orderBankFacadeRequestClear.setPosYun(null);
                    orderBankFacadeRequestClear.setPosJfYun(null);
                    OrderBankFacadeRequest.BMerchantModify bMerchantModifyDf = new OrderBankFacadeRequest.BMerchantModify();
                    bMerchantModifyDf.setBMerchantDueDetailInfo(orderBankFacadeTranslator.generateZeroBMerchantDueDetailInfo(ReceiveTypeEnum.RECEIVE_TYPE_Yun));
                    orderBankFacadeRequestClear.setBMerchantModify(bMerchantModifyDf);
                    orderBankFacade.saveOrUpdate(orderBankFacadeRequestClear, this.getClass());
                }
                //B商家修改 寄付清理
                if (orderBankClearPdqMessage.isClearBMerchantJf()) {
                    orderBankFacadeRequestClear.setPosYun(null);
                    orderBankFacadeRequestClear.setPosJfYun(null);
                    OrderBankFacadeRequest.BMerchantModify bMerchantModifyJf = new OrderBankFacadeRequest.BMerchantModify();
                    bMerchantModifyJf.setBMerchantDueDetailInfo(orderBankFacadeTranslator.generateZeroBMerchantDueDetailInfo(ReceiveTypeEnum.RECEIVE_TYPE_JiFuYun));
                    orderBankFacadeRequestClear.setBMerchantModify(bMerchantModifyJf);
                    orderBankFacade.saveOrUpdate(orderBankFacadeRequestClear, this.getClass());
                }
                //B商家修改 到付暂存费清理
                if (orderBankClearPdqMessage.isClearBMerchantZCF()) {
                    orderBankFacadeRequestClear.setPosYun(null);
                    orderBankFacadeRequestClear.setPosJfYun(null);
                    OrderBankFacadeRequest.BMerchantModify bMerchantModifyDf = new OrderBankFacadeRequest.BMerchantModify();
                    bMerchantModifyDf.setBMerchantDueDetailInfo(orderBankFacadeTranslator.generateZeroBMerchantDueDetailInfo(ReceiveTypeEnum.RECEIVE_TYPE_ZCF));
                    orderBankFacadeRequestClear.setBMerchantModify(bMerchantModifyDf);
                    orderBankFacade.saveOrUpdate(orderBankFacadeRequestClear, this.getClass());
                }

                // 外单台账
                if (orderBankClearPdqMessage.isClearOts()) {
                    orderBankFacadeRequestClear.setPosYun(null);
                    orderBankFacadeRequestClear.setPosJfYun(null);
                    orderBankFacadeRequestClear.setBMerchantModify(null);
                    orderBankFacadeRequestClear.setOtsCancel(orderBankFacadeTranslator.toOtsCancel(orderModel, merchantIdEnum));
                    orderBankFacade.clear(orderBankFacadeRequest);
                }

                result.setCode(Result.SUCCESS);
                LOGGER.info("台账清理任务 topic:【{}】orderNo:【{}】完成", topic, orderNo);
            }
        } catch (BusinessDomainException e) {
            LOGGER.error("台账清理任务调度执行异常,再次重试: {}", e.fullMessage());
        } catch (Exception e) {
            LOGGER.error("台账清理任务调度执行异常,再次重试", e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
            if (redisLock != null) {
                redisLock.unlock();
            }
        }
        return result;

    }

    /**
     * 台账清理公共参数
     *
     * @param orderBankFacadeRequest
     */
    private OrderBankFacadeRequest toCommonOrderBankFacadeRequestClear(OrderBankFacadeRequest orderBankFacadeRequest) {
        //清理不需要的台账操作
        OrderBankFacadeRequest orderBankFacadeRequestClear = new OrderBankFacadeRequest();
        orderBankFacadeRequestClear.setBMerchantCreate(null);
        orderBankFacadeRequestClear.setOtsCreate(null);
        orderBankFacadeRequestClear.setWaybillNo(orderBankFacadeRequest.getWaybillNo());
        orderBankFacadeRequestClear.setOrgId(orderBankFacadeRequest.getOrgId());
        orderBankFacadeRequestClear.setOrgName(orderBankFacadeRequest.getOrgName());
        orderBankFacadeRequestClear.setConsigneeInfo(orderBankFacadeRequest.getConsigneeInfo());
        orderBankFacadeRequestClear.setConsignorInfo(orderBankFacadeRequest.getConsignorInfo());
        orderBankFacadeRequestClear.setUUid(orderBankFacadeRequest.getUUid());
        return orderBankFacadeRequestClear;
    }

    /**
     * 查询订单详情
     * @param requestProfile
     * @param orderNo
     * @param waybillNo
     * @return
     */
    private ExpressOrderModel getOrderInfo(RequestProfile requestProfile, String orderNo, String waybillNo) {
        //查询订单详情
        GetOrderFacadeRequest getOrderFacadeRequest = new GetOrderFacadeRequest();
        getOrderFacadeRequest.setOrderNo(orderNo);
        getOrderFacadeRequest.setCustomOrderNo(waybillNo);

        GetOrderFacadeResponse facadeResponse = getOrderFacade.getOrder(requestProfile, getOrderFacadeRequest);
        if (facadeResponse == null || StringUtils.isBlank(facadeResponse.getOrderNo())) {
            return null;
        }
        //将订单详情转换成model
        ExpressOrderModelCreator orderModelCreator = getOrderModelCreatorTranslator.toExpressOrderModelCreator(facadeResponse);
        return new ExpressOrderModel(orderModelCreator).withRequestProfile(requestProfile);
    }
}
