#redis config
redis.pool.maxIdle=300
redis.pool.maxTotal=600
redis.pool.timeBetweenEvictionRunsMillis=5000
redis.pool.minEvictableIdleTimeMillis=8000
redis.pool.testOnBorrow=true
redis.pool.testWhileIdle=true
redis.pool.host=redis-nxeovu8oh6oh-proxy-nlb.jvessel-open-hb.jdcloud.com
redis.pool.port=6379
redis.pool.password=OMSExpress98
redis.pool.timeout=3000
redis.pool.host.client=redis-nxeovu8oh6oh-proxy-nlb.jvessel-open-hb.jdcloud.com
redis.pool.port.client=6379
redis.pool.password.client=OMSExpress98

#\u81EA\u5B9A\u4E49\u72B6\u6001\u7F13\u5B58\u65F6\u95F4 1\u5206\u949F
status.expiryTime=60
#\u91CD\u590D\u63D0\u4EA4\u5E76\u53D1\u9501\u6709\u6548\u671F \u5206\u949F
repeat.receive.cacheTimeOut=4
#\u91CD\u590D\u4FEE\u6539\u5E76\u53D1\u9501\u6709\u6548\u671F \u5206\u949F
repeat.modify.cacheTimeOut=1
#\u91CD\u590D\u53D6\u6D88\u5E76\u53D1\u9501\u6709\u6548\u671F \u5206\u949F
repeat.cancel.cacheTimeOut=1
#\u91CD\u590D\u56DE\u4F20\u5E76\u53D1\u9501\u6709\u6548\u671F \u5206\u949F
repeat.callBack.cacheTimeOut=1
#\u91CD\u590D\u5220\u9664\u5E76\u53D1\u9501\u6709\u6548\u671F\u5355\u4F4D\u79D2
repeat.delete.cacheTimeOut=5
#\u91CD\u590D\u6062\u590D\u5E76\u53D1\u9501\u6709\u6548\u671F\u5355\u4F4D\u79D2
repeat.recover.cacheTimeOut=5

#\uFFFD\uFFFD\u03BB\uFFFD\uFFFD
jxd.repeat.callBack.cacheTimeOut=60
#\uFFFD\uFFFD\u03BB\uFFFD\uFFFD
jxd.repeat.cancel.cacheTimeOut=60
#\uFFFD\uFFFD\u03BB\uFFFD\uFFFD\uFFFD\uFFFD
jxd.repeat.receive.cacheTimeOut=60
#\uFFFD\uFFFD\u03BB\uFFFD\uFFFD
jxd.repeat.modify.cacheTimeOut=10
#\u7BB1\u53F7\u7F13\u5B58\u6709\u6548\u65F6\u95F4150\u5929
b2c.boxCode.expiresTime=365
#\u9632\u91CD365\u5929
b2c.repeat.receive.cacheTimeOut=365
#\u9632\u5E76\u53D110\u79D2
b2c.relock.receive.cacheTimeOut=10

#\u91CD\u590D\u62E6\u622A\u5E76\u53D1\u9501\u6709\u6548\u671F \u5206\u949F
repeat.intercept.cacheTimeOut=1

# \u8BE2\u4EF7\u9632\u5E76\u53D1\u9501\u8FC7\u671F\u8D85\u65F6\u81EA\u52A8\u91CA\u653E\u65F6\u95F4 10\u79D2
repeat.enquiry.cacheTimeOut=10

#\u9632\u91CD\u7F13\u5B58\u5929\u6570
anti.repeat.cache.key.timeOut=180

#C2C\u8BA2\u5355\u9632\u91CD-\u5355\u4F4D\u5929
c2c.repeat.receive.cacheTimeOut=365

repeat.pay.cacheTimeOut= 15

#relation redis config
relation.redis.pool.host=redis-yz40le9zfo91-proxy-nlb.jvessel-open-hb.jdcloud.com
relation.redis.pool.port=6379
relation.redis.pool.password=OMSExpress98

#\u53F0\u8D26key\u5931\u6548\u65F6\u95F4\uFF08\u5929\uFF09
orderBank.cacheTimeOut=90

#\u8017\u6750\u552E\u5356-\u63A5\u5355-\u9632\u5E76\u53D1\u7F13\u5B58\u65F6\u95F4
packing.repeat.receive.cacheTimeOut=10
#\u8017\u6750\u552E\u5356-\u63A5\u5355-\u9632\u91CD\u7F13\u5B58\u65F6\u95F4
packing.repeat.receive.order.cacheTimeOut=365
#\u8017\u6750\u552E\u5356-\u56DE\u4F20-\u9632\u5E76\u53D1\u7F13\u5B58\u65F6\u95F4
packing.repeat.callBack.cacheTimeOut=10
#\u8017\u6750\u552E\u5356-\u53D6\u6D88-\u9632\u5E76\u53D1\u7F13\u5B58\u65F6\u95F4
packing.repeat.cancel.cacheTimeOut=10

#C2B\u9632\u91CD365\u5929
c2b.repeat.receive.cacheTimeOut=365
#C2B\u9632\u5E76\u53D110\u79D2
c2b.relock.receive.cacheTimeOut=10
#\u6839\u636E\u4EAC\u6807\u7F16\u7801\u83B7\u53D6\u56FD\u6807\u7F16\u7801,\u7F13\u5B58\u8FC7\u671F\u65F6\u95F4\uFF0C\u5355\u4F4D\uFF1A\u5929
gbDistrictByJDCodeCacheTimeout = 90

uep.repeat.receive.cacheTimeOut=365
uep.repeat.orderNo.cacheTimeOut=7
anti.relock.receive.cacheTimeOut=4
anti.relock.cacheTimeOut=1

#LAS\u9632\u91CD365\u5929
las.repeat.receive.cacheTimeOut=365
#LAS\u9632\u5E76\u53D110\u79D2
las.relock.receive.cacheTimeOut=10
#\u5FEB\u8FD0\u7BB1\u53F7\u9632\u91CD\uFF0C\u5355\u4F4D\u5929
freight.boxCode.expiresTime =730

#LM-\u843D\u5730\u914D\u8BA2\u5355-\u9632\u91CD-\u5355\u4F4D\u5929
lm.repeat.receive.cacheTimeOut=365
#LM-\u843D\u5730\u914D\u8BA2\u5355-\u9632\u5E76\u53D1-\u5355\u4F4D\u79D2
lm.relock.receive.cacheTimeOut=10

#CCB2B\u9632\u91CD365\u5929
cc.b2b.repeat.receive.cacheTimeOut=365
#CCB2B\u9632\u5E76\u53D110\u79D2
cc.b2b.relock.receive.cacheTimeOut=10

#\u5408\u540C\u7269\u6D41\u63A5\u5355\u9632\u5E76\u53D1\u9501\u63A5\u5355\u65F6\u95F4
contract.relock.receive.cacheTimeOut=10
contract.repeat.callBack.cacheTimeOut=10
#\u4FEE\u6539\u7BB1\u53F7\u7F13\u5B58\u6709\u6548\u671F
contract.boxCode.expiresTime=365

#\u9A6C\u7532\u63A5\u5355\u9632\u91CD\u7F13\u5B58\u5931\u6548\u65F6\u95F4
mask.repeat.receive.cacheTimeOut=730

#\u9000\u6B3E\u9632\u5E76\u53D110\u79D2
refund.relock.cacheTimeOut=10
#\u9000\u6B3E\u9632\u91CD\u590D365\u5929
refund.repeat.cacheTimeOut=10
##\u7A0E\u91D1\u540C\u6B65\u5904\u7406\u5E76\u53D1\u9501
tax.sync.lock.cacheTimeOut=2
#\u8FD0\u529B\u8BA2\u5355\u9632\u5E76\u53D1\u7F13\u5B58\u5931\u6548\u65F6\u95F4
tms.relock.receive.cacheTimeOut=10
#\u8FD0\u529B\u5E73\u53F0\u9632\u91CD\u8FC7\u671F\u65F6\u95F4
tms.repeat.cacheTimeOut=365
#\u9632\u91CD\u964D\u7EA7\u65F6\u95F4
repeatCreateCacheTimeoutDown=15

common.repeat.cacheTimeOut=365