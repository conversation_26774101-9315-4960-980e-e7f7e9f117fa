#JMQ\u57FA\u7840\u914D\u7F6E
jmq.app=JDLOMSEXPRESS
jmq.userName=JDLOMSEXPRESS
jmq.password=8039AD67
jmq.address=jmq-testcluster.jd.local:50088

#\u5206\u7247\u662F\u5426\u81EA\u52A8\u964D\u7EA7
jmq.downgrade=true
#\u964D\u7EA7\u81EA\u52A8\u6062\u590D\u65F6\u957Fms
jmq.downgradeTime=120000
#\u5931\u8D25\u6B21\u6570
jmq.errorCount=3

###\u8BA2\u5355\u4E2D\u53F0MQ start
#\u652F\u4ED8\u8D85\u65F6\u81EA\u52A8\u89E6\u53D1\u53D6\u6D88
jdl.oms.express.cancel.pay.timeout.order.destination=cancel_pay_timeOut_order
jdl.oms.express.callback.record.destination=callback_order_record
jdl.oms.express.modify.record.destination=modify_order_record
jdl.oms.express.cancel.record.destination=cancel_order_record
jdl.oms.express.delete.record.destination=delete_order_record
jdl.oms.express.order.snapshot.destination=order_snapshot_msg
jdl.oms.express.retry.order.pdq.destination=retry_order_pdq

#\u53F0\u8D26\u6D41\u6C34\u8BB0\u5F55
jdl.oms.express.order.bank.record.destination=orderBank_record

###\u8BA2\u5355\u4E2D\u53F0MQ end

###\u5916\u90E8MQ
# \u5916\u5355\u9000\u6B3E\u5B8C\u6210\u6D88\u606F
jdl.oms.express.order.refund=OutSideRefundMessage

#\u652F\u4ED8\u6D88\u606F
jdl.oms.express.jd.pay.success.notify.destination=jdpaySuccessNotify

#\u8FD0\u5355\u4FE1\u606F\u66F4\u65B0\u6D88\u606F
jdl.oms.express.waybill.update.info=bd_forward_update_info

#\u8BA2\u5355\u8BB0\u5F55
jdl.oms.express.create.record.destination=create_order_record

#\u8BE2\u4EF7\u8BB0\u5F55
jdl.oms.express.enquiry.record.destination=enquiry_order_record
#\u5916\u5355\u53F0\u8D26-\u5BF9\u8D26\u5B8C\u6210\u6D88\u606F
jdl.oms.express.reconciliation.success.destination=OTS_DuiZhang_Success
#C2C\u53F0\u8D26\u521D\u59CB\u5316
jdl.oms.express.c2c.orderBank.init.destination=init_order_bank_c2c

#\u62E6\u622A\u8BB0\u5F55
jdl.oms.express.intercept.record.destination=intercept_order_record
#\u652F\u4ED8\u8BB0\u5F55
jdl.oms.express.pay.record.destination=pay_order_record
#\u8BA2\u5355\u6062\u590D\u8BB0\u5F55\u6D88\u606F
jdl.oms.express.recover.record.destination=recover_order_record
#\u8BA2\u5355\u91CD\u53D7\u7406\u8BB0\u5F55\u6D88\u606F
jdl.oms.express.reaccept.record.destination=reaccept_order_record
#B2C\u53F0\u8D26\u521D\u59CB\u5316topic
jdl.oms.express.b2c.orderBank.init.destination=init_order_bank_b2c
#c2b\u53F0\u8D26\u521D\u59CB\u5316topic
jdl.oms.express.c2b.orderBank.init.destination=init_order_bank_c2b
#\u8BA2\u5355\u6570\u636E\u6D41\u6C34\u8BB0\u5F55\u6D88\u606F
jdl.oms.express.orderData.record.destination=EXPRESS_ORDER_DATA
#\u8BA2\u5355\u6570\u636E\u53D8\u66F4\u901A\u77E5
jdl.oms.express.orderData.update.notice=EXPRESS_ORDER_DATA_UPDATE_NOTICE
#\u8BA2\u5355\u63A5\u5355\u6210\u529F\u6D88\u606F
jdl.oms.express.createOrder.notice.destination=EXPRESS_ORDER_CREATE_NOTICE
##clp\u51B7\u94FE\u8BA1\u8D39\u7ED3\u679Cmq
jdl.oms.express.cc.fee.info.result.destination=lbs2eclp_fee_infos_result
#\u8BE2\u4EF7\u7CFB\u7EDF\u63A8\u9001\u53D6\u6D88\u6D88\u606Fmq
jdl.oms.express.tms.enquiry.cancel.notify.destination=tms_enquiry_cancel_notify
#\u8BE2\u4EF7\u56DE\u4F20\u6D88\u606Fmq
jdl.oms.express.tms.enquiry.confirm.back.destination=tms_enquiry_confirm_back
#\u51B7\u94FE\u8FD0\u5355\u5F85\u652F\u4ED8\u8FD0\u5355mq
jdl.oms.express.cold.chain.waybill.unpaid.destination=coldchain_waybill_unpaid
#\u79D1\u6280\u652F\u4ED8\u7ED3\u679C
jdl.oms.express.pay.return.info.destination= pay_return_info
#\u51B7\u94FE\u6574\u8F66\u8BE2\u4EF7\u63A8\u8BA1\u8D39mq
jdl.oms.express.cold.chain.push.fee.destination=eclp_to_lbs_enquiry_fee
#\u5FEB\u8FD0\u5916\u5355\u8BE2\u4EF7\u56DE\u4F20
jdl.oms.express.ldop.middle.enquiry.bill.back.destination=ldop_middle_enquiry_bill_back
#\u8BE2\u4EF7\u8F66\u8F86\u4FE1\u606F\u56DE\u4F20
jdl.oms.express.tms.enquiry.vehicle.driver.destination=ldop_middle_enquiry_vehicle_driver
#\u53D1\u9001B\u7F51\u7279\u6B8A\u8D39\u7528
jdl.oms.express.b.normal.specail.fee=b_normal_specail_fee
# ldop mq when package amount changed
jdl.oms.express.ldop.package.number=ldop_package_number
# terminal notify destination
jdl.oms.express.terminal.notify.destination=ql_erp_receive_finish_xx