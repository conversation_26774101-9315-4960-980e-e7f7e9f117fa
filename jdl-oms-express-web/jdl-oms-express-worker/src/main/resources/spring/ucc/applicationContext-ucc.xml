<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:util="http://www.springframework.org/schema/util"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
	http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd
	http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-3.1.xsd">

    <!-- ucc 配置 -->
    <bean id="uccConfigInsPostProcessor" class="com.jd.coo.ucc.client.service.UccConfigInsPostProcessor"/>

    <bean id="uccConfigCenter"  class="com.jd.coo.ucc.client.service.UccConfigCenter" lazy-init="false">
        <constructor-arg index="0" ref="zkConfig"/>
        <constructor-arg index="1" ref="uccConfig"/>
    </bean>

    <bean id="zkConfig" class="com.jd.coo.ucc.client.config.UccZkConfig">
        <property name="server" value="${zk.server}"/>
        <property name="sessionTimeout" value="${zk.session.timeout}"/>
        <property name="connectionTimeout" value="${zk.connection.timeout}"/>
        <property name="appPath" value="${app.path}"/>
        <property name="appGroup" value="${app.ucc.group}"/>
    </bean>

    <bean id="uccConfig" class="com.jd.coo.ucc.client.config.UccConfig">
        <property name="logConfig" ref="log2Config"/>
        <property name="propertyConfig" ref="propertyConfig"/>
        <property name="jsfConfig" ref="jsfConfig"/>
    </bean>

    <!-- 将jsf托管至UCC统一配置中心 -->
    <bean id="jsfConfigProcessor" class="com.jd.coo.ucc.client.plugin.processor.JsfConfigProcessor"/>
    <bean id="jsfConfig" class="com.jd.coo.ucc.client.config.UccJsfConfig">
        <!--  若需要以追加动态别名的方式修改发布的JSF服务，请务必放开此段配置
            <property name="dynamicGroup" value="${jsfConfig.dynamicGroup}" ></property>
            <property name="appId" value="${jsfConfig.appId}"></property>
            <property name="token" value="${jsfConfig.token}"></property>
            <property name="erp" value="${jsfConfig.erp}"></property>
            <property name="jsfOpenApi" value="${jsfConfig.jsfOpenApi}"></property>
        -->
        <property name="processor" ref="jsfConfigProcessor" />
        <property name="weakList">
            <list>
            </list>
        </property>
    </bean>

    <!-- 将log4j2.x日志输出level托管至统一配置中心 -->
    <bean id="log2ConfigProcessor" class="com.jd.coo.ucc.client.plugin.processor.Log2ConfigProcessor"/>
    <bean id="log2Config" class="com.jd.coo.ucc.client.config.UccLogConfig">
        <property name="processor" ref="log2ConfigProcessor"/>
        <property name="weakList">
            <list>
                <value>log4j2.AsyncLogger.RootLog</value>
                <value>log4j2.AsyncLogger.cn.jdl.oms.express.domain.ability</value>
                <value>log4j2.AsyncLogger.cn.jdl.oms.express.domain.service</value>
                <value>log4j2.AsyncLogger.cn.jdl.oms.express.domain.flow</value>
                <value>log4j2.AsyncLogger.cn.jdl.oms.express.domain.ext</value>
                <value>log4j2.AsyncLogger.cn.jdl.oms.express.domain.infrs</value>
                <value>log4j2.AsyncLogger.cn.jdl.oms.express.horz.infrs</value>
                <value>log4j2.AsyncLogger.cn.jdl.oms.express.horz.ext</value>
                <value>log4j2.AsyncLogger.cn.jdl.oms.express.app.service</value>
                <value>log4j2.AsyncLogger.cn.jdl.batrix</value>
                <value>log4j2.AsyncLogger.com.jd.matrix</value>
                <value>log4j2.AsyncLogger.com.jdl.cp</value>
                <value>log4j2.AsyncLogger.cn.jdl.express</value>
            </list>
        </property>
    </bean>

    <!-- spring bean -->
    <bean id="propertyConfigProcessor" class="com.jd.coo.ucc.client.plugin.processor.PropertyConfigProcessor"/>
    <bean id="propertyConfig" class="com.jd.coo.ucc.client.config.UccPropertyConfig">
        <property name="processor" ref="propertyConfigProcessor" />
        <property name="weakList">
            <list>
                <!--开关-->
                <!--接单敏感词校验-->
                <value>expressUccConfigCenter.sensitiveWordsSwitch</value>
                <!-- 防并发能力是否降级 -->
                <value>expressUccConfigCenter.antiConcurrentSwitch</value>
                <!-- JMQ是否发送消息开关 -->
                <value>expressUccConfigCenter.sendMessageSwitch</value>
                <!-- 逆向单原单费用计算方式开关 -->
                <value>expressUccConfigCenter.orderBankMergeSwitch</value>
                <!-- 支付机构查询缓存开关 -->
                <value>expressUccConfigCenter.addressOrgCacheSwitch</value>
                <!-- 支付机构查询缓存时间 -->
                <value>expressUccConfigCenter.addressOrgCacheExpireSeconds</value>
                <!-- 订单超时取消buffer -->
                <value>expressUccConfigCenter.cancelPayTimeOutOrderBuffer</value>
                <!--台账修改回滚查询外单开关-->
                <value>expressUccConfigCenter.orderBankModifyRollbackQueryLdopSwitch</value>
                <!-- 台账同步初始化开关 -->
                <value>expressUccConfigCenter.orderBankSyncInitSwitch</value>
                <!-- 逆向单-结算方式月结-是否走商家配置校验：false 不校验，true校验 -->
                <value>expressUccConfigCenter.reverseMonthSettlementCustomerConfigSwitch</value>
                <!-- Gis解析失败是否阻塞流程：true 阻塞，false 不阻塞 -->
                <value>expressUccConfigCenter.gisAnalysisFailBlockFlowSwitch</value><!--接单长度校验-->
                <!--接单敏感词校验-->
                <value>expressUccConfigCenter.sensitiveWordsSwitch</value>
                <!-- 防并发能力是否降级 -->
                <value>expressUccConfigCenter.antiConcurrentSwitch</value>
                <!-- JMQ是否发送消息开关 -->
                <value>expressUccConfigCenter.sendMessageSwitch</value>
                <!-- 逆向单原单费用计算方式开关 -->
                <value>expressUccConfigCenter.orderBankMergeSwitch</value>
                <!-- 允许货品数量为空渠道接入来源范围 -->
                <value>expressUccConfigCenter.cargoQuantitySystemCallers</value>
                <!-- 允许货品重量渠道接入来源范围 -->
                <value>expressUccConfigCenter.cargoWeightSystemCallers</value>
                <!-- 零售订单校验黑名单 -->
                <value>expressUccConfigCenter.jdOrderBlackList</value>
                <!-- 敏感词是否需要校验渠道来源 -->
                <value>expressUccConfigCenter.sensitiveWordSystemCallers</value>
                <!-- 修改渠道代收货款校验开关 -->
                <value>expressUccConfigCenter.modifyCodSwitch</value>
                <!-- 修改渠道代收货款校验开关 -->
                <value>expressUccConfigCenter.ccb2bModifyCodSwitch</value>
                <!-- 修改不校验白名单渠道 -->
                <value>expressUccConfigCenter.modifyWhiteSystemCallers</value>
                <!-- 支付机构查询缓存开关 -->
                <value>expressUccConfigCenter.addressOrgCacheSwitch</value>
                <!-- 支付机构查询缓存时间 -->
                <value>expressUccConfigCenter.addressOrgCacheExpireSeconds</value>
                <!-- 订单超时取消buffer -->
                <value>expressUccConfigCenter.cancelPayTimeOutOrderBuffer</value>
                <!--台账修改回滚查询外单开关-->
                <value>expressUccConfigCenter.orderBankModifyRollbackQueryLdopSwitch</value>
                <!-- 台账同步初始化开关 -->
                <value>expressUccConfigCenter.orderBankSyncInitSwitch</value>
                <!-- 逆向单-结算方式月结-是否走商家配置校验：false 不校验，true校验 -->
                <value>expressUccConfigCenter.reverseMonthSettlementCustomerConfigSwitch</value>
                <!-- Gis解析失败是否阻塞流程：true 阻塞，false 不阻塞 -->
                <value>expressUccConfigCenter.gisAnalysisFailBlockFlowSwitch</value>
                <!-- JMQ接单是否发送消息开关 -->
                <value>expressUccConfigCenter.sendCreateFlowMessageSwitch</value>
                <!-- JMQ修改是否发送消息开关 -->
                <value>expressUccConfigCenter.sendModifyFlowMessageSwitch</value>
                <!-- JMQ取消是否发送消息开关 -->
                <value>expressUccConfigCenter.sendCancelFlowMessageSwitch</value>
                <!-- JMQ删单是否发送消息开关 -->
                <value>expressUccConfigCenter.sendDeleteFlowMessageSwitch</value>
                <!-- JMQ回传是否发送消息开关 -->
                <value>expressUccConfigCenter.sendCallBackFlowMessageSwitch</value>
                <!-- 同步运单数据特殊处理增值服务编码 -->
                <value>expressUccConfigCenter.syncWaybillDataSpecialHandlerAddedProduct</value>
                <!-- 回传-未映射到订单状态到扩展状态白名单配置 -->
                <value>expressUccConfigCenter.callBackExtendStatusValidWhite</value>
                <!-- 修改-需要校验询价状态的systemCaller -->
                <value>expressUccConfigCenter.modifyValidEnquirySystemCaller</value>
                <!-- 回传-需要处理的扩展状态白名单配置 -->
                <value>expressUccConfigCenter.callBackExtendStatusHandleWhite</value>
                <!-- 百川监控数据上报采集开关 -->
                <value>expressUccConfigCenter.bscMonitorSwitch</value>
                <!-- 百川监控数据上报采集频率秒 -->
                <value>bMonitorConfig.frequency</value>
                <!-- 逆向单询价计算原单费用编码配置 -->
                <value>expressUccConfigCenter.reverseOriginOrderFinanceConfig</value>
                <!-- 逆向单询价计算原单费用折扣编码配置 -->
                <value>expressUccConfigCenter.reverseOriginOrderDiscountConfig</value>
                <!-- 修改订单数据同步修改来源黑名单 -->
                <value>expressUccConfigCenter.modifyDataSyncSourceBlack</value>
                <!-- 接货退货场景运单状态配置 -->
                <value>expressUccConfigCenter.pickReturnWaybillStatus</value>
                <!-- 京喜达台账是否同步初始化-->
                <value>expressUccConfigCenter.jxdOrderBankSyncInitSwitch</value>
                <!-- 增值服务白名单配置-->
                <value>expressUccConfigCenter.syncNewAddOnProductWhite</value>
                <!-- 同步新增主产品扩展关联产品白名单配置-->
                <value>expressUccConfigCenter.syncMainProductExtRefWhite</value>
                <!-- 是否询价查外单详情开关 -->
                <value>expressUccConfigCenter.c2cEnquiryQueryWaybillSwitch</value>
                <!-- C2C初始化台账降级开关 -->
                <value>expressUccConfigCenter.c2cInitOrderBankLowerSwitch</value>
                <!-- b2c是否异步写帐开关（true:同步,false:异步）-->
                <value>expressUccConfigCenter.b2cOrderBankSyncInitSwitch</value>
                <!-- b2c异步写帐方式（true:pdq,false:jmq）-->
                <value>expressUccConfigCenter.b2cOrderBankSyncInitPdqSwitch</value>
                <!--C2C揽收后不允许修改结算方式开关-->
                <value>expressUccConfigCenter.c2cNotAllowModifySettlementAfterPickedUpSwitch</value>
                <!-- c2b是否异步写帐开关（true:同步,false:异步）-->
                <value>expressUccConfigCenter.c2bOrderBankSyncInitSwitch</value>
                <!-- c2b异步写帐方式（true:pdq,false:jmq）-->
                <value>expressUccConfigCenter.c2bOrderBankSyncInitPdqSwitch</value>
                <!-- 规则引擎开关控制，true:开启，false:关闭 -->
                <value>expressUccConfigCenter.droolsRuleSwitch</value>
                <!-- 金额小数位降级成2位开关：true 开启 false 关闭 -->
                <value>expressUccConfigCenter.amountScaleDownSwitch</value>
                <!-- 温层时效开关：true 开启 false 关闭 -->
                <value>expressUccConfigCenter.warmLayerAgingSwitch</value>
                <!-- 原单未支付，生成逆向单时需要合并支付的支付方式名单配置，多个,分割。配置的是原单的支付方式 -->
                <value>expressUccConfigCenter.needSumOriginalOrderPaymentWhite</value>

                <!-- 根据渠道的调用方的子来源，判断是否需要创建关联关系-->
                <value>expressUccConfigCenter.needCreateRelationSystemSubCaller</value>
                <!-- 根据订单标识字段，判断是否需要创建关联关系-->
                <value>expressUccConfigCenter.needCreateRelationOrderSign</value>
                <!-- 改址单询价增值产品白名单配置 -->
                <value>expressUccConfigCenter.readdressEnquiryAddOnProductWhite</value>
                <!-- CCB2C是否异步写帐开关（true:同步,false:异步）-->
                <value>expressUccConfigCenter.ccB2cOrderBankSyncInitSwitch</value>
                <!-- 询价接口发送询价记录消息开关 -->
                <value>expressUccConfigCenter.sendExpressOrderEnquiryRecordSwitch</value>
                <!-- 快运揽收后改址支付超时时间buffer -->
                <value>expressUccConfigCenter.freightPayOutTimeBuffer</value>
                <!-- 快运校验商家是否开通月结 -->
                <value>expressUccConfigCenter.freightValidateMonthlyPaymentSwitch</value>
                <!-- CCB2B是否异步写帐开关（true:同步,false:异步）-->
                <value>expressUccConfigCenter.ccB2BOrderBankSyncInitSwitch</value>
                <!-- 快运预占优惠券和回滚优惠券使用客户订单号开关 -->
                <value>expressUccConfigCenter.freightCouponUseCustomerOrderNoSwitch</value>
                <!-- 冷链B2B超时取消时间 -->
                <value>expressUccConfigCenter.ccB2BCancelPayTimeOutOrderBuffer</value>
                <!-- 快运整车直达待销售确认时间 -->
                <value>expressUccConfigCenter.freightFTLWaitSalesConfirmTimeBuffer</value>
                <!-- 港澳税金终端灰度派送站点白名单配置,多个英文逗号分割 -->
                <value>expressUccConfigCenter.taxPDAEndStationWhite</value>
                <!-- 图外能力点调用切换的流程图列表,多个英文逗号分割 -->
                <value>expressUccConfigCenter.switchFlowRunningOnlyList</value>
            </list>
        </property>
    </bean>


</beans>
