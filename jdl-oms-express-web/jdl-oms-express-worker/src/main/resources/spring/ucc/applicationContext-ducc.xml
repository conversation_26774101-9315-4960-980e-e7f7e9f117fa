<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:laf-config="http://ducc.jd.com/schema/laf-config"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
	    http://ducc.jd.com/schema/laf-config
	    http://ducc.jd.com/schema/laf-config/laf-config.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <context:annotation-config/>

   <bean id="configListener" class="cn.jdl.oms.express.domain.infrs.ohs.locals.ducc.ExpressDUCConfigCenter"/>
    <!-- https://joyspace.jd.com/pages/wPt96EsTHKRn2o6RkmV4 -->
    <laf-config:manager id="configuratorManager" application="${express.ducc.appName}">
        <laf-config:parameter key="autoListener" value="true"/>
        <laf-config:resource name="batrixResource"
                             uri="ucc://${ducc_application}:${ducc_token}@ducc.jd.local/v1/namespace/${ducc_namespace}/config/${ducc_config}/profiles/${ducc_profile}?longPolling=60000&amp;necessary=true"/>
        <!-- necessary 该配置资源是否被强依赖，如果配置为 true 则应用(DUCC SDK) 启动必须加载成功，否则程序启动不起来（加载成功包括从本地缓存文件恢复，远程拉取）建议值：true -->
        <!-- log4j2 resource -->
        <laf-config:resource name="logResource" uri="ucc://${express.ducc.appName}:${express.ducc.token}@${express.ducc.uri}/v1/namespace/${express.ducc.namespace}/config/${express.ducc.logConfig}/profiles/${express.ducc.profile}?longPolling=60000&amp;necessary=true"/>
        <!-- log4j2 配置-->
        <laf-config:listener-script key="logger.level" script="script/log4j2.js"/>
        <!-- properties resource -->
        <laf-config:resource name="propertiesResource" uri="ucc://${express.ducc.appName}:${express.ducc.token}@${express.ducc.uri}/v1/namespace/${express.ducc.namespace}/config/${express.ducc.propertiesConfig}/profiles/${express.ducc.profile}?longPolling=60000&amp;necessary=true"/>

        <!--接单敏感词校验-->
        <laf-config:listener-method key="expressUccConfigCenter.sensitiveWordsSwitch" method="setSensitiveWordsSwitch" beanName="expressUccConfigCenter"/>
        <!-- 防并发能力是否降级 -->
        <laf-config:listener-method key="expressUccConfigCenter.antiConcurrentSwitch" method="setAntiConcurrentSwitch" beanName="expressUccConfigCenter"/>
        <!-- JMQ是否发送消息开关 -->
        <laf-config:listener-method key="expressUccConfigCenter.sendMessageSwitch" method="setSendMessageSwitch" beanName="expressUccConfigCenter"/>
        <!-- 逆向单原单费用计算方式开关 -->
        <laf-config:listener-method key="expressUccConfigCenter.orderBankMergeSwitch" method="setOrderBankMergeSwitch" beanName="expressUccConfigCenter"/>
        <!-- 支付机构查询缓存开关 -->
        <laf-config:listener-method key="expressUccConfigCenter.addressOrgCacheSwitch" method="setAddressOrgCacheSwitch" beanName="expressUccConfigCenter"/>
        <!-- 支付机构查询缓存时间 -->
        <laf-config:listener-method key="expressUccConfigCenter.addressOrgCacheExpireSeconds" method="setAddressOrgCacheExpireSeconds" beanName="expressUccConfigCenter"/>
        <!-- 订单超时取消buffer -->
        <laf-config:listener-method key="expressUccConfigCenter.cancelPayTimeOutOrderBuffer" method="setCancelPayTimeOutOrderBuffer" beanName="expressUccConfigCenter"/>
        <!-- 写关联关系时的自定义单号最小长度 -->
        <laf-config:listener-method key="expressUccConfigCenter.orderRelationCustomerMinLength" method="setOrderRelationCustomerMinLength" beanName="expressUccConfigCenter"/>
        <!-- 允许货品数量为空渠道接入来源范围 -->
        <laf-config:listener-method key="expressUccConfigCenter.cargoQuantitySystemCallers" method="setCargoQuantitySystemCallers" beanName="expressUccConfigCenter"/>
        <!-- 允许货品重量渠道接入来源范围 -->
        <laf-config:listener-method key="expressUccConfigCenter.cargoWeightSystemCallers" method="setCargoWeightSystemCallers" beanName="expressUccConfigCenter"/>
        <!-- 零售订单校验黑名单 -->
        <laf-config:listener-method key="expressUccConfigCenter.jdOrderBlackList" method="setJdOrderBlackList" beanName="expressUccConfigCenter"/>
        <!-- 敏感词是否需要校验渠道来源 -->
        <laf-config:listener-method key="expressUccConfigCenter.sensitiveWordSystemCallers" method="setSensitiveWordSystemCallers" beanName="expressUccConfigCenter"/>
        <!-- 修改渠道代收货款校验开关 -->
        <laf-config:listener-method key="expressUccConfigCenter.modifyCodSwitch" method="setModifyCodSwitch" beanName="expressUccConfigCenter"/>
        <!-- 冷链B2B修改渠道代收货款校验开关 -->
        <laf-config:listener-method key="expressUccConfigCenter.ccb2bModifyCodSwitch" method="setCcb2bModifyCodSwitch" beanName="expressUccConfigCenter"/>
        <!-- 修改不校验白名单渠道 -->
        <laf-config:listener-method key="expressUccConfigCenter.modifyWhiteSystemCallers" method="setModifyWhiteSystemCallers" beanName="expressUccConfigCenter"/>
        <!--台账修改回滚查询外单开关-->
        <laf-config:listener-method key="expressUccConfigCenter.orderBankModifyRollbackQueryLdopSwitch" method="setOrderBankModifyRollbackQueryLdopSwitch" beanName="expressUccConfigCenter"/>
        <!-- 台账同步初始化开关 -->
        <laf-config:listener-method key="expressUccConfigCenter.orderBankSyncInitSwitch" method="setOrderBankSyncInitSwitch" beanName="expressUccConfigCenter"/>
        <!-- 逆向单-结算方式月结-是否走商家配置校验：false 不校验，true校验 -->
        <laf-config:listener-method key="expressUccConfigCenter.reverseMonthSettlementCustomerConfigSwitch" method="setReverseMonthSettlementCustomerConfigSwitch" beanName="expressUccConfigCenter"/>
        <!-- Gis解析失败是否阻塞流程：true 阻塞，false 不阻塞 -->
        <laf-config:listener-method key="expressUccConfigCenter.gisAnalysisFailBlockFlowSwitch" method="setGisAnalysisFailBlockFlowSwitch" beanName="expressUccConfigCenter"/>
        <!-- JMQ接单是否发送消息开关 -->
        <laf-config:listener-method key="expressUccConfigCenter.sendCreateFlowMessageSwitch" method="setSendCreateFlowMessageSwitch" beanName="expressUccConfigCenter"/>
        <!-- JMQ修改是否发送消息开关 -->
        <laf-config:listener-method key="expressUccConfigCenter.sendModifyFlowMessageSwitch" method="setSendModifyFlowMessageSwitch" beanName="expressUccConfigCenter"/>
        <!-- JMQ修改财务是否发送消息开关：true 发送，false:不发 -->
        <laf-config:listener-method key="expressUccConfigCenter.sendModifyFinanceFlowMessageSwitch" method="setSendModifyFinanceFlowMessageSwitch" beanName="expressUccConfigCenter"/>
        <!-- JMQ取消是否发送消息开关 -->
        <laf-config:listener-method key="expressUccConfigCenter.sendCancelFlowMessageSwitch" method="setSendCancelFlowMessageSwitch" beanName="expressUccConfigCenter"/>
        <!-- JMQ删单是否发送消息开关 -->
        <laf-config:listener-method key="expressUccConfigCenter.sendDeleteFlowMessageSwitch" method="setSendDeleteFlowMessageSwitch" beanName="expressUccConfigCenter"/>
        <!-- JMQ回传是否发送消息开关 -->
        <laf-config:listener-method key="expressUccConfigCenter.sendCallBackFlowMessageSwitch" method="setSendCallBackFlowMessageSwitch" beanName="expressUccConfigCenter"/>
        <!-- 同步运单数据特殊处理增值服务编码 -->
        <laf-config:listener-method key="expressUccConfigCenter.syncWaybillDataSpecialHandlerAddedProduct" method="setSyncWaybillDataSpecialHandlerAddedProduct" beanName="expressUccConfigCenter"/>
        <!-- 回传-未映射到订单状态到扩展状态白名单配置 -->
        <laf-config:listener-method key="expressUccConfigCenter.callBackExtendStatusValidWhite" method="setCallBackExtendStatusValidWhite" beanName="expressUccConfigCenter"/>
        <!-- 修改-需要校验询价状态的systemCaller -->
        <laf-config:listener-method key="expressUccConfigCenter.modifyValidEnquirySystemCaller" method="setModifyValidEnquirySystemCaller" beanName="expressUccConfigCenter"/>
        <!-- 回传-需要处理的扩展状态白名单配置 -->
        <laf-config:listener-method key="expressUccConfigCenter.callBackExtendStatusHandleWhite" method="setCallBackExtendStatusHandleWhite" beanName="expressUccConfigCenter"/>
        <!-- 百川监控数据上报采集开关 -->
        <laf-config:listener-method key="expressUccConfigCenter.bscMonitorSwitch" method="setBscMonitorSwitch" beanName="expressUccConfigCenter"/>
        <!-- 百川监控数据上报采集频率秒 -->
        <laf-config:listener-method key="expressUccConfigCenter.frequency" method="setFrequency" beanName="expressUccConfigCenter"/>
        <!-- 逆向单询价计算原单费用编码配置 -->
        <laf-config:listener-method key="expressUccConfigCenter.reverseOriginOrderFinanceConfig" method="setReverseOriginOrderFinanceConfig" beanName="expressUccConfigCenter"/>
        <!-- 逆向单询价计算原单费用折扣编码配置 -->
        <laf-config:listener-method key="expressUccConfigCenter.reverseOriginOrderDiscountConfig" method="setReverseOriginOrderDiscountConfig" beanName="expressUccConfigCenter"/>
        <!-- 修改订单数据同步修改来源黑名单 -->
        <laf-config:listener-method key="expressUccConfigCenter.modifyDataSyncSourceBlack" method="setModifyDataSyncSourceBlack" beanName="expressUccConfigCenter"/>
        <!-- 接货退货场景运单状态配置 -->
        <laf-config:listener-method key="expressUccConfigCenter.pickReturnWaybillStatus" method="setPickReturnWaybillStatus" beanName="expressUccConfigCenter"/>
        <!-- 京喜达台账是否同步初始化-->
        <laf-config:listener-method key="expressUccConfigCenter.jxdOrderBankSyncInitSwitch" method="setJxdOrderBankSyncInitSwitch" beanName="expressUccConfigCenter"/>
        <!-- C2C台账异步初始化方式,是否使用PDQ -->
        <laf-config:listener-method key="expressUccConfigCenter.c2cOrderBankSyncInitPdqSwitch" method="setC2cOrderBankSyncInitPdqSwitch" beanName="expressUccConfigCenter"/>
        <!-- 增值服务白名单配置-->
        <laf-config:listener-method key="expressUccConfigCenter.syncNewAddOnProductWhite" method="setSyncNewAddOnProductWhite" beanName="expressUccConfigCenter"/>
        <!-- 同步新增主产品扩展关联产品白名单配置-->
        <laf-config:listener-method key="expressUccConfigCenter.syncMainProductExtRefWhite" method="setSyncMainProductExtRefWhite" beanName="expressUccConfigCenter"/>
        <!-- 是否询价查外单详情开关 -->
        <laf-config:listener-method key="expressUccConfigCenter.c2cEnquiryQueryWaybillSwitch" method="setC2cEnquiryQueryWaybillSwitch" beanName="expressUccConfigCenter"/>
        <!-- C2C初始化台账降级开关 -->
        <laf-config:listener-method key="expressUccConfigCenter.c2cInitOrderBankLowerSwitch" method="setC2cInitOrderBankLowerSwitch" beanName="expressUccConfigCenter"/>
        <!-- b2c是否异步写帐开关（true:同步,false:异步）-->
        <laf-config:listener-method key="expressUccConfigCenter.b2cOrderBankSyncInitSwitch" method="setB2cOrderBankSyncInitSwitch" beanName="expressUccConfigCenter"/>
        <!-- b2c异步写帐方式（true:pdq,false:jmq）-->
        <laf-config:listener-method key="expressUccConfigCenter.b2cOrderBankSyncInitPdqSwitch" method="setB2cOrderBankSyncInitPdqSwitch" beanName="expressUccConfigCenter"/>
        <!--调用地址围栏业务线列表-->
        <laf-config:listener-method key="expressUccConfigCenter.invokeAddressFenceConfig" method="setInvokeAddressFenceConfig" beanName="expressUccConfigCenter"/>
        <!--C2C揽收后不允许修改结算方式开关-->
        <laf-config:listener-method key="expressUccConfigCenter.c2cNotAllowModifySettlementAfterPickedUpSwitch" method="setC2cNotAllowModifySettlementAfterPickedUpSwitch" beanName="expressUccConfigCenter"/>
        <!--内容安全校验服务信息：true开启新服务,false原数科老服务校验-->
        <laf-config:listener-method key="expressUccConfigCenter.aiSensitiveWordsSwitch" method="setAiSensitiveWordsSwitch" beanName="expressUccConfigCenter"/>
        <!-- c2b是否异步写帐开关（true:同步,false:异步）-->
        <laf-config:listener-method key="expressUccConfigCenter.c2bOrderBankSyncInitSwitch" method="setC2bOrderBankSyncInitSwitch" beanName="expressUccConfigCenter"/>
        <!-- c2b异步写帐方式（true:pdq,false:jmq）-->
        <laf-config:listener-method key="expressUccConfigCenter.c2bOrderBankSyncInitPdqSwitch" method="setC2bOrderBankSyncInitPdqSwitch" beanName="expressUccConfigCenter"/>
        <!-- 规则引擎开关控制，true:开启，false:关闭 -->
        <laf-config:listener-method key="expressUccConfigCenter.droolsRuleSwitch" method="setDroolsRuleSwitch" beanName="expressUccConfigCenter"/>
        <!-- 数据流水字段配置 ','分割-->
        <laf-config:listener-method key="expressUccConfigCenter.dataFlowRequiredFields" method="setDataFlowRequiredFields" beanName="expressUccConfigCenter"/>
        <!--订单数据流水开关，true:开启；false:关闭-->
        <laf-config:listener-method key="expressUccConfigCenter.dataFlowSwitch" method="setDataFlowSwitch" beanName="expressUccConfigCenter"/>
        <!-- 金额小数位降级成2位开关：true 开启 false 关闭 -->
        <laf-config:listener-method key="expressUccConfigCenter.amountScaleDownSwitch" method="setAmountScaleDownSwitch" beanName="expressUccConfigCenter"/>
        <!-- 温层时效开关：true 开启 false 关闭 -->
        <laf-config:listener-method key="expressUccConfigCenter.warmLayerAgingSwitch" method="setWarmLayerAgingSwitch" beanName="expressUccConfigCenter"/>
        <!-- 原单未支付，生成逆向单时需要合并支付的支付方式名单配置，多个,分割。配置的是原单的支付方式 -->
        <laf-config:listener-method key="expressUccConfigCenter.needSumOriginalOrderPaymentWhite" method="setNeedSumOriginalOrderPaymentWhite" beanName="expressUccConfigCenter"/>
        <!-- 根据渠道的调用方的子来源，判断是否需要创建关联关系-->
        <laf-config:listener-method key="expressUccConfigCenter.needCreateRelationSystemSubCaller" method="setNeedCreateRelationSystemSubCaller" beanName="expressUccConfigCenter"/>
        <!-- 根据订单标识字段，判断是否需要创建关联关系-->
        <laf-config:listener-method key="expressUccConfigCenter.needCreateRelationOrderSign" method="setNeedCreateRelationOrderSign" beanName="expressUccConfigCenter"/>
        <!-- 改址单询价增值产品白名单配置 -->
        <laf-config:listener-method key="expressUccConfigCenter.readdressEnquiryAddOnProductWhite" method="setReaddressEnquiryAddOnProductWhite" beanName="expressUccConfigCenter"/>
        <!-- Batrix归因平台记录总开关：true 开启 false 关闭 -->
        <laf-config:listener-method key="expressUccConfigCenter.batrixTracerSwitch" method="setBatrixTracerSwitch" beanName="expressUccConfigCenter"/>
        <!-- Batrix归因平台需要记录的业务场景。多个场景用英文逗号分割。具体值使用 BusinessSceneEnum -->
        <laf-config:listener-method key="expressUccConfigCenter.batrixTracerBusinessScenes" method="setBatrixTracerBusinessScenes" beanName="expressUccConfigCenter"/>
        <!-- 根据京标编码获取国标编码服务开关：true:开启，false:关闭 -->
        <laf-config:listener-method key="expressUccConfigCenter.getGBDistrictByJDCodeSwitch" method="setGetGBDistrictByJDCodeSwitch" beanName="expressUccConfigCenter"/>
        <!-- 营业部资源校验及预占-渠道子来源 空为都支持 -->
        <laf-config:listener-method key="expressUccConfigCenter.c2bDepartmentResourceCheckPreemptSystemSubCaller" method="setC2bDepartmentResourceCheckPreemptSystemSubCaller" beanName="expressUccConfigCenter"/>
        <!-- 允许优惠券和折扣同时使用白名单，多个英文逗号分割 -->
        <laf-config:listener-method key="expressUccConfigCenter.allowTicketDiscountWhite" method="setAllowTicketDiscountWhite" beanName="expressUccConfigCenter"/>
        <!-- ccB2c是否异步写帐开关（true:同步,false:异步）-->
        <laf-config:listener-method key="expressUccConfigCenter.ccB2cOrderBankSyncInitSwitch" method="setCcB2cOrderBankSyncInitSwitch" beanName="expressUccConfigCenter"/>
        <!-- ccB2B是否异步写帐开关（true:同步,false:异步）-->
        <laf-config:listener-method key="expressUccConfigCenter.ccB2BOrderBankSyncInitSwitch" method="setCcB2BOrderBankSyncInitSwitch" beanName="expressUccConfigCenter"/>
        <!-- 快运逆向原单询价增值产品白名单，多个英文逗号分割 -->
        <laf-config:listener-method key="expressUccConfigCenter.freightReverseOriginOrderEnquiryAddOnProductWhite" method="setFreightReverseOriginOrderEnquiryAddOnProductWhite" beanName="expressUccConfigCenter"/>
        <!-- 快运货物重量最小值（单位kg） -->
        <laf-config:listener-method key="expressUccConfigCenter.freightCargoWeightMin" method="setFreightCargoWeightMin" beanName="expressUccConfigCenter"/>
        <!-- 快运货物重量最大值（单位kg） -->
        <laf-config:listener-method key="expressUccConfigCenter.freightCargoWeightMax" method="setFreightCargoWeightMax" beanName="expressUccConfigCenter"/>
        <!-- 快运货物体积最小值（单位cm3） -->
        <laf-config:listener-method key="expressUccConfigCenter.freightCargoVolumeMin" method="setFreightCargoVolumeMin" beanName="expressUccConfigCenter"/>
        <!-- 快运货物体积最大值（单位cm3） -->
        <laf-config:listener-method key="expressUccConfigCenter.freightCargoVolumeMax" method="setFreightCargoVolumeMax" beanName="expressUccConfigCenter"/>
        <!-- 快运台账异步初始化方式 -->
        <laf-config:listener-method key="expressUccConfigCenter.freightOrderBankSyncInitPdqSwitch" method="setFreightOrderBankSyncInitPdqSwitch" beanName="expressUccConfigCenter"/>
        <!-- 快运台账是否同步初始化开关 -->
        <laf-config:listener-method key="expressUccConfigCenter.freightOrderBankSyncInitSwitch" method="setFreightOrderBankSyncInitSwitch" beanName="expressUccConfigCenter"/>
        <!-- b2cKA切量商家青龙业主号白名单-在白名单内的需要写关联关系，当全量需要时赋值全量标识"all"，多个英文逗号分割 -->
        <laf-config:listener-method key="expressUccConfigCenter.kaAccountNoWhite" method="setKaAccountNoWhite" beanName="expressUccConfigCenter"/>
        <!-- c2bKA切量商家青龙业主号白名单-在白名单内的需要写关联关系，当全量需要时赋值全量标识"all"，多个英文逗号分割 -->
        <laf-config:listener-method key="expressUccConfigCenter.c2bKaAccountNoWhite" method="setC2BKaAccountNoWhite" beanName="expressUccConfigCenter"/>
        <!-- 允许货品体积渠道接入来源范围 -->
        <laf-config:listener-method key="expressUccConfigCenter.cargoVolumeSystemCallers" method="setCargoVolumeSystemCallers" beanName="expressUccConfigCenter"/>
        <!-- 询价接口发送询价记录消息开关 -->
        <laf-config:listener-method key="expressUccConfigCenter.sendExpressOrderEnquiryRecordSwitch" method="setSendExpressOrderEnquiryRecordSwitch" beanName="expressUccConfigCenter"/>
        <!-- 快运揽收后改址支付超时时间buffer -->
        <laf-config:listener-method key="expressUccConfigCenter.freightPayOutTimeBuffer" method="setFreightPayOutTimeBuffer" beanName="expressUccConfigCenter"/>
        <!-- 接单持久化超时删除开关 -->
        <laf-config:listener-method key="expressUccConfigCenter.createRepositoryTimeoutDelSwitch" method="setCreateRepositoryTimeoutDelSwitch" beanName="expressUccConfigCenter"/>
        <!-- 快运校验商家是否开通月结 -->
        <laf-config:listener-method key="expressUccConfigCenter.freightValidateMonthlyPaymentSwitch" method="setFreightValidateMonthlyPaymentSwitch" beanName="expressUccConfigCenter"/>
        <!-- 百川B2C重置调用预分拣waybillNo复制逻辑开关 -->
        <laf-config:listener-method key="expressUccConfigCenter.b2cResetPresortRpcFieldSwitch" method="setB2cResetPresortRpcFieldSwitch" beanName="expressUccConfigCenter"/>
        <!-- JMQ支付是否发送消息开关：true 发送，false:不发 -->
        <laf-config:listener-method key="expressUccConfigCenter.sendPayFlowMessageSwitch" method="setSendPayFlowMessageSwitch" beanName="expressUccConfigCenter"/>
        <!-- 快运灰度切量名单 -->
        <laf-config:listener-method key="expressUccConfigCenter.freightPosTypeOrderNos" method="setFreightPosTypeOrderNos" beanName="expressUccConfigCenter"/>
        <!-- 快运预占优惠券和回滚优惠券使用客户订单号开关 -->
        <laf-config:listener-method key="expressUccConfigCenter.freightCouponUseCustomerOrderNoSwitch" method="setFreightCouponUseCustomerOrderNoSwitch" beanName="expressUccConfigCenter"/>
        <!-- 冷链B2B超时取消时间 -->
        <laf-config:listener-method key="expressUccConfigCenter.ccB2BCancelPayTimeOutOrderBuffer" method="setCcB2BCancelPayTimeOutOrderBuffer" beanName="expressUccConfigCenter"/>
        <!-- 合同物流货物重量最小值（单位kg） -->
        <laf-config:listener-method key="expressUccConfigCenter.contractCargoWeightMin" method="setContractCargoWeightMin" beanName="expressUccConfigCenter"/>
        <!-- 合同物流货物重量最大值（单位kg） -->
        <laf-config:listener-method key="expressUccConfigCenter.contractCargoWeightMax" method="setContractCargoWeightMax" beanName="expressUccConfigCenter"/>
        <!-- 合同物流货物体积最小值（单位cm3） -->
        <laf-config:listener-method key="expressUccConfigCenter.contractCargoVolumeMin" method="setContractCargoVolumeMin" beanName="expressUccConfigCenter"/>
        <!-- 合同物流货物体积最大值（单位cm3） -->
        <laf-config:listener-method key="expressUccConfigCenter.contractCargoVolumeMax" method="setContractCargoVolumeMax" beanName="expressUccConfigCenter"/>
        <!-- 合同物流货物数量最小值 -->
        <laf-config:listener-method key="expressUccConfigCenter.contractCargoNumMin" method="setContractCargoNumMin" beanName="expressUccConfigCenter"/>
        <!-- 合同物流货物数量最大值 -->
        <laf-config:listener-method key="expressUccConfigCenter.contractCargoNumMax" method="setContractCargoNumMax" beanName="expressUccConfigCenter"/>
        <!-- 压测数据丢弃总开关 -->
        <laf-config:listener-method key="expressUccConfigCenter.ycAbandonSwitch" method="setYcAbandonSwitch" beanName="expressUccConfigCenter"/>
        <!-- 预热数据跳过总开关 -->
        <laf-config:listener-method key="expressUccConfigCenter.yrSkipSwitch" method="setYrSkipSwitch" beanName="expressUccConfigCenter"/>
        <!-- 港澳策略为仅修改报关数据时所能修改的内容白名单,多个英文逗号分割 -->
        <laf-config:listener-method key="expressUccConfigCenter.expressHKMModifyWhite" method="setExpressHKMModifyWhite" beanName="expressUccConfigCenter"/>
        <!-- 揽收前修改结算方式时需要校验询价状态的systemCaller白名单,多个英文逗号分割 -->
        <laf-config:listener-method key="expressUccConfigCenter.modifyBeforePickedUpValidEnquirySystemCallerWhite" method="setModifyBeforePickedUpValidEnquirySystemCallerWhite" beanName="expressUccConfigCenter"/>
        <!-- POP售后合单开关 -->
        <laf-config:listener-method key="expressUccConfigCenter.popMergeOrderSwitch" method="setPopMergeOrderSwitch" beanName="expressUccConfigCenter"/>
        <!-- 对私pin代扣超时时间:分钟 -->
        <laf-config:listener-method key="expressUccConfigCenter.uepPinPayTradeExpiry" method="setUepPinPayTradeExpiry" beanName="expressUccConfigCenter"/>
        <!-- 合同物流支持写台账事业部 -->
        <laf-config:listener-method key="expressUccConfigCenter.contractOrderBankSupportDeptNos" method="setContractOrderBankSupportDeptNos" beanName="expressUccConfigCenter"/>
        <!-- JMQ退款是否发送消息开关：true 发送，false:不发 -->
        <laf-config:listener-method key="expressUccConfigCenter.sendRefundFlowMessageSwitch" method="setSendRefundFlowMessageSwitch" beanName="expressUccConfigCenter"/>
        <!-- JMQ退款是否发送消息开关：true 发送，false:不发 -->
        <laf-config:listener-method key="expressUccConfigCenter.sendReacceptFlowMessageSwitch" method="setSendReacceptFlowMessageSwitch" beanName="expressUccConfigCenter"/>
        <!-- 零售订单快运执行店铺校验：true-校验，false-不校验 -->
        <laf-config:listener-method key="expressUccConfigCenter.jdOrderValidateFreightPopIdSwitch" method="setJdOrderValidateFreightPopIdSwitch" beanName="expressUccConfigCenter"/>
        <!-- 快运整车直达待销售确认时间 -->
        <laf-config:listener-method key="expressUccConfigCenter.freightFTLWaitSalesConfirmTimeBuffer" method="setFreightFTLWaitSalesConfirmTimeBuffer" beanName="expressUccConfigCenter"/>
        <!-- 港澳税金终端灰度派送站点白名单配置,多个英文逗号分割 -->
        <laf-config:listener-method key="expressUccConfigCenter.taxPDAEndStationWhite" method="setTaxPDAEndStationWhite" beanName="expressUccConfigCenter"/>
        <!-- 改址数据同步跳过开关：true：开启，false：关闭 -->
        <laf-config:listener-method key="expressUccConfigCenter.readdressWaybillUpdateSwitch" method="setReaddressWaybillUpdateSwitch" beanName="expressUccConfigCenter"/>
        <!-- 被替代PDQ主题列表名单 -->
        <laf-config:listener-method key="expressUccConfigCenter.replacingPDQEnabledTopics" method="setReplacingPDQEnabledTopics" beanName="expressUccConfigCenter"/>
        <!-- 快运C2C整车直达支付超时时间 -->
        <laf-config:listener-method key="expressUccConfigCenter.freightC2CFTLPayOutTimeBuffer" method="setFreightC2CFTLPayOutTimeBuffer" beanName="expressUccConfigCenter"/>
        <!-- 快运C2C整车直达待商家确认时间 -->
        <laf-config:listener-method key="expressUccConfigCenter.freightC2CFTLWaitConfirmTimeBuffer" method="setFreightC2CFTLWaitConfirmTimeBuffer" beanName="expressUccConfigCenter"/>
        <!-- 快运整车直达的几个timeBuffer使用的时间单位 -->
        <laf-config:listener-method key="expressUccConfigCenter.timeUnitOfFreightFTLTimeBuffer" method="setTimeUnitOfFreightFTLTimeBuffer" beanName="expressUccConfigCenter"/>
        <!-- 货品明细行数最大值，默认20000 -->
        <laf-config:listener-method key="expressUccConfigCenter.maxCargoListSize" method="setMaxCargoListSize" beanName="expressUccConfigCenter"/>
        <!-- 允许合并新旧产品的业务身份 -->
        <laf-config:listener-method key="expressUccConfigCenter.allowProductsMergeBusinessUnits" method="setAllowProductsMergeBusinessUnits" beanName="expressUccConfigCenter"/>
        <!-- 调用产品映射开关true:开启(默认),false:关闭 -->
        <laf-config:listener-method key="expressUccConfigCenter.invokeProductMappingSwitch" method="setInvokeProductMappingSwitch" beanName="expressUccConfigCenter"/>
        <!-- 图外能力点调用切换的流程图列表,多个英文逗号分割 -->
        <laf-config:listener-method key="expressUccConfigCenter.switchFlowRunningOnlyList" method="setSwitchFlowRunningOnlyList" beanName="expressUccConfigCenter"/>
        <!-- 广播消息转换时是否无条件广播presortExtend -->
        <laf-config:listener-method key="expressUccConfigCenter.broadcastConverterPresortExtendSwitch" method="setBroadcastConverterPresortExtendSwitch" beanName="expressUccConfigCenter"/>
        <!-- 快运后款支付（终端发起）能否取消服务询价单开关 -->
        <laf-config:listener-method key="expressUccConfigCenter.cashOnDeliveryCancelServiceEnquirySwitch" method="setCashOnDeliveryCancelServiceEnquirySwitch" beanName="expressUccConfigCenter"/>
        <!-- 大件异步发送消息广播 -->
        <laf-config:listener-method key="expressUccConfigCenter.lasAsyncSendJMQ" method="setLasAsyncSendJMQ" beanName="expressUccConfigCenter"/>
        <!-- 不同步运单属性信息开关 -->
        <laf-config:listener-method key="expressUccConfigCenter.notUpdatePackagingAttributesSwitch" method="setNotUpdatePackagingAttributesSwitch" beanName="expressUccConfigCenter"/>
        <!-- 调用运单号释放开关。true:开启(默认),false:关闭 -->
        <laf-config:listener-method key="expressUccConfigCenter.waybillNoReleaseSwitch" method="setWaybillNoReleaseSwitch" beanName="expressUccConfigCenter"/>
        <!-- uep合单订单防重锁写入开关 -->
        <laf-config:listener-method key="expressUccConfigCenter.mergedOrderAntiRepeat" method="setMergedOrderAntiRepeat" beanName="expressUccConfigCenter"/>
        <!-- 校验云仓vmi账号开关 -->
        <laf-config:listener-method key="expressUccConfigCenter.clpsVmiCustomerConfigSwitch" method="setClpsVmiCustomerConfigSwitch" beanName="expressUccConfigCenter"/>
        <!-- 支付截止时间配置 -->
        <laf-config:listener-method key="expressUccConfigCenter.documentSendPayTimeout" method="setDocumentSendPayTimeout" beanName="expressUccConfigCenter"/>
        <!-- 快运C2C整车直达取消订单是否自动核销 -->
        <laf-config:listener-method key="expressUccConfigCenter.freightC2CFTLAutoWriteOffSwitch" method="setFreightC2CFTLAutoWriteOffSwitch" beanName="expressUccConfigCenter"/>
        <!-- 自动核销事业部编码 -->
        <laf-config:listener-method key="expressUccConfigCenter.autoWriteOffAccountNos" method="setAutoWriteOffAccountNos" beanName="expressUccConfigCenter"/>
        <!-- 放开自行联系揽收前修改卡控的开关： true 卡控，false 不卡控。默认不卡控 -->
        <laf-config:listener-method key="expressUccConfigCenter.frightContactDirectlyInsOrDelBAP" method="setFrightContactDirectlyInsOrDelBAP" beanName="expressUccConfigCenter"/>
        <!-- 有条件的调用询价接口停止重试：true 开启 false 关闭 默认开启 -->
        <laf-config:listener-method key="expressUccConfigCenter.stopRetryQlLLComputeServiceComputeSwitch" method="setStopRetryQlLLComputeServiceComputeSwitch" beanName="expressUccConfigCenter"/>
        <!-- 快运询价新流程开关。true-新流程，false-旧流程 -->
        <laf-config:listener-method key="expressUccConfigCenter.freightNewEnquiryProcessSwitch" method="setFreightNewEnquiryProcessSwitch" beanName="expressUccConfigCenter"/>
        <!-- 快运数据同步是否校验修改并发锁。true-校验并发锁；false-不校验并发锁 -->
        <laf-config:listener-method key="expressUccConfigCenter.freightUpdateHandlerModifyLockKeySwitch" method="setFreightUpdateHandlerModifyLockKeySwitch" beanName="expressUccConfigCenter"/>
        <!-- 快运数据同步是否校验台账操作并发锁。true-校验并发锁；false-不校验并发锁 -->
        <laf-config:listener-method key="expressUccConfigCenter.freightUpdateHandlerOrderBankLockKeySwitch" method="setFreightUpdateHandlerOrderBankLockKeySwitch" beanName="expressUccConfigCenter"/>
        <!-- 快运B2C整车直达切换到CRM调价：true-新流程CRM调价；false-旧流程ECLP调价；默认false -->
        <laf-config:listener-method key="expressUccConfigCenter.freightEnquiryQuoteNoticeCRM" method="setFreightEnquiryQuoteNoticeCRM" beanName="expressUccConfigCenter"/>
    </laf-config:manager>

    <bean id="orderStatusConfigUtil" class="cn.jdl.oms.express.shared.common.utils.OrderStatusConfigUtil">
        <!--可直接取消的状态-->
        <constructor-arg name="immediateCancelStatus" index="0">
            <map>
                <entry>
                    <!--key {租户:业务单元:业务类型} c2c -->
                    <key>
                        <value>1000:cn_jdl_c2c:express</value>
                    </key>
                    <value>100</value>
                </entry>
                <entry>
                    <!--key {租户:业务单元:业务类型} o2o -->
                    <key>
                        <value>1000:cn_jdl_o2o:express</value>
                    </key>
                    <value>100</value>
                </entry>
            </map>
        </constructor-arg>
    </bean>

    <bean id="batrixSwitch" class="cn.jdl.oms.express.shared.common.utils.BatrixSwitch">
        <constructor-arg name="duccAppName" value="batrixSwitch"/>
        <constructor-arg name="uri" value="ucc://${express.ducc.appName}:${express.ducc.token}@${express.ducc.uri}/v1/namespace/${express.ducc.namespace}/config/${express.ducc.propertiesConfig}/profiles/${express.ducc.profile}?longPolling=60000&amp;necessary=true"/>
    </bean>
</beans>
