<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:task="http://www.springframework.org/schema/task"
       xmlns:jmq="http://code.jd.com/schema/jmq"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
	http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
	http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd
	http://code.jd.com/schema/jmq http://code.jd.com/schema/jmq/jmq-1.1.xsd
    http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd
	http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-3.0.xsd">

    <!--JMQ 加载配置文件-->
    <import resource="classpath:spring/jmq/applicationContext-jmq-bean.xml"/>

    <!--接受消息解析监听器-->
    <bean id="jmqMessageListener" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.container.JMQMessageListener">
        <property name="messageContainer" ref="jmqMessageContainer"/>
        <property name="exceptionIntecepter" ref="exceptionIntecepter"/>
        <property name="messageIntecepter" ref="messageIntercepter"/>
        <property name="jmqMessageFilter" ref="jmqMessageFilter"/>
    </bean>

    <!-- 基于环境配置的topic过滤器 -->
    <bean id="jmqMessageFilter" class="cn.jdl.oms.express.domain.infrs.ohs.locals.filter.JMQMessageFilter">
        <!-- messageHandle -->
        <constructor-arg index="0">
            <!--  线上环境设置环境  -->
            <util:map key-type="java.lang.String">
                <entry key="ONLINE">
                    <util:map>
                        <!--  设置Topic  -->
                        <entry key="${jdl.oms.express.waybill.update.info}">
                            <util:map>
                                <entry key="JMQ_QL_IS_UAT" value="false"/>
                            </util:map>
                        </entry>
                        <!--  设置Topic  -->
                        <entry key="${jdl.oms.express.ldop.package.number}">
                            <util:map>
                                <entry key="JMQ_QL_IS_UAT" value="false"/>
                            </util:map>
                        </entry>
                        <entry key="${jdl.oms.express.terminal.notify.destination}">
                            <util:map>
                                <entry key="JMQ_QL_IS_UAT" value="false"/>
                            </util:map>
                        </entry>
                    </util:map>
                </entry>
                <entry key="PRE">
                    <util:map>
                        <!--  设置Topic  -->
                        <entry key="${jdl.oms.express.waybill.update.info}">
                            <util:map>
                                <entry key="JMQ_QL_IS_UAT" value="true"/>
                            </util:map>
                        </entry>
                        <entry key="${jdl.oms.express.terminal.notify.destination}">
                            <util:map>
                                <entry key="JMQ_QL_IS_UAT" value="true"/>
                            </util:map>
                        </entry>
                        <entry key="${jdl.oms.express.ldop.package.number}">
                            <util:map>
                                <entry key="JMQ_QL_IS_UAT" value="true"/>
                            </util:map>
                        </entry>
                    </util:map>
                </entry>
                <!--  测试环境设置环境  -->
                <entry key="TEST">
                    <util:map>
                        <!--  设置Topic  -->
                        <entry key="${jdl.oms.express.waybill.update.info}">
                            <util:map>
                                <entry key="JMQ_QL_IS_UAT" value="false"/>
                            </util:map>
                        </entry>
                        <entry key="${jdl.oms.express.terminal.notify.destination}">
                            <util:map>
                                <entry key="JMQ_QL_IS_UAT" value="false"/>
                            </util:map>
                        </entry>
                    </util:map>
                </entry>
            </util:map>
        </constructor-arg>
        <property name="environment" value="${jmq.environment}"/>
    </bean>

    <jmq:consumer id="consumer" transport="jmq.transport">
        <!-- 监听订单ECLP拦截反馈结果  -->
        <jmq:listener topic="${jdl.oms.express.delete.record.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.callback.record.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.cancel.record.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.create.record.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.modify.record.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.order.bank.record.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.jd.pay.success.notify.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.waybill.update.info}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <!-- 监听外单退款完成消息 -->
        <!--https://cf.jd.com/pages/viewpage.action?pageId=*********-->
        <jmq:listener topic="${jdl.oms.express.order.refund}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <!--订单超时取消-->
        <jmq:listener topic="${jdl.oms.express.cancel.pay.timeout.order.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <!--pdq异常重试-->
        <jmq:listener topic="${jdl.oms.express.retry.order.pdq.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.reconciliation.success.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.c2c.orderBank.init.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.tms.enquiry.cancel.notify.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.tms.enquiry.confirm.back.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.cold.chain.waybill.unpaid.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.pay.return.info.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.ldop.middle.enquiry.bill.back.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.tms.enquiry.vehicle.driver.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.ldop.package.number}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.terminal.notify.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
    </jmq:consumer>

    <!--jmq4 topic 消费-->
    <jmq:consumer id="consumerJmq4" transport="jmq4.transport">
        <!--jmq4 topic 消费-->
        <jmq:listener topic="${jdl.oms.express.enquiry.record.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.intercept.record.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.pay.record.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.recover.record.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.reaccept.record.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.b2c.orderBank.init.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.c2b.orderBank.init.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.freight.orderBank.init.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.order.status.notify.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.cc.fee.info.result.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.enquiry.record.new.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.customs.status.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.order.tax.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.refund.record.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.order.info.update}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.exception.retry.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.wait.sales.confirm.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.order.resource.release.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.order.readdress.async.enquiry.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
<!--        <jmq:listener topic="${jdl.oms.express.pay.timeout.order.destination}" listener="jmqMessageListener"/>-->
        <jmq:listener topic="${jdl.oms.express.order.invoice.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.order.common.async.enquiry.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.order.ccb2b.async.enquiry.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.order.common.async.release.coupon.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.order.common.wechat.payment.retry.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.order.common.refund.order.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.order.common.integral.release.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.order.common.ious.release.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.order.common.ious.modify.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.order.b2c.clear.order.bank.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.order.common.freight.reverse.order.bank.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.order.common.waybill.refund.order.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.order.ccb2b.async.enquiry.order.bank.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.order.common.push.invoice.info.uep.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.order.common.create.tms.enquiry.bill.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.order.zc.ccb2b.issue.persist.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.order.zc.ccb2b.cancel.persist.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.order.zc.ccb2b.init.order.bank.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.order.zc.ccb2b.create.persist.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.order.push.ebs.info.freight.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.order.pos.pay.qr.close.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.order.tms.clear.order.bank.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.order.auto.write.off.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.order.clear.order.bank.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.ldop.fee.discount.detail.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.terminal.receive.pay.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.precharge.occupy.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.push.ebs.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.order.retry.repository.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.cancel.service.order}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.standard.product.and.discount.enquiry.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.create.payment.order.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.release.payment.order.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.outbound.to.eds.package.info.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.delivery.pickup.sync.bind.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
        <jmq:listener topic="${jdl.oms.express.ecap.fee.details.notice.destination}" listener="jmqMessageListener" shadowListener="jmqMessageListener"/>
    </jmq:consumer>
</beans>
