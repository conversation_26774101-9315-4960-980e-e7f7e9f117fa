<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:util="http://www.springframework.org/schema/util"
       xmlns:task="http://www.springframework.org/schema/task"
       xmlns:jmq="http://code.jd.com/schema/jmq"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
            http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd
            http://code.jd.com/schema/jmq http://code.jd.com/schema/jmq/jmq-1.1.xsd
            http://www.springframework.org/schema/context  http://www.springframework.org/schema/context/spring-context-3.0.xsd
	        http://www.springframework.org/schema/util  http://www.springframework.org/schema/util/spring-util-3.1.xsd">
    <!-- 消息处理器引入 -->
    <import resource="applicationContext-handler.xml"/>

    <!--JSON 消息体解析parser -->
    <bean id="jsonParser" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.processor.impl.JsonMessageParser"/>

    <!--xml 消息体解析parser -->
    <bean id="xmlParser" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.processor.impl.XmlMessageParser"/>

    <!--消息处理异常-->
    <bean id="exceptionIntecepter"
          class="cn.jdl.oms.express.horz.infrs.ohs.locals.message.intercepter.ExceptionIntecepterImpl"/>

    <!--消息处理容器-->
    <bean id="jmqMessageContainer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.container.JMQMessageContainer">
        <!-- messageHandle -->
        <constructor-arg index="0">
            <map key-type="java.lang.String">
                <entry key="${jdl.oms.express.create.record.destination}" value-ref="expressOrderFlowHandler"/>
                <entry key="${jdl.oms.express.delete.record.destination}" value-ref="expressOrderFlowHandler"/>
                <entry key="${jdl.oms.express.callback.record.destination}" value-ref="expressOrderFlowHandler"/>
                <entry key="${jdl.oms.express.modify.record.destination}" value-ref="expressOrderFlowHandler"/>
                <entry key="${jdl.oms.express.cancel.record.destination}" value-ref="expressOrderFlowHandler"/>
                <entry key="${jdl.oms.express.waybill.update.info}" value-ref="waybillInfoUpdateHandler"/>
                <entry key="${jdl.oms.express.order.bank.record.destination}" value-ref="orderBankFlowHandler"/>
                <entry key="${jdl.oms.express.jd.pay.success.notify.destination}" value-ref="jdPaySuccessNotifyHandler"/>
                <entry key="${jdl.oms.express.order.refund}" value-ref="refundMessageHandler"/>
                <!--超时取消MQ-->
                <entry key="${jdl.oms.express.cancel.pay.timeout.order.destination}" value-ref="cancelPayTimeOutOrderHandler"/>
                <entry key="${jdl.oms.express.retry.order.pdq.destination}" value-ref="retryExpressOrderPDQHandler"/>
                <entry key="${jdl.oms.express.enquiry.record.destination}" value-ref="expressOrderFlowHandler"/>
                <entry key="${jdl.oms.express.intercept.record.destination}" value-ref="expressOrderFlowHandler"/>
                <entry key="${jdl.oms.express.pay.record.destination}" value-ref="expressOrderFlowHandler"/>
                <entry key="${jdl.oms.express.recover.record.destination}" value-ref="expressOrderFlowHandler"/>
                <entry key="${jdl.oms.express.reaccept.record.destination}" value-ref="expressOrderFlowHandler"/>
                <entry key="${jdl.oms.express.reconciliation.success.destination}" value-ref="reconciliationHandler"/>
                <entry key="${jdl.oms.express.c2c.orderBank.init.destination}" value-ref="c2CInitOrderBankJmqHandler"/>
                <entry key="${jdl.oms.express.b2c.orderBank.init.destination}" value-ref="b2CInitOrderBankJmqHandler"/>
                <entry key="${jdl.oms.express.c2b.orderBank.init.destination}" value-ref="c2BInitOrderBankJmqHandler"/>
                <entry key="${jdl.oms.express.freight.orderBank.init.destination}" value-ref="freightInitOrderBankJmqHandler"/>
                <!-- 状态流水MQ -->
                <entry key="${jdl.oms.express.order.status.notify.destination}" value-ref="orderStatusNotifyAsyncHandler"/>
                <entry key="${jdl.oms.express.cc.fee.info.result.destination}" value-ref="ccFeeInfoResultJmqHandler"/>
                <entry key="${jdl.oms.express.enquiry.record.new.destination}" value-ref="orderEnquiryRecordHandler"/>
                <entry key="${jdl.oms.express.customs.status.destination}" value-ref="expressCustomsStatusJmqHandler"/>
                <entry key="${jdl.oms.express.order.tax.destination}" value-ref="expressOrderTaxJmqHandler"/>
                <entry key="${jdl.oms.express.refund.record.destination}" value-ref="expressOrderFlowHandler"/>
                <entry key="${jdl.oms.express.order.info.update}" value-ref="expressOrderInfoUpdateHandler"/>
                <entry key="${jdl.oms.express.exception.retry.destination}" value-ref="expressRetryHandler"/>
                <entry key="${jdl.oms.express.tms.enquiry.cancel.notify.destination}" value-ref="tmsEnquiryCancelNotifyJmqHandler"/>
                <entry key="${jdl.oms.express.tms.enquiry.confirm.back.destination}" value-ref="tmsEnquiryConfirmBackJmqHandler"/>
                <entry key="${jdl.oms.express.cold.chain.waybill.unpaid.destination}" value-ref="coldChainWayBillUnpaidHandler"/>
                <entry key="${jdl.oms.express.pay.return.info.destination}" value-ref="payReturnInfoHandler"/>
                <entry key="${jdl.oms.express.wait.sales.confirm.destination}" value-ref="waitSalesConfirmJmqHandler"/>
                <entry key="${jdl.oms.express.ldop.middle.enquiry.bill.back.destination}" value-ref="tmsEnquiryQuoteConfirmJmqHandler"/>
                <entry key="${jdl.oms.express.tms.enquiry.vehicle.driver.destination}" value-ref="enquiryVehicleDriverJmqHandler"/>
                <entry key="${jdl.oms.express.order.resource.release.destination}" value-ref="expressResourceReleaseHandler"/>
                <entry key="${jdl.oms.express.order.readdress.async.enquiry.destination}" value-ref="readdressAsyncEnquiryHandler"/>
                <entry key="${jdl.oms.express.pay.timeout.order.destination}" value-ref="cancelPayTimeOutOrderHandler"/>
                <entry key="${jdl.oms.express.order.invoice.destination}" value-ref="expressPushInvoiceHandler"/>
                <entry key="__retry_${jdl.oms.express.order.invoice.destination}_${jmq4.userName}" value-ref="expressPushInvoiceHandler"/>
                <entry key="${jdl.oms.express.order.common.async.enquiry.destination}" value-ref="commonAsyncEnquiryHandler"/>
                <entry key="${jdl.oms.express.order.ccb2b.async.enquiry.destination}" value-ref="genericJMQToPDQAdaptorHandler"/>
                <entry key="${jdl.oms.express.order.common.async.release.coupon.destination}" value-ref="genericJMQToPDQAdaptorHandler"/>
                <entry key="${jdl.oms.express.order.common.wechat.payment.retry.destination}" value-ref="genericJMQToPDQAdaptorHandler"/>
                <entry key="${jdl.oms.express.order.common.refund.order.destination}" value-ref="genericJMQToPDQAdaptorHandler"/>
                <entry key="${jdl.oms.express.order.common.integral.release.destination}" value-ref="genericJMQToPDQAdaptorHandler"/>
                <entry key="${jdl.oms.express.order.common.ious.release.destination}" value-ref="genericJMQToPDQAdaptorHandler"/>
                <entry key="${jdl.oms.express.order.common.ious.modify.destination}" value-ref="genericJMQToPDQAdaptorHandler"/>
                <entry key="${jdl.oms.express.order.b2c.clear.order.bank.destination}" value-ref="genericJMQToPDQAdaptorHandler"/>
                <entry key="${jdl.oms.express.order.common.freight.reverse.order.bank.destination}" value-ref="genericJMQToPDQAdaptorHandler"/>
                <entry key="${jdl.oms.express.order.common.waybill.refund.order.destination}" value-ref="genericJMQToPDQAdaptorHandler"/>
                <entry key="${jdl.oms.express.order.ccb2b.async.enquiry.order.bank.destination}" value-ref="genericJMQToPDQAdaptorHandler"/>
                <entry key="${jdl.oms.express.order.common.push.invoice.info.uep.destination}" value-ref="genericJMQToPDQAdaptorHandler"/>
                <entry key="${jdl.oms.express.order.common.create.tms.enquiry.bill.destination}" value-ref="genericJMQToPDQAdaptorHandler"/>
                <entry key="${jdl.oms.express.order.zc.ccb2b.issue.persist.destination}" value-ref="genericJMQToPDQAdaptorHandler"/>
                <entry key="${jdl.oms.express.order.zc.ccb2b.cancel.persist.destination}" value-ref="genericJMQToPDQAdaptorHandler"/>
                <entry key="${jdl.oms.express.order.zc.ccb2b.init.order.bank.destination}" value-ref="genericJMQToPDQAdaptorHandler"/>
                <entry key="${jdl.oms.express.order.zc.ccb2b.create.persist.destination}" value-ref="genericJMQToPDQAdaptorHandler"/>
                <entry key="${jdl.oms.express.order.push.ebs.info.freight.destination}" value-ref="genericJMQToPDQAdaptorHandler"/>
                <entry key="${jdl.oms.express.order.pos.pay.qr.close.destination}" value-ref="genericJMQToPDQAdaptorHandler"/>
                <entry key="${jdl.oms.express.order.tms.clear.order.bank.destination}" value-ref="genericJMQToPDQAdaptorHandler"/>
                <entry key="${jdl.oms.express.order.auto.write.off.destination}" value-ref="genericJMQToPDQAdaptorHandler"/>
                <entry key="${jdl.oms.express.order.clear.order.bank.destination}" value-ref="genericJMQToPDQAdaptorHandler"/>
                <entry key="${jdl.oms.express.ldop.fee.discount.detail.destination}" value-ref="ldopFeeDiscountDetailHandler"/>
                <entry key="${jdl.oms.express.ldop.package.number}" value-ref="ldopPackageNumberHandler"/>
                <!-- 终端揽收成功MQ -->
                <entry key="${jdl.oms.express.terminal.notify.destination}" value-ref="terminalPickedUpNotifyHandler"/>
                <entry key="${jdl.oms.express.terminal.receive.pay.destination}" value-ref="terminalReceivePayFinishHandler"/>
                <entry key="${jdl.oms.express.precharge.occupy.destination}" value-ref="prechargeOccupyHandler"/>
                <entry key="${jdl.oms.express.push.ebs.destination}" value-ref="omsExpressPushEbsHandler"/>
                <entry key="${jdl.oms.express.order.retry.repository.destination}" value-ref="omsExpressRetryOrderJmqHandler"/>
                <entry key="${jdl.oms.express.cancel.service.order}" value-ref="asyncCancelOrderHandler"/>
                <entry key="${jdl.oms.express.create.payment.order.destination}" value-ref="omsExpressCreatePaymentOrderHandler"/>
                <entry key="${jdl.oms.express.release.payment.order.destination}" value-ref="omsExpressReleasePaymentOrderHandler"/>
                <entry key="${jdl.oms.express.standard.product.and.discount.enquiry.destination}" value-ref="asyncStandardProductAndDiscountInquiryHandler"/>
                <entry key="${jdl.oms.express.outbound.to.eds.package.info.destination}" value-ref="outboundPackageUpdateHandler"/>
                <entry key="${jdl.oms.express.delivery.pickup.sync.bind.destination}" value-ref="deliveryPickupSyncBindJmqHandler"/>
                <entry key="${jdl.oms.express.ecap.fee.details.notice.destination}" value-ref="ecapFeeDetailsNoticeHandler"/>
            </map>
        </constructor-arg>
        <property name="exceptionIntecepter" ref="exceptionIntecepter"/>
        <property name="messageParseContainer" ref="jmqMessageParseContainer"/>
    </bean>

    <!--消息解析容器-->
    <bean id="jmqMessageParseContainer"
          class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.container.JMQMessageParseContainer">
        <constructor-arg index="0">
            <map>
                <entry key="${jdl.oms.express.create.record.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ExpressOrderFlowMessageDto"/>
                <entry key="${jdl.oms.express.delete.record.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ExpressOrderFlowMessageDto" />
                <entry key="${jdl.oms.express.callback.record.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ExpressOrderFlowMessageDto"/>
                <entry key="${jdl.oms.express.modify.record.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ExpressOrderFlowMessageDto" />
                <entry key="${jdl.oms.express.cancel.record.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ExpressOrderFlowMessageDto"/>
                <entry key="${jdl.oms.express.waybill.update.info}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.waybill.WaybillInfoDto"/>
                <entry key="${jdl.oms.express.order.bank.record.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.OrderBankFlowDto"/>
                <entry key="${jdl.oms.express.jd.pay.success.notify.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.JdPaySuccessNotifyDto"/>
                <entry key="${jdl.oms.express.order.refund}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.OutSideRefundMessageDto"/>
                <entry key="${jdl.oms.express.cancel.pay.timeout.order.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.PayTimeoutCancelMessageDto"/>
                <entry key="${jdl.oms.express.retry.order.pdq.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.RetryExpressOrderPdqDto"/>
                <entry key="${jdl.oms.express.enquiry.record.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ExpressOrderFlowMessageDto"/>
                <entry key="${jdl.oms.express.intercept.record.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ExpressOrderFlowMessageDto"/>
                <entry key="${jdl.oms.express.pay.record.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ExpressOrderFlowMessageDto"/>
                <entry key="${jdl.oms.express.recover.record.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ExpressOrderFlowMessageDto"/>
                <entry key="${jdl.oms.express.reaccept.record.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ExpressOrderFlowMessageDto"/>
                <entry key="${jdl.oms.express.reconciliation.success.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ReconciliationDto"/>
                <entry key="${jdl.oms.express.c2c.orderBank.init.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.OrderBankInitJmqMessageDto"/>
                <entry key="${jdl.oms.express.b2c.orderBank.init.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.OrderBankInitJmqMessageDto"/>
                <entry key="${jdl.oms.express.c2b.orderBank.init.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.OrderBankInitJmqMessageDto"/>
                <entry key="${jdl.oms.express.freight.orderBank.init.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.OrderBankInitJmqMessageDto"/>
                <entry key="${jdl.oms.express.order.status.notify.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.OrderStatusNotifyMessageDto"/>
                <entry key="${jdl.oms.express.cc.fee.info.result.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.CCFeeInfoResultJmqMessageDto"/>
                <entry key="${jdl.oms.express.tms.enquiry.cancel.notify.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.TmsEnquiryCancelNotifyMessageDto"/>
                <entry key="${jdl.oms.express.tms.enquiry.confirm.back.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.TmsEnquiryConfirmBackMessageDto"/>
                <entry key="${jdl.oms.express.enquiry.record.new.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ExpressOrderFlowMessageDto" />
                <entry key="${jdl.oms.express.customs.status.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ExpressCustomsStatusMessageDto"/>
                <entry key="${jdl.oms.express.order.tax.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ExpressOrderTaxMessageDto"/>
                <entry key="${jdl.oms.express.refund.record.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ExpressOrderFlowMessageDto"/>
                <entry key="${jdl.oms.express.order.info.update}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.OrderDataFlowDto"/>
                <entry key="${jdl.oms.express.exception.retry.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ExpressRetryMsgDto"/>
                <entry key="${jdl.oms.express.cold.chain.waybill.unpaid.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ColdChainWaybillUnpaidMessageDto"/>
                <entry key="${jdl.oms.express.pay.return.info.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.PayReturnInfoMessageDto"/>
                <entry key="${jdl.oms.express.wait.sales.confirm.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.WaitSalesConfirmMessageDto"/>
                <entry key="${jdl.oms.express.ldop.middle.enquiry.bill.back.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.TmsEnquiryQuoteConfirmMessageDto"/>
                <entry key="${jdl.oms.express.tms.enquiry.vehicle.driver.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.EnquiryVehicleDriverJmqMessageDto"/>
                <entry key="${jdl.oms.express.order.resource.release.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ExpressResourceReleaseMsgDto"/>
                <entry key="${jdl.oms.express.order.readdress.async.enquiry.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ReaddressAsyncEnquiryMsgDto"/>
                <entry key="${jdl.oms.express.pay.timeout.order.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.PayTimeoutCancelMessageDto"/>
                <entry key="${jdl.oms.express.order.invoice.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.CommonJmqMessageDto"/>
                <entry key="__retry_${jdl.oms.express.order.invoice.destination}_${jmq4.userName}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.CommonJmqMessageDto"/>
                <entry key="${jdl.oms.express.order.common.async.enquiry.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.enquiry.CommonAsyncEnquiryJmqMessageDto"/>
                <entry key="${jdl.oms.express.order.ccb2b.async.enquiry.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.JmqWrapperOfPdqDto"/>
                <entry key="${jdl.oms.express.order.common.async.release.coupon.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.JmqWrapperOfPdqDto"/>
                <entry key="${jdl.oms.express.order.common.wechat.payment.retry.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.JmqWrapperOfPdqDto"/>
                <entry key="${jdl.oms.express.order.common.refund.order.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.JmqWrapperOfPdqDto"/>
                <entry key="${jdl.oms.express.order.common.integral.release.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.JmqWrapperOfPdqDto"/>
                <entry key="${jdl.oms.express.order.common.ious.release.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.JmqWrapperOfPdqDto"/>
                <entry key="${jdl.oms.express.order.common.ious.modify.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.JmqWrapperOfPdqDto"/>
                <entry key="${jdl.oms.express.order.b2c.clear.order.bank.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.JmqWrapperOfPdqDto"/>
                <entry key="${jdl.oms.express.order.common.freight.reverse.order.bank.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.JmqWrapperOfPdqDto"/>
                <entry key="${jdl.oms.express.order.common.waybill.refund.order.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.JmqWrapperOfPdqDto"/>
                <entry key="${jdl.oms.express.order.ccb2b.async.enquiry.order.bank.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.JmqWrapperOfPdqDto"/>
                <entry key="${jdl.oms.express.order.common.push.invoice.info.uep.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.JmqWrapperOfPdqDto"/>
                <entry key="${jdl.oms.express.order.common.create.tms.enquiry.bill.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.JmqWrapperOfPdqDto"/>
                <entry key="${jdl.oms.express.order.zc.ccb2b.issue.persist.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.JmqWrapperOfPdqDto"/>
                <entry key="${jdl.oms.express.order.zc.ccb2b.cancel.persist.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.JmqWrapperOfPdqDto"/>
                <entry key="${jdl.oms.express.order.zc.ccb2b.init.order.bank.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.JmqWrapperOfPdqDto"/>
                <entry key="${jdl.oms.express.order.zc.ccb2b.create.persist.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.JmqWrapperOfPdqDto"/>
                <entry key="${jdl.oms.express.order.push.ebs.info.freight.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.JmqWrapperOfPdqDto"/>
                <entry key="${jdl.oms.express.order.pos.pay.qr.close.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.JmqWrapperOfPdqDto"/>
                <entry key="${jdl.oms.express.order.tms.clear.order.bank.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.JmqWrapperOfPdqDto"/>
                <entry key="${jdl.oms.express.order.auto.write.off.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.JmqWrapperOfPdqDto"/>
                <entry key="${jdl.oms.express.order.clear.order.bank.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.JmqWrapperOfPdqDto"/>
                <entry key="${jdl.oms.express.ldop.fee.discount.detail.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.LdopFeeDiscountDetailMessageDto"/>
                <entry key="${jdl.oms.express.ldop.package.number}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.LdopPackageNumberMessageDto"/>
                <entry key="${jdl.oms.express.terminal.receive.pay.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.TerminalMessageDto"/>
                <entry key="${jdl.oms.express.terminal.notify.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.TerminalMessageDto"/>
                <entry key="${jdl.oms.express.precharge.occupy.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.CommonJmqMessageDto"/>
                <entry key="${jdl.oms.express.push.ebs.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.PushEBSJmqMessageDto"/>
                <entry key="${jdl.oms.express.order.retry.repository.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.RetryOrderJmqMessageDto"/>
                <entry key="${jdl.oms.express.cancel.service.order}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.CancelOrderMessageDto"/>
                <entry key="${jdl.oms.express.create.payment.order.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.CreatePaymentOrderMessageDto"/>
                <entry key="${jdl.oms.express.release.payment.order.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ReleasePaymentOrderMessageDto"/>
                <entry key="${jdl.oms.express.standard.product.and.discount.enquiry.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.CommonJmqMessageDto"/>
                <entry key="${jdl.oms.express.outbound.to.eds.package.info.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.OutboundToEdsPackageInfoMessageDto"/>
                <entry key="${jdl.oms.express.delivery.pickup.sync.bind.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.DeliveryPickupSyncBindMessageDto"/>
                <entry key="${jdl.oms.express.ecap.fee.details.notice.destination}" value="cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.EcapFeeDetailsNoticeMessageDto"/>
            </map>
        </constructor-arg>
        <constructor-arg index="1">
            <map>
                <entry key="${jdl.oms.express.create.record.destination}" value="1" />
                <entry key="${jdl.oms.express.delete.record.destination}" value="1"/>
                <entry key="${jdl.oms.express.callback.record.destination}" value="1" />
                <entry key="${jdl.oms.express.modify.record.destination}" value="1" />
                <entry key="${jdl.oms.express.cancel.record.destination}" value="1" />
                <entry key="${jdl.oms.express.waybill.update.info}" value="1"/>
                <entry key="${jdl.oms.express.order.bank.record.destination}" value="1"/>
                <entry key="${jdl.oms.express.jd.pay.success.notify.destination}" value="1"/>
                <entry key="${jdl.oms.express.order.refund}" value="1"/>
                <entry key="${jdl.oms.express.cancel.pay.timeout.order.destination}" value="1"/>
                <entry key="${jdl.oms.express.retry.order.pdq.destination}" value="1"/>
                <entry key="${jdl.oms.express.enquiry.record.destination}" value="1"/>
                <entry key="${jdl.oms.express.intercept.record.destination}" value="1"/>
                <entry key="${jdl.oms.express.pay.record.destination}" value="1"/>
                <entry key="${jdl.oms.express.recover.record.destination}" value="1"/>
                <entry key="${jdl.oms.express.reaccept.record.destination}" value="1"/>
                <entry key="${jdl.oms.express.reconciliation.success.destination}" value="1"/>
                <entry key="${jdl.oms.express.c2c.orderBank.init.destination}" value="1"/>
                <entry key="${jdl.oms.express.b2c.orderBank.init.destination}" value="1"/>
                <entry key="${jdl.oms.express.c2b.orderBank.init.destination}" value="1"/>
                <entry key="${jdl.oms.express.freight.orderBank.init.destination}" value="1"/>
                <entry key="${jdl.oms.express.order.status.notify.destination}" value="1" />
                <entry key="${jdl.oms.express.cc.fee.info.result.destination}" value="1"/>
                <entry key="${jdl.oms.express.enquiry.record.new.destination}" value="1" />
                <entry key="${jdl.oms.express.customs.status.destination}" value="1"/>
                <entry key="${jdl.oms.express.order.tax.destination}" value="1" />
                <entry key="${jdl.oms.express.refund.record.destination}" value="1"/>
                <entry key="${jdl.oms.express.order.info.update}" value="1"/>
                <entry key="${jdl.oms.express.exception.retry.destination}" value="1"/>
                <entry key="${jdl.oms.express.tms.enquiry.cancel.notify.destination}" value="1"/>
                <entry key="${jdl.oms.express.tms.enquiry.confirm.back.destination}" value="1"/>
                <entry key="${jdl.oms.express.cold.chain.waybill.unpaid.destination}" value="1"/>
                <entry key="${jdl.oms.express.pay.return.info.destination}" value="1"/>
                <entry key="${jdl.oms.express.wait.sales.confirm.destination}" value="1"/>
                <entry key="${jdl.oms.express.ldop.middle.enquiry.bill.back.destination}" value="1"/>
                <entry key="${jdl.oms.express.tms.enquiry.vehicle.driver.destination}" value="1"/>
                <entry key="${jdl.oms.express.order.resource.release.destination}" value="1"/>
                <entry key="${jdl.oms.express.order.readdress.async.enquiry.destination}" value="1"/>
                <entry key="${jdl.oms.express.pay.timeout.order.destination}" value="1"/>
                <entry key="${jdl.oms.express.order.invoice.destination}" value="1"/>
                <entry key="__retry_${jdl.oms.express.order.invoice.destination}_${jmq4.userName}" value="1"/>
                <entry key="${jdl.oms.express.order.common.async.enquiry.destination}" value="1"/>
                <entry key="${jdl.oms.express.order.ccb2b.async.enquiry.destination}" value="1"/>
                <entry key="${jdl.oms.express.order.common.async.release.coupon.destination}" value="1"/>
                <entry key="${jdl.oms.express.order.common.wechat.payment.retry.destination}" value="1"/>
                <entry key="${jdl.oms.express.order.common.refund.order.destination}" value="1"/>
                <entry key="${jdl.oms.express.order.common.integral.release.destination}" value="1"/>
                <entry key="${jdl.oms.express.order.common.ious.release.destination}" value="1"/>
                <entry key="${jdl.oms.express.order.common.ious.modify.destination}" value="1"/>
                <entry key="${jdl.oms.express.order.b2c.clear.order.bank.destination}" value="1"/>
                <entry key="${jdl.oms.express.order.common.freight.reverse.order.bank.destination}" value="1"/>
                <entry key="${jdl.oms.express.order.common.waybill.refund.order.destination}" value="1"/>
                <entry key="${jdl.oms.express.order.ccb2b.async.enquiry.order.bank.destination}" value="1"/>
                <entry key="${jdl.oms.express.order.common.push.invoice.info.uep.destination}" value="1"/>
                <entry key="${jdl.oms.express.order.common.create.tms.enquiry.bill.destination}" value="1"/>
                <entry key="${jdl.oms.express.order.zc.ccb2b.issue.persist.destination}" value="1"/>
                <entry key="${jdl.oms.express.order.zc.ccb2b.cancel.persist.destination}" value="1"/>
                <entry key="${jdl.oms.express.order.zc.ccb2b.init.order.bank.destination}" value="1"/>
                <entry key="${jdl.oms.express.order.zc.ccb2b.create.persist.destination}" value="1"/>
                <entry key="${jdl.oms.express.order.push.ebs.info.freight.destination}" value="1"/>
                <entry key="${jdl.oms.express.order.pos.pay.qr.close.destination}" value="1"/>
                <entry key="${jdl.oms.express.order.tms.clear.order.bank.destination}" value="1"/>
                <entry key="${jdl.oms.express.order.auto.write.off.destination}" value="1"/>
                <entry key="${jdl.oms.express.order.clear.order.bank.destination}" value="1"/>
                <entry key="${jdl.oms.express.ldop.fee.discount.detail.destination}" value="1"/>
                <entry key="${jdl.oms.express.ldop.package.number}" value="1"/>
                <entry key="${jdl.oms.express.terminal.notify.destination}" value="1"/>
                <entry key="${jdl.oms.express.terminal.receive.pay.destination}" value="1"/>
                <entry key="${jdl.oms.express.precharge.occupy.destination}" value="1"/>
                <entry key="${jdl.oms.express.push.ebs.destination}" value="1"/>
                <entry key="${jdl.oms.express.order.retry.repository.destination}" value="1"/>
                <entry key="${jdl.oms.express.cancel.service.order}" value="1"/>
                <entry key="${jdl.oms.express.create.payment.order.destination}" value="1"/>
                <entry key="${jdl.oms.express.release.payment.order.destination}" value="1"/>
                <entry key="${jdl.oms.express.standard.product.and.discount.enquiry.destination}" value="1"/>
                <entry key="${jdl.oms.express.outbound.to.eds.package.info.destination}" value="1"/>
                <entry key="${jdl.oms.express.delivery.pickup.sync.bind.destination}" value="1"/>
                <entry key="${jdl.oms.express.ecap.fee.details.notice.destination}" value="1"/>
            </map>
        </constructor-arg>
        <constructor-arg name="parser" ref="jsonParser" />
    </bean>

    <!--消息处理前置处理 -->
    <bean id="messageIntercepter" class="cn.jdl.oms.express.horz.infrs.ohs.locals.message.intercepter.DefaultMessageIntecepter">
    </bean>

</beans>
