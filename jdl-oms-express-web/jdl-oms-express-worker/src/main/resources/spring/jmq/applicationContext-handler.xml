<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!--消息接受处理抽象基类-->
    <bean id="expressAbstractHandler"
          class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.handler.ExpressAbstractHandler"
          abstract="true"/>

    <!--运单信息更新消息处理handler-->
    <bean id="waybillInfoUpdateHandler"
          class="cn.jdl.oms.express.worker.message.handler.WaybillInfoUpdateHandler"
          parent="expressAbstractHandler"/>

    <!--台账流水记录消息处理handler-->
    <bean id="orderBankFlowHandler"
          class="cn.jdl.oms.express.worker.message.handler.OrderBankFlowHandler"
          parent="expressAbstractHandler"/>

    <!--支付成功消息处理handler-->
    <bean id="jdPaySuccessNotifyHandler"
          class="cn.jdl.oms.express.worker.message.handler.JdPaySuccessNotifyHandler"
          parent="expressAbstractHandler"/>

    <!--退款消息处理handler-->
    <bean id="refundMessageHandler"
          class="cn.jdl.oms.express.worker.message.handler.RefundMessageHandler"
          parent="expressAbstractHandler"/>

    <!--超时取消订单handler-->
    <bean id="cancelPayTimeOutOrderHandler"
          class="cn.jdl.oms.express.worker.message.handler.CancelPayTimeOutOrderHandler"
          parent="expressAbstractHandler"/>

    <!--订单记录消息处理handler-->
    <bean id="expressOrderFlowHandler"
          class="cn.jdl.oms.express.worker.message.handler.ExpressOrderFlowHandler"
          parent="expressAbstractHandler"/>

    <!--pdq任务存储db失败重试消息处理handler-->
    <bean id="retryExpressOrderPDQHandler"
          class="cn.jdl.oms.express.worker.message.handler.RetryExpressOrderPDQHandler"
          parent="expressAbstractHandler"/>

    <!--外单台账-对账消息处理handler-->
    <bean id="reconciliationHandler"
          class="cn.jdl.oms.express.worker.message.handler.ReconciliationHandler"
          parent="expressAbstractHandler"/>

    <!--C2C台账初始化消息处理handler-->
    <bean id="c2CInitOrderBankJmqHandler"
          class="cn.jdl.oms.express.worker.message.handler.C2CInitOrderBankJmqHandler"
          parent="expressAbstractHandler"/>

    <!--B2C台账初始化消息处理handler-->
    <bean id="b2CInitOrderBankJmqHandler"
          class="cn.jdl.oms.express.worker.message.handler.B2CInitOrderBankJmqHandler"
          parent="expressAbstractHandler"/>

    <!--C2B台账初始化消息处理handler-->
    <bean id="c2BInitOrderBankJmqHandler"
          class="cn.jdl.oms.express.worker.message.handler.C2BInitOrderBankJmqHandler"
          parent="expressAbstractHandler"/>

    <!--快运台账初始化消息处理handler-->
    <bean id="freightInitOrderBankJmqHandler"
          class="cn.jdl.oms.express.worker.message.handler.FreightInitOrderBankJmqHandler"
          parent="expressAbstractHandler"/>

    <!--订单状态流水消息处理handler-->
    <bean id="orderStatusNotifyAsyncHandler"
          class="cn.jdl.oms.express.worker.message.handler.OrderStatusNotifyAsyncHandler"
          parent="expressAbstractHandler"/>
    <!--冷链计费结果消息处理handler-->
    <bean id="ccFeeInfoResultJmqHandler"
          class="cn.jdl.oms.express.worker.message.handler.CCB2BFeeInfoResultJmqHandler"
          parent="expressAbstractHandler"/>

    <!--关服系统状态(审核+清关)广播消息处理handler-->
    <bean id="expressCustomsStatusJmqHandler"
          class="cn.jdl.oms.express.worker.message.handler.ExpressCustomsStatusJmqHandler"
          parent="expressAbstractHandler"/>

    <!--港澳进口税金消息通知消息处理handler-->
    <bean id="expressOrderTaxJmqHandler"
          class="cn.jdl.oms.express.worker.message.handler.ExpressOrderTaxJmqHandler"
          parent="expressAbstractHandler"/>

    <!--订单信息更新消息处理handler-->
    <bean id="expressOrderInfoUpdateHandler"
          class="cn.jdl.oms.express.worker.message.handler.ExpressOrderInfoUpdateHandler"
          parent="expressAbstractHandler"/>

    <!--订单中心重试handler-->
    <bean id="expressRetryHandler" parent="expressAbstractHandler"
          class="cn.jdl.oms.express.worker.message.handler.ExpressRetryHandler"/>

    <!--询价系统推送取消消息处理handler-->
    <bean id="tmsEnquiryCancelNotifyJmqHandler"
          class="cn.jdl.oms.express.worker.message.handler.TmsEnquiryCancelNotifyJmqHandler"
          parent="expressAbstractHandler"/>

    <!--询价系统推送取消消息处理handler-->
    <bean id="tmsEnquiryConfirmBackJmqHandler"
          class="cn.jdl.oms.express.worker.message.handler.TmsEnquiryConfirmBackJmqHandler"
          parent="expressAbstractHandler"/>

    <!--冷链运单待支付运单mq-->
    <bean id="coldChainWayBillUnpaidHandler"
          class="cn.jdl.oms.express.worker.message.handler.ColdChainWayBillUnpaidHandler"
          parent="expressAbstractHandler"/>

    <!--支付结果消息处理handler-->
    <bean id="payReturnInfoHandler" parent="expressAbstractHandler"
          class="cn.jdl.oms.express.worker.message.handler.PayReturnInfoHandler"/>


    <!--快运整车直达待销售确认异步任务处理handler-->
    <bean id="waitSalesConfirmJmqHandler"
          class="cn.jdl.oms.express.worker.message.handler.WaitSalesConfirmJmqHandler"
          parent="expressAbstractHandler"/>

    <!--TMS询价系统推送结果确认消息处理handler-->
    <bean id="tmsEnquiryQuoteConfirmJmqHandler"
          class="cn.jdl.oms.express.worker.message.handler.TmsEnquiryQuoteConfirmJmqHandler"
          parent="expressAbstractHandler"/>

    <!--询价系统推送司机车辆消息处理handler-->
    <bean id="enquiryVehicleDriverJmqHandler"
          class="cn.jdl.oms.express.worker.message.handler.EnquiryVehicleDriverJmqHandler"
          parent="expressAbstractHandler"/>

    <!--纯配资源释放handler-->
    <bean id="expressResourceReleaseHandler"
          class="cn.jdl.oms.express.worker.message.handler.ExpressResourceReleaseHandler"
          parent="expressAbstractHandler"/>

    <!--改址一单到底异步询价handler-->
    <bean id="readdressAsyncEnquiryHandler"
          class="cn.jdl.oms.express.worker.message.handler.ReaddressAsyncEnquiryHandler"
          parent="expressAbstractHandler"/>

    <!--纯配发票推送任务handler-->
    <bean id="expressPushInvoiceHandler"
          class="cn.jdl.oms.express.worker.message.handler.invoice.ExpressPushInvoiceHandler"
          parent="expressAbstractHandler"/>

    <!--异步询价handler-->
    <bean id="commonAsyncEnquiryHandler"
          class="cn.jdl.oms.express.worker.message.handler.CommonAsyncEnquiryHandler"
          parent="expressAbstractHandler"/>

    <!--通用JMQ适配器异步handler-->
    <bean id="genericJMQToPDQAdaptorHandler"
          class="cn.jdl.oms.express.worker.message.handler.GenericJMQToPDQAdaptorHandler"
          parent="expressAbstractHandler"/>

    <!--外单计费结果同步，改址引入，获取专业市场折扣 handler-->
    <bean id="ldopFeeDiscountDetailHandler"
          class="cn.jdl.oms.express.worker.message.handler.LdopFeeDiscountDetailHandler"
          parent="expressAbstractHandler"/>

    <!--外单包裹数据更新 handler-->
    <bean id="ldopPackageNumberHandler"
          class="cn.jdl.oms.express.worker.message.handler.LdopPackageNumberHandler"
          parent="expressAbstractHandler"/>

    <!--终端揽收成功消息 handler-->
    <bean id="terminalPickedUpNotifyHandler"
          class="cn.jdl.oms.express.worker.message.handler.TerminalPickedUpNotifyHandler"
          parent="expressAbstractHandler"/>

    <!--终端揽收成功消息 handler-->
    <bean id="terminalReceivePayFinishHandler"
          class="cn.jdl.oms.express.worker.message.handler.TerminalReceivePayFinishHandler"
          parent="expressAbstractHandler"/>

    <!--收发管家预占 handler-->
    <bean id="prechargeOccupyHandler"
          class="cn.jdl.oms.express.worker.message.handler.transaction.PrechargeOccupyHandler"
          parent="expressAbstractHandler"/>

    <!--推送收入集成消息处理handler-->
    <bean id="omsExpressPushEbsHandler"
          class="cn.jdl.oms.express.worker.message.handler.OmsExpressPushEbsHandler"
          parent="expressAbstractHandler"/>


    <!--纯配订单持久化失败异步jmq重试-->
    <bean id="omsExpressRetryOrderJmqHandler"
          class="cn.jdl.oms.express.worker.message.handler.OmsExpressRetryOrderJmqHandler"
          parent="expressAbstractHandler"/>

    <!--异步取消订单 handler-->
    <bean id="asyncCancelOrderHandler"
          class="cn.jdl.oms.express.worker.message.handler.cancel.AsyncCancelOrderHandler"
          parent="expressAbstractHandler"/>

    <!--异步创建支付单 handler-->
    <bean id="omsExpressCreatePaymentOrderHandler"
          class="cn.jdl.oms.express.worker.message.handler.OmsExpressCreatePaymentOrderHandler"
          parent="expressAbstractHandler"/>
    <!--异步释放支付单 handler-->
    <bean id="omsExpressReleasePaymentOrderHandler"
          class="cn.jdl.oms.express.worker.message.handler.OmsExpressReleasePaymentOrderHandler"
          parent="expressAbstractHandler"/>
    <!--异步事后折询价 handler-->
    <bean id="asyncStandardProductAndDiscountInquiryHandler"
          class="cn.jdl.oms.express.worker.message.handler.transaction.AsyncStandardProductAndDiscountInquiryHandler"
          parent="expressAbstractHandler"/>

    <!--仓配接配出库包裹信息更新 handler-->
    <bean id="outboundPackageUpdateHandler"
          class="cn.jdl.oms.express.worker.message.handler.OutboundPackageUpdateHandler"
          parent="expressAbstractHandler"/>

    <!--送取同步绑定或解除绑定关联单 handler-->
    <bean id="deliveryPickupSyncBindJmqHandler"
          class="cn.jdl.oms.express.worker.message.handler.DeliveryPickupSyncBindJmqHandler"
          parent="expressAbstractHandler"/>

    <!--平台应收明细同步 handler-->
    <bean id="ecapFeeDetailsNoticeHandler"
          class="cn.jdl.oms.express.worker.message.handler.EcapFeeDetailsNoticeHandler"
          parent="expressAbstractHandler"/>

    <!--询价记录处理类-->
    <bean id="orderEnquiryRecordHandler"
          class="cn.jdl.oms.express.worker.message.handler.OrderEnquiryRecordHandler"
          parent="expressAbstractHandler"/>

</beans>
