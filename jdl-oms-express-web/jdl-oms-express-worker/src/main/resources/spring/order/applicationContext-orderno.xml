<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
        http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd
        http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-3.0.xsd"
       default-lazy-init="false" default-autowire="byName">

    <bean id="orderNoPrefixUtil" class="cn.jdl.oms.express.shared.common.utils.OrderNoCreateUtil">
        <!--订单生成前缀配置-->
        <constructor-arg name="prefix" index="0">
            <map>
                <!--( key:C2C业务单元，value:订单号前缀)-->
                <entry>
                    <key>
                        <util:constant static-field="cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_C2C"/>
                    </key>
                    <value>${c2c.order.no.prefix}</value>
                </entry>
                <!--( key:C2C业务单元，value:订单号前缀)-->
                <entry>
                    <key>
                        <util:constant static-field="cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_O2O"/>
                    </key>
                    <value>${c2c.order.no.prefix}</value>
                </entry>
                <!--( key:B2C业务单元，value:订单号前缀)-->
                <entry>
                    <key>
                        <util:constant static-field="cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_B2C"/>
                    </key>
                    <value>${b2c.order.no.prefix}</value>
                </entry>
                <!--( key:C2B业务单元，value:订单号前缀)-->
                <entry>
                    <key>
                        <util:constant static-field="cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_C2B"/>
                    </key>
                    <value>${c2b.order.no.prefix}</value>
                </entry>
                <!--( key:耗材售卖业务单元，value:订单号前缀)-->
                <entry>
                    <key>
                        <util:constant static-field="cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_PACKING"/>
                    </key>
                    <value>${packing.order.no.prefix}</value>
                </entry>
                <!--( key:短链业务单元，value:订单号前缀)-->
                <entry>
                    <key>
                        <util:constant static-field="cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_SHORT_CHAIN"/>
                    </key>
                    <value>${shortchain.order.no.prefix}</value>
                </entry>
                <!-- key:平台订单散客业务，value:订单号前缀 -->
                <entry>
                    <key>
                        <util:constant static-field="cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_UEP_C2C"/>
                    </key>
                    <value>${uep.c2c.order.no.prefix}</value>
                </entry>
                <!-- key:大件纯配，value:订单号前缀 -->
                <entry>
                    <key>
                        <util:constant static-field="cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_LAS"/>
                    </key>
                    <value>${las.order.no.prefix}</value>
                </entry>
                <!-- key:平台订单C2B业务，value:订单号前缀 -->
                <entry>
                    <key>
                        <util:constant static-field="cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_UEP_C2B"/>
                    </key>
                    <value>${uep.c2c.order.no.prefix}</value>
                </entry>

                <!-- key:落地配B2C业务，value:订单号前缀 -->
                <entry>
                    <key>
                        <util:constant static-field="cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_LM_B2C"/>
                    </key>
                    <value>${lm.b2c.order.no.prefix}</value>
                </entry>
                <!-- key:冷链B2B业务，value:订单号前缀 -->
                <entry>
                    <key>
                        <util:constant static-field="cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_CC_B2B"/>
                    </key>
                    <value>${cc.b2b.order.no.prefix}</value>
                </entry>

                <!--( key:国际C2C业务单元，value:订单号前缀)-->
                <entry>
                    <key>
                        <util:constant static-field="cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_INTL_C2C"/>
                    </key>
                    <value>${intl.c2c.order.no.prefix}</value>
                </entry>

                <!--( key:国际B2C业务单元，value:订单号前缀)-->
                <entry>
                    <key>
                        <util:constant static-field="cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_INTL_B2C"/>
                    </key>
                    <value>${intl.c2c.order.no.prefix}</value>
                </entry>

                <!--( key:运力包仓专线业务单元，value:订单号前缀)-->
                <entry>
                    <key>
                        <util:constant static-field="cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_TMS_ZX"/>
                    </key>
                    <value>${tms.order.no.prefix}</value>
                </entry>

                <!--( key:融合C2C业务单元，value:订单号前缀)-->
                <entry>
                    <key>
                        <util:constant static-field="cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_UNITED_C2C"/>
                    </key>
                    <value>${united.c2c.order.no.prefix}</value>
                </entry>
            </map>
        </constructor-arg>
        <!--订单生成序列号key配置-->
        <constructor-arg name="serialnumberKey" index="1">
            <map>
                <!--( key:C2C业务单元，value:订单生成序列号key)-->
                <entry>
                    <key>
                        <util:constant static-field="cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_C2C"/>
                    </key>

                    <value>cn_jdl_express</value>
                </entry>

                <entry>
                    <key>
                        <util:constant static-field="cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_O2O"/>
                    </key>

                    <value>cn_jdl_express</value>
                </entry>
                <!--( key:B2C业务单元，value:订单生成序列号key)-->
                <entry>
                    <key>
                        <util:constant static-field="cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_B2C"/>
                    </key>
                    <value>cn_jdl_express</value>
                </entry>
                <!--( key:C2B业务单元，value:订单生成序列号key)-->
                <entry>
                    <key>
                        <util:constant static-field="cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_C2B"/>
                    </key>
                    <value>cn_jdl_express</value>
                </entry>
                <!--( key:耗材售卖业务单元，value:订单生成序列号key)-->
                <entry>
                    <key>
                        <util:constant static-field="cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_PACKING"/>
                    </key>
                    <value>cn_jdl_packing</value>
                </entry>
                <!--( key:短链业务单元，value:订单生成序列号key)-->
                <entry>
                    <key>
                        <util:constant static-field="cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_SHORT_CHAIN"/>
                    </key>
                    <value>cn_jdl_express</value>
                </entry>
                <!-- key:平台订单散客业务，value:订单生成序列号key -->
                <entry>
                    <key>
                        <util:constant static-field="cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_UEP_C2C"/>
                    </key>
                    <value>cn_jdl_uep</value>
                </entry>
                <!-- key:平台订单C2B业务，value:订单生成序列号key -->
                <entry>
                    <key>
                        <util:constant static-field="cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_UEP_C2B"/>
                    </key>
                    <value>cn_jdl_uep</value>
                </entry>
                <!-- key:大件纯配单元，value:订单生成序列号key -->
                <entry>
                    <key>
                        <util:constant static-field="cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_LAS"/>
                    </key>
                    <value>cn_jdl_las</value>
                </entry>
                <!-- key:冷链B2B，value:订单生成序列号key -->
                <entry>
                    <key>
                        <util:constant static-field="cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_CC_B2B"/>
                    </key>
                    <value>cn_jdl_cc-b2b</value>
                </entry>

                <!-- key:落地配B2C业务，value:订单生成序列号key -->
                <entry>
                    <key>
                        <util:constant static-field="cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_LM_B2C"/>
                    </key>
                    <value>cn_jdl_lm</value>
                </entry>

                <!--( key:国际C2C业务单元，value:订单生成序列号key)-->
                <entry>
                    <key>
                        <util:constant static-field="cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_INTL_C2C"/>
                    </key>

                    <value>cn_jdl_express</value>
                </entry>
                <!--( key:国际B2C业务单元，value:订单生成序列号key)-->
                <entry>
                    <key>
                        <util:constant static-field="cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_INTL_B2C"/>
                    </key>

                    <value>cn_jdl_express</value>
                </entry>

                <!--( key:运力包仓专线业务单元，value:订单生成序列号key)-->
                <entry>
                    <key>
                        <util:constant static-field="cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_TMS_ZX"/>
                    </key>

                    <value>cn_jdl_tms</value>
                </entry>
            </map>
        </constructor-arg>
    </bean>

</beans>