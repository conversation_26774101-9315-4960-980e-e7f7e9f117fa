<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:q="http://code.jd.com/schema/pdq/q"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
	    http://www.springframework.org/schema/beans/spring-beans.xsd http://code.jd.com/schema/pdq/q
	    http://code.jd.com/schema/pdq/q.xsd
        http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context.xsd
	   http://jsf.jd.com/schema/jsf
	   http://jsf.jd.com/schema/jsf/jsf.xsd
       http://www.springframework.org/schema/aop
       http://www.springframework.org/schema/aop/spring-aop.xsd">
    <!-- 加载important.properties配置文件 -->

    <!--接单下发异常重试100003-->
    <bean id="createIssueRetryHandler" class="cn.jdl.oms.express.worker.scheduler.issue.CreateIssueRetryHandler"/>
    <!--修改持久化重试100005-->
    <bean id="modifyRepositoryRetryHandler" class="cn.jdl.oms.express.worker.scheduler.order.ModifyRepositoryRetryHandler"/>
    <!--删除持久化重试100004-->
    <bean id="deleteRepositoryRetryHandler" class="cn.jdl.oms.express.worker.scheduler.order.DeleteRepositoryRetryHandler"/>

    <!--回传持久化重试100006-->
    <bean id="callbackRepositoryRetryHandler" class="cn.jdl.oms.express.worker.scheduler.order.CallbackRepositoryRetryHandler"/>
    <!--台账清理800001-->
    <bean id="c2CClearOrderBankHandler" class="cn.jdl.oms.express.worker.scheduler.orderbank.C2CClearOrderBankHandler"/>
    <!--微信免密单取消-->
    <bean id="weChatPaymentCancelHandler" class="cn.jdl.oms.express.worker.scheduler.wechatpayment.WeChatPaymentCancelHandler"/>
    <!-- 优惠券释放 100001-->
    <bean id="couponReleaseHandler" class="cn.jdl.oms.express.worker.scheduler.coupon.CouponReleaseHandler" />
    <!-- 资源鸡毛信释放 100002-->
    <bean id="iOTReleaseHandler" class="cn.jdl.oms.express.worker.scheduler.iot.IOTReleaseHandler"/>
    <!--取消落库重试-->
    <bean id="cancelRepositoryRetryHandler" class="cn.jdl.oms.express.worker.scheduler.order.CancelRepositoryRetryHandler"/>
    <!--退款100010「暂时只有O2O」-->
    <bean id="refundOrderHandler" class="cn.jdl.oms.express.worker.scheduler.pay.RefundOrderHandler"/>
    <!--台账初始化800002-->
    <bean id="c2CInitOrderBankHandler" class="cn.jdl.oms.express.worker.scheduler.orderbank.C2CInitOrderBankHandler"/>
    <!--异步询价台账800003-->
    <bean id="c2CAsynEnquiryOrderBankHandler" class="cn.jdl.oms.express.worker.scheduler.enquiry.C2CAsynEnquiryOrderBankHandler"/>
    <!--逆向单持久化异步重试100009-->
    <bean id="reverseRepositoryRetryHandler" class="cn.jdl.oms.express.worker.scheduler.order.ReverseRepositoryRetryHandler"/>
    <!--台账修改失败回滚800005-->
    <!--    <bean id="c2CModifyOrderBankRollbackHandler" class="cn.jdl.oms.express.worker.scheduler.orderbank.C2CModifyOrderBankRollbackHandler"/>-->
    <!--白条预授权释放900001-->
    <bean id="iousReleaseHandler" class="cn.jdl.oms.express.worker.scheduler.ious.IousReleaseHandler"/>
    <!--订单状态变化重试 100011-->
    <!--<bean id="modifyOrderStatusHandler" class="cn.jdl.oms.express.worker.scheduler.order.ModifyOrderStatusHandler"/>-->
    <!--b2c异步询价台账-->
    <bean id="b2CAsynEnquiryOrderBankHandler" class="cn.jdl.oms.express.worker.scheduler.enquiry.B2CAsynEnquiryOrderBankHandler"/>
    <!--白条预授权释放(修改单失败，修改白条预授权为原单预占值处理)900002-->
    <bean id="iousModifyHandler" class="cn.jdl.oms.express.worker.scheduler.ious.IousModifyHandler"/>
    <!-- 原单解绑改址单100012 -->
    <bean id="unbindOriginalOrderHandle" class="cn.jdl.oms.express.worker.scheduler.bindOriginalOrder.UnbindOriginalOrderHandle" />

    <!--订单状态变化重试 100011-->
    <bean id="modifyOrderStatusHandler" class="cn.jdl.oms.express.worker.scheduler.order.ModifyOrderStatusHandler"/>

    <!-- 积分回滚100014 -->
    <bean id="integralReleaseHandler" class="cn.jdl.oms.express.worker.scheduler.integral.IntegralReleaseHandler" />

    <!-- 改址单清原单COD 800007 -->
    <bean id="productClearHandler" class="cn.jdl.oms.express.worker.scheduler.order.ModifyOriginalOrderHandler" />
    <!-- 台账调整 800008 -->
    <bean id="c2CAdjustOrderBankHandler" class="cn.jdl.oms.express.worker.scheduler.orderbank.C2CAdjustOrderBankHandler" />
    <!-- B2C初始化台账处理 800009 -->
    <bean id="b2cInitOrderBankHandler" class="cn.jdl.oms.express.worker.scheduler.orderbank.B2CInitOrderBankHandler" />
    <!-- B2C台账清理处理 800011 -->
    <bean id="b2cClearOrderBankHandler" class="cn.jdl.oms.express.worker.scheduler.orderbank.B2CClearOrderBankHandler" />

    <!-- c2b异步询价台账 800014-->
    <bean id="c2bAsynEnquiryOrderBankHandler" class="cn.jdl.oms.express.worker.scheduler.enquiry.C2BAsynEnquiryOrderBankHandler"/>
    <!-- c2b初始化台账处理 800019 -->
    <bean id="c2bInitOrderBankHandler" class="cn.jdl.oms.express.worker.scheduler.orderbank.C2BInitOrderBankHandler" />
    <!-- c2b台账清理处理 800013 -->
    <bean id="c2bClearOrderBankHandler" class="cn.jdl.oms.express.worker.scheduler.orderbank.C2BClearOrderBankHandler" />

    <!-- 全程跟踪100020 -->
    <bean id="orderTrackRetryHandler" class="cn.jdl.oms.express.worker.scheduler.ordertrack.OrderTrackRetryHandler" />

    <!--京喜达-台账初始化800015-->
    <bean id="jxdInitOrderBankHandler" class="cn.jdl.oms.express.worker.scheduler.orderbank.JXDInitOrderBankHandler"/>
    <!--京喜达-台账清理800016-->
    <bean id="jxdClearOrderBankHandler" class="cn.jdl.oms.express.worker.scheduler.orderbank.JXDClearOrderBankHandler"/>

    <!--单状态通知重试100021-->
    <bean id="orderStatusNotifyRetryHandler" class="cn.jdl.oms.express.worker.scheduler.status.OrderStatusNotifyRetryHandler"/>

    <!--关联关系存储重试 100015-->
    <bean id="createOrderRelationHandler" class="cn.jdl.oms.express.worker.scheduler.relation.CreateOrderRelationHandler"/>

    <!--接单消息重试 100016-->
    <bean id="createOrderNoticeRetryHandler" class="cn.jdl.oms.express.worker.scheduler.notice.CreateOrderNoticeRetryHandler"/>

    <!-- UEP台账清理处理 800025 -->
    <bean id="uepClearOrderBankHandler" class="cn.jdl.oms.express.worker.scheduler.orderbank.UEPClearOrderBankHandler" />

    <!-- 保险释放 800030 -->
    <bean id="insuranceReleaseHandler" class="cn.jdl.oms.express.worker.scheduler.insurance.InsuranceReleaseHandler" />

    <!-- 开票信息推送 800032 -->
    <bean id="pushInvoiceInfoHandler" class="cn.jdl.oms.express.worker.scheduler.invoice.PushInvoiceInfoHandler" />

    <!-- 快运初始化台账处理 800020 -->
    <bean id="freightInitOrderBankHandler" class="cn.jdl.oms.express.worker.scheduler.orderbank.FreightInitOrderBankHandler" />

    <!-- 快运异步询价台账消息处理 800021 -->
    <bean id="freightAsynEnquiryOrderBankHandler" class="cn.jdl.oms.express.worker.scheduler.enquiry.FreightAsynEnquiryOrderBankHandler" />
    <!-- 运单退款息处理 800022 -->
    <bean id="waybillRefundApplyMessageHandler" class="cn.jdl.oms.express.worker.scheduler.pay.WaybillRefundApplyMessageHandler" />
    <!-- 改址单状态通知重试 800031 -->
    <bean id="readdressStatusNoticeRetryHandler" class="cn.jdl.oms.express.worker.scheduler.status.ReaddressStatusNoticeRetryHandler" />
    <!-- 支付失败重试 800036  -->
    <bean id="payHandler" class="cn.jdl.oms.express.worker.scheduler.pay.PayHandler" />
    <!-- 冷链B2B逆向异步询价台账消息处理 800033 -->
    <bean id="ccB2BReverseAsyncEnquiryOrderBankHandler" class="cn.jdl.oms.express.worker.scheduler.enquiry.CCB2BReverseAsyncEnquiryOrderBankHandler" />
    <!-- 冷链B2B修改异步询价台账消息处理 800034 -->
    <bean id="ccB2BModifyAsyncEnquiryOrderBankHandler" class="cn.jdl.oms.express.worker.scheduler.enquiry.CCB2BModifyAsyncEnquiryOrderBankHandler" />
    <!--冷链B2B异步询价 800023-->
    <bean id="ccB2BAsyncEnquiryOrderBankHandler" class="cn.jdl.oms.express.worker.scheduler.enquiry.CCB2BAsyncEnquiryOrderBankHandler"/>
    <!-- 删除关联关系存储重试 100022 -->
    <bean id="deleteOrderRelationHandler" class="cn.jdl.oms.express.worker.scheduler.relation.DeleteOrderRelationHandler"/>
    <!-- 冷链B2B整车异步下发持久化补偿 -->
    <bean id="ccB2BBatchIssueRetryHandler" class="cn.jdl.oms.express.worker.scheduler.issue.CCB2BBatchIssueRetryHandler" />
    <!-- 冷链B2B整车异步取消持久化补偿 -->
    <bean id="ccB2BBatchCancelRetryHandler" class="cn.jdl.oms.express.worker.scheduler.order.CCB2BBatchCancelRetryHandler" />
    <!--冷链B2B整车初始化台账处理 -->
    <bean id="ccB2BBatchInitOrderBankHandler" class="cn.jdl.oms.express.worker.scheduler.orderbank.CCB2BBatchInitOrderBankHandler"/>

    <!-- C2C开票信息推送 800033 -->
    <bean id="c2cPushInvoiceInfoHandler" class="cn.jdl.oms.express.worker.scheduler.invoice.C2CPushInvoiceInfoHandler" />
    <!-- 创建订单预处理重试 800037 -->
    <bean id="createOrderPreprocessRetryHandler" class="cn.jdl.oms.express.worker.scheduler.orderpreprocess.CreateOrderPreprocessRetryHandler" />
    <!-- 删除订单预处理重试 800038 -->
    <bean id="deleteOrderPreprocessRetryHandler" class="cn.jdl.oms.express.worker.scheduler.orderpreprocess.DeleteOrderPreprocessRetryHandler" />
    <!-- 修改订单预处理重试 800039 -->
    <bean id="modifyOrderPreprocessRetryHandler" class="cn.jdl.oms.express.worker.scheduler.orderpreprocess.ModifyOrderPreprocessRetryHandler" />
    <!-- 修改订单预处理重试 -->
    <bean id="createRepositoryRetryHandler" class="cn.jdl.oms.express.worker.scheduler.order.CreateRepositoryRetryHandler" />
    <!-- 批量修改订单重试 -->
    <bean id="modifyRepositoryBatchRetryHandler" class="cn.jdl.oms.express.worker.scheduler.order.ModifyRepositoryBatchRetryHandler" />

    <!-- 快运开票信息推送 800040 -->
    <bean id="freightPushInvoiceInfoHandler" class="cn.jdl.oms.express.worker.scheduler.invoice.FreightPushInvoiceInfoHandler" />
    <!-- 快运开票信息推送 800041 -->
    <bean id="freightPushEBSInfoHandler" class="cn.jdl.oms.express.worker.scheduler.ebs.FreightPushEBSInfoHandler" />
    <!-- 修改包含持久化内后续逻辑的重试 800042 -->
    <bean id="modifyRepositoryBusinessRetryHandler" class="cn.jdl.oms.express.worker.scheduler.order.ModifyRepositoryBusinessRetryHandler" />

    <!-- 创建TMS询价单 800045  -->
    <bean id="createTMSEnquiryBillHandler" class="cn.jdl.oms.express.worker.scheduler.tms.CreateTMSEnquiryBillHandler" />
    <!-- 修改下发重试 100023  -->
    <bean id="modifyIssueRetryHandler" class="cn.jdl.oms.express.worker.scheduler.issue.ModifyIssueRetryHandler" />
    <!-- 关闭支付二维码 800051  -->
    <bean id="posPayQrClosePDQHandler" class="cn.jdl.oms.express.worker.scheduler.pospay.PosPayQrClosePDQHandler" />

    <!-- Tms台账清理处理 800052 -->
    <bean id="tmsClearOrderBankHandler" class="cn.jdl.oms.express.worker.scheduler.orderbank.TmsClearOrderBankHandler" />
    <!-- 自动核销 800053  -->
    <bean id="autoWriteOffPDQHandler" class="cn.jdl.oms.express.worker.scheduler.writeoff.AutoWriteOffPDQHandler" />
    <!--redis缓存重试 100024-->
    <bean id="redisWriteRetryPdqHandler" class="cn.jdl.oms.express.worker.scheduler.redis.RedisWriteRetryPdqHandler"/>
    <!-- 数据库配置 -->
    <q:configuration>
        <q:database>
            <q:property name="db-driver" value="${jdbc.mysql.driver}" />
            <q:property name="db-url" value="${jdbc.mysql.pdq.url}" />
            <q:property name="db-user" value="${jdbc.mysql.pdq.username}" />
            <q:property name="db-password" value="${jdbc.mysql.pdq.password}" />
        </q:database>
        <!--任务调度默认配置-->
        <q:server>
            <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryDefaultPolicy" />
            <q:property name="flush-at-commit" value="1" /><!-- 控制节流阀生效，批量提交 -->
            <q:property name="max-per-second-rates" value="5000" /> <!-- 节流阀， 每秒钟处理的 消息数量-->
            <q:property name="monitor-umpkey" value="worker.express.oms.jdl.cn" />
            <q:property name="max-polling-times" value="10"/> <!-- 最大重试次数-->
            <q:property name="fmsg-size" value="50"/> <!--wait表单次抓取数量-->
            <q:property name="frmsg-size" value="50"/> <!--fail表单次抓取数量-->
            <q:property name="max-queue-size" value="100"/><!--wait 最大抓取大小-->
            <!-- Topic配置 -->
            <!--demo-->
            <q:topic name="100003" ref="createIssueRetryHandler" >
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="100004" ref="deleteRepositoryRetryHandler" >
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="100005" ref="modifyRepositoryRetryHandler" >
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="100006" ref="callbackRepositoryRetryHandler" >
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="800001" ref="c2CClearOrderBankHandler" >
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="100007" ref="weChatPaymentCancelHandler" >
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="100001" ref="couponReleaseHandler" >
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="100002" ref="iOTReleaseHandler" >
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="100008" ref="cancelRepositoryRetryHandler" >
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="100010" ref="refundOrderHandler" >
                <q:property name="policy" value="cn.jdl.oms.express.worker.scheduler.policy.RedeliveryFixedTimePolicy" />
            </q:topic>
            <q:topic name="800002" ref="c2CInitOrderBankHandler" >
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="800003" ref="c2CAsynEnquiryOrderBankHandler" >
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="100009" ref="reverseRepositoryRetryHandler">
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <!--            <q:topic name="800005" ref="c2CModifyOrderBankRollbackHandler">-->
            <!--                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />-->
            <!--            </q:topic>-->
            <q:topic name="900001" ref="iousReleaseHandler">
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="900002" ref="iousModifyHandler">
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <!-- <q:topic name="100011" ref="modifyOrderStatusHandler">
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>-->
            <q:topic name="100012" ref="unbindOriginalOrderHandle">
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="100011" ref="modifyOrderStatusHandler">
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="100014" ref="integralReleaseHandler">
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="800007" ref="productClearHandler">
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="800008" ref="c2CAdjustOrderBankHandler">
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="100020" ref="orderTrackRetryHandler">
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="800009" ref="b2cInitOrderBankHandler">
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="800011" ref="b2cClearOrderBankHandler">
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="800012" ref="b2CAsynEnquiryOrderBankHandler">
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="800019" ref="c2bInitOrderBankHandler">
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="800013" ref="c2bClearOrderBankHandler">
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="800014" ref="c2bAsynEnquiryOrderBankHandler">
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="800015" ref="jxdInitOrderBankHandler" >
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="800016" ref="jxdClearOrderBankHandler" >
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="100021" ref="orderStatusNotifyRetryHandler" >
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="100015" ref="createOrderRelationHandler" >
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="100016" ref="createOrderNoticeRetryHandler" >
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="800025" ref="uepClearOrderBankHandler" >
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="800030" ref="insuranceReleaseHandler" >
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <!-- 开票信息推送topic & handler配置 -->
            <q:topic name="800032" ref="pushInvoiceInfoHandler" >
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="800020" ref="freightInitOrderBankHandler">
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="800021" ref="freightAsynEnquiryOrderBankHandler">
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="800022" ref="waybillRefundApplyMessageHandler">
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryFixedTimePolicy" />
            </q:topic>
            <q:topic name="800031" ref="readdressStatusNoticeRetryHandler">
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="100022" ref="deleteOrderRelationHandler" >
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <!-- C2C开票信息推送 -->
            <q:topic name="800035" ref="c2cPushInvoiceInfoHandler" >
                <q:property name="policy" value="cn.jdl.oms.express.worker.scheduler.policy.InvoiceRedeliveryPolicy" />
                <q:property name="max-polling-times" value="500"/> <!-- 最大重试次数-->
            </q:topic>
            <!-- 创建订单预处理重试 -->
            <q:topic name="800037" ref="createOrderPreprocessRetryHandler" >
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <!-- 删除订单预处理重试 -->
            <q:topic name="800038" ref="deleteOrderPreprocessRetryHandler" >
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <!-- 修改订单预处理重试 -->
            <q:topic name="800039" ref="modifyOrderPreprocessRetryHandler" >
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="800033" ref="ccB2BReverseAsyncEnquiryOrderBankHandler">
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="800034" ref="ccB2BModifyAsyncEnquiryOrderBankHandler">
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="800023" ref="ccB2BAsyncEnquiryOrderBankHandler">
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="800046" ref="ccB2BBatchIssueRetryHandler">
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="800047" ref="ccB2BBatchCancelRetryHandler">
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="800048" ref="ccB2BBatchInitOrderBankHandler">
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="800036" ref="payHandler">
                <q:property name="policy" value="cn.jdl.oms.express.worker.scheduler.policy.RedeliveryPeriodicalPolicy" />
            </q:topic>
            <q:topic name="800049" ref="createRepositoryRetryHandler">
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="800050" ref="modifyRepositoryBatchRetryHandler">
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <!-- 快运开票信息推送 -->
            <q:topic name="800040" ref="freightPushInvoiceInfoHandler" >
                <q:property name="policy" value="cn.jdl.oms.express.worker.scheduler.policy.InvoiceRedeliveryPolicy" />
                <q:property name="max-polling-times" value="500"/> <!-- 最大重试次数-->
            </q:topic>
            <!-- 快运收入集成信息推送 -->
            <q:topic name="800041" ref="freightPushEBSInfoHandler" >
                <q:property name="policy" value="cn.jdl.oms.express.worker.scheduler.policy.RedeliveryPeriodicalPolicy" />
            </q:topic>
            <!-- 创建TMS询价单 -->
            <q:topic name="800045" ref="createTMSEnquiryBillHandler" >
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <!-- 修改下发重试 100023 -->
            <q:topic name="100023" ref="modifyIssueRetryHandler" >
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <!-- 修改包含持久化内后续逻辑的重试 -->
            <q:topic name="800042" ref="modifyRepositoryBusinessRetryHandler" >
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <!-- 关闭支付二维码 800051 -->
            <q:topic name="800051" ref="posPayQrClosePDQHandler" >
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <!-- TMS清台账 -->
            <q:topic name="800052" ref="tmsClearOrderBankHandler" >
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <!-- 自动核销 800053 -->
            <q:topic name="800053" ref="autoWriteOffPDQHandler" >
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
            <q:topic name="100024" ref="redisWriteRetryPdqHandler" >
                <q:property name="policy" value="com.jd.paq.core.policy.RedeliveryResetIncerementPolicy" />
            </q:topic>
        </q:server>
    </q:configuration>

</beans>
