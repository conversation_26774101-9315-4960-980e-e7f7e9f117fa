<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd


	http://jsf.jd.com/schema/jsf http://jsf.jd.com/schema/jsf/jsf.xsd">

    <!-- 事业部编码校验 暂未使用 -->
    <jsf:consumer id="deptService" interface="com.jd.eclp.master.dept.service.DeptService"
                  timeout="${jsf.deptService.timeout}" retries="${jsf.deptService.retries}"
                  protocol="jsf" alias="${jsf.deptService.alias}" serialization="hessian">
    </jsf:consumer>

    <!-- 商家信息查询接口 -->
    <jsf:consumer id="basicTraderAPI" interface="com.jd.ldop.basic.api.BasicTraderAPI" protocol="jsf"
                  alias="${jsf.basicTraderAPI.alias}" timeout="${jsf.basicTraderAPI.timeout}"
                  retries="${jsf.basicTraderAPI.retries}" serialization="hessian">
    </jsf:consumer>

    <!-- 商家多发货地址查询接口 -->
    <jsf:consumer id="basicTraderShipAddressAPI" interface="com.jd.ldop.basic.api.BasicTraderShipAddressAPI" protocol="jsf"
                  alias="${jsf.basicTraderShipAddressAPI.alias}" timeout="${jsf.basicTraderShipAddressAPI.timeout}"
                  retries="${jsf.basicTraderShipAddressAPI.retries}" serialization="hessian">
    </jsf:consumer>

    <!-- 单个获取ID序列号 -->
    <jsf:consumer id="singleSequenceService" interface="com.jdl.cp.oms.pk.client.service.SingleSequenceService"
                  timeout="${jsf.singleSequenceService.timeout}" alias="${jsf.singleSequenceService.alias}"
                  retries="${jsf.singleSequenceService.retries}"
                  protocol="jsf" serialization="hessian">
        <jsf:parameter key="token" value="${jsf.singleSequenceService.token}" hide="true"/>
    </jsf:consumer>

    <!-- 国标gis解析 -->
    <jsf:consumer id="gbLevelService" interface="com.jd.addresstranslation.api.address.AddressToGBLevelService"
                  alias="${jsf.gbLevelService.alias}" protocol="jsf" serialization="hessian"
                  timeout="${jsf.gbLevelService.timeout}" retries="${jsf.gbLevelService.retries}">
        <jsf:parameter key="token" value="${jsf.gbLevelService.token}" hide="true"/>
    </jsf:consumer>

    <!--地址解析京标接口 guoxueliang1-->
    <jsf:consumer id="externalAddressToJDAddressService"
                  interface="com.jd.addresstranslation.api.address.ExternalAddressToJDAddressService"
                  alias="${jsf.externalAddressToJDAddressService.alias}" protocol="jsf"
                  timeout="${jsf.externalAddressToJDAddressService.timeout}" serialization="hessian"
                  retries="${jsf.externalAddressToJDAddressService.retries}">
        <jsf:parameter key="token" value="${jsf.externalAddressToJDAddressService.token}" hide="true"/>
    </jsf:consumer>

    <!--批量地址解析京标接口 guoxueliang1-->
    <jsf:consumer id="batchExternalAddressToJDAddressService"
                  interface="com.jd.addresstranslation.api.address.BatchExternalAddressToJDAddressService"
                  alias="${jsf.batchExternalAddressToJDAddressService.alias}" protocol="jsf"
                  timeout="${jsf.batchExternalAddressToJDAddressService.timeout}" serialization="hessian"
                  retries="${jsf.batchExternalAddressToJDAddressService.retries}">
        <jsf:parameter key="token" value="${jsf.batchExternalAddressToJDAddressService.token}" hide="true"/>
    </jsf:consumer>

    <!-- 持久化接单 -->
    <jsf:consumer id="persistOrderApi" interface="com.jdl.cp.op.client.api.PersistOrderApi"
                  alias="${jsf.persistOrderApi.alias}" protocol="jsf" serialization="hessian" filter="consumerChainFilter"
                  timeout="${jsf.persistOrderApi.timeout}" retries="${jsf.persistOrderApi.retries}">
        <jsf:parameter key="token" value="${jsf.persistOrderApi.token}" hide="true"/>
    </jsf:consumer>

    <!-- 接单下发履约层 -->
    <jsf:consumer id="createOrderOfcService"
                  interface="cn.jdl.oms.express.api.ofc.CreateOrderOfcService"
                  timeout="${jsf.createOrderOfcService.timeout}" retries="${jsf.createOrderOfcService.retries}" filter="consumerChainFilter"
                  protocol="jsf" check="false" alias="${jsf.createOrderOfcService.alias}" serialization="hessian">
        <jsf:parameter key="token" value="${jsf.createOrderOfcService.token}" hide="true"/>
    </jsf:consumer>

    <!--订单详情查询-->
    <jsf:consumer id="getFullOrderApi" interface="com.jdl.cp.osc.client.api.GetFullOrderApi"
                  alias="${jsf.getFullOrderApi.alias}" protocol="jsf" serialization="hessian"  filter="consumerChainFilter"
                  timeout="${jsf.getFullOrderApi.timeout}" retries="${jsf.getFullOrderApi.retries}">
        <jsf:parameter key="token" value="${jsf.getFullOrderApi.token}" hide="true"/>
    </jsf:consumer>

    <!--订单变更记录-->
    <jsf:consumer id="getOrderModifyInfoService" interface="cn.jdl.oms.search.api.GetOrderModifyInfoService"
                  alias="${jsf.get.order.modifyInfo.alias}" protocol="jsf" serialization="hessian"
                  timeout="${jsf.get.order.modifyInfo.timeout}" retries="${jsf.get.order.modifyInfo.retries}">
        <jsf:parameter key="token" value="${jsf.get.order.modifyInfo.token}" hide="true"/>
    </jsf:consumer>

    <!-- 修改订单 -->
    <jsf:consumer id="modifyOrderApi" interface="com.jdl.cp.op.client.api.ModifyOrderApi"
                  alias="${jsf.modifyOrderApi.alias}" protocol="jsf" serialization="hessian"  filter="consumerChainFilter"
                  timeout="${jsf.modifyOrderApi.timeout}" retries="${jsf.modifyOrderApi.retries}">
        <jsf:parameter key="token" value="${jsf.modifyOrderApi.token}" hide="true"/>
    </jsf:consumer>

    <!-- 取消订单 -->
    <jsf:consumer id="cancelOrderApi" interface="com.jdl.cp.op.client.api.CancelOrderApi"
                  alias="${jsf.cancelOrderApi.alias}" protocol="jsf" serialization="hessian"  filter="consumerChainFilter"
                  timeout="${jsf.cancelOrderApi.timeout}" retries="${jsf.cancelOrderApi.retries}">
        <jsf:parameter key="token" value="${jsf.cancelOrderApi.token}" hide="true"/>
    </jsf:consumer>

    <!--订单中间件接口-->
    <jsf:consumer id="orderMiddlewareServiceJsf"
                  interface="com.jd.jdorders.component.export.OrderMiddlewareCBDExport"  filter="consumerChainFilter"
                  alias="${jsf.orderMiddlewareCBD.alias}" protocol="jsf" timeout="${jsf.orderMiddlewareCBD.timeout}"
                  serialization="hessian">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${jsf.orderMiddlewareCBD.authToken}" hide="true"/>
        <!-- 藏经阁客户端名称 -->
        <jsf:parameter key="clientName" value="${jsf.orderMiddlewareCBD.clientName}" hide="true"/>
        <!-- 藏经阁JSF分组名，和上边的alias一致 -->
        <jsf:parameter key="alias" value="${jsf.orderMiddlewareCBD.alias}" hide="true"/>
    </jsf:consumer>

    <!-- 商城台账查询服务 -->
    <jsf:consumer id="jdOrderQueryResource" interface="com.jd.orderbank.export.rest.OrderQueryResource"
                  timeout="${jsf.jdOrderBank.OrderQueryResource.timeout}" retries="1"  filter="consumerChainFilter"
                  protocol="jsf" alias="${jsf.jdOrderBank.OrderQueryResource.alias}" serialization="hessian">
    </jsf:consumer>

    <!-- 台账查询服务 -->
    <jsf:consumer id="orderQueryResource" interface="com.jd.ots.orderbank.export.rest.OrderQueryResource"
                  timeout="${jsf.orderQueryResource.timeout}" retries="${jsf.orderQueryResource.retries}" filter="consumerChainFilter"
                  protocol="jsf" alias="${jsf.orderQueryResource.alias}" serialization="hessian">
    </jsf:consumer>

    <!--产品中心-->
    <jsf:consumer id="productRecommendationService" interface="cn.jdl.pms.api.ProductRecommendationService"
                  protocol="jsf" serialization="hessian" alias="${jsf.productRecommendationService.alias}"  filter="consumerChainFilter"
                  timeout="${jsf.productRecommendationService.timeout}" retries="${jsf.productRecommendationService.retries}">
        <jsf:parameter key="token" hide="true" value="${jsf.productRecommendationService.token}"/>
    </jsf:consumer>

    <!--鸡毛信服务物联网设备接口-->
    <jsf:consumer id="iotDeviceWS" interface="com.jd.tms.iot.ws.IotDeviceWS"
                  alias="${jsf.iotDeviceWS.alias}" protocol="jsf" retries="${jsf.iotDeviceWS.retries}"  filter="consumerChainFilter"
                  timeout="${jsf.iotDeviceWS.timeout}" serialization="hessian">
        <jsf:parameter key="token" value="${jsf.iotDeviceWS.token}" hide="true"/>
    </jsf:consumer>

    <!--优惠券查询服务-->
    <jsf:consumer id="couponUseQueryService" interface="com.jd.lcmc.out.service.CouponUseQueryService"
                  alias="${jsf.couponUseQueryService.alias}" protocol="jsf"
                  retries="${jsf.couponUseQueryService.retries}"  filter="consumerChainFilter"
                  timeout="${jsf.couponUseQueryService.timeout}" serialization="hessian">
    </jsf:consumer>

    <!--优惠券使用服务-->
    <jsf:consumer id="couponUseService" interface="com.jd.lcmc.out.service.CouponUseService"
                  alias="${jsf.couponUseService.alias}" protocol="jsf" retries="${jsf.couponUseService.retries}"  filter="consumerChainFilter"
                  timeout="${jsf.couponUseService.timeout}" serialization="hessian">
    </jsf:consumer>

    <!--预分拣逻辑计算统一接口(新) yangjianmin-->
    <jsf:consumer id="preSortCalcExecutorApi" interface="com.jd.ldop.center.api.presort.PreSortCalcExecutorApi"
                  alias="${jsf.preSortCalcExecutorApi.alias}" protocol="jsf"
                  retries="${jsf.preSortCalcExecutorApi.retries}"  filter="consumerChainFilter"
                  timeout="${jsf.preSortCalcExecutorApi.timeout}" serialization="hessian">
    </jsf:consumer>

    <!--预分拣逻辑计算统一批量接口(新) yangjianmin-->
    <jsf:consumer id="presortCalcBatchApi" interface="com.jd.ldop.center.api.presort.PresortCalcBatchApi"
                  alias="${jsf.presortCalcBatchApi.alias}" protocol="jsf" retries="${jsf.presortCalcBatchApi.retries}"  filter="consumerChainFilter"
                  timeout="${jsf.presortCalcBatchApi.timeout}" serialization="hessian">
    </jsf:consumer>

    <!--根据站点ID获取站点信息-->
    <jsf:consumer id="basicPrimaryWS" interface="com.jd.ql.basic.ws.BasicPrimaryWS"
                  alias="${jsf.basicPrimaryWS.alias}" protocol="jsf" retries="${jsf.basicPrimaryWS.retries}"  filter="consumerChainFilter"
                  timeout="${jsf.basicPrimaryWS.timeout}" serialization="hessian">
        <jsf:parameter key="token" value="${jsf.basicPrimaryWS.token}" hide="true"/>
    </jsf:consumer>

    <!--计费询价-->
    <jsf:consumer id="standardProductInquiryApi" interface="com.jd.lbs.product.inquiry.api.StandardProductInquiryApi"
                  alias="${jsf.standardProductInquiryApi.alias}" protocol="jsf"
                  retries="${jsf.standardProductInquiryApi.retries}"  filter="consumerChainFilter"
                  timeout="${jsf.standardProductInquiryApi.timeout}" serialization="hessian">
        <jsf:parameter key="token" value="${jsf.standardProductInquiryApi.token}" hide="true"/>
    </jsf:consumer>

    <!--外发计费询价-->
    <jsf:consumer id="wfStandardProductInquiryApi" interface="com.jd.lbs.product.inquiry.api.StandardProductInquiryApi" filter="consumerChainFilter"
                  alias="${jsf.wfStandardProductInquiryApi.alias}" protocol="jsf" retries="${jsf.wfStandardProductInquiryApi.retries}"
                  timeout="${jsf.wfStandardProductInquiryApi.timeout}" serialization="hessian">
        <jsf:parameter key="token" value="${jsf.standardProductInquiryApi.token}" hide="true"/>
    </jsf:consumer>

    <!--计费事后价询价MTD -->
    <jsf:consumer id="standardProductAndDiscountInquiryApi" interface="com.jd.lbs.product.inquiry.api.StandardProductAndDiscountInquiryApi"  filter="consumerChainFilter"
                  alias="${jsf.standardProductAndDiscountInquiryApi.alias}" protocol="jsf" retries="${jsf.standardProductAndDiscountInquiryApi.retries}"
                  timeout="${jsf.standardProductAndDiscountInquiryApi.timeout}" serialization="hessian">
        <jsf:parameter key="token" value="${jsf.standardProductAndDiscountInquiryApi.token}" hide="true"/>
    </jsf:consumer>

    <!--计费费用拆分-->
    <jsf:consumer id="standardInquiryResultProcessApi" interface="com.jd.lbs.product.inquiry.api.StandardInquiryResultProcessApi"
                  protocol="jsf" serialization="hessian"
                  alias="${jsf.standardInquiryResultProcessApi.alias}"
                  retries="${jsf.standardInquiryResultProcessApi.retries}"  filter="consumerChainFilter"
                  timeout="${jsf.standardInquiryResultProcessApi.timeout}" >
        <jsf:parameter key="token" value="${jsf.standardInquiryResultProcessApi.token}" hide="true"/>
    </jsf:consumer>

    <!--pos台账-->
    <jsf:consumer id="webPosPayOrderServer" interface="com.jd.pos.jsf.WebPosPayOrderServer"
                  alias="${jsf.webPosPayOrderServer.alias}" protocol="jsf" retries="${jsf.webPosPayOrderServer.retries}" filter="consumerChainFilter"
                  timeout="${jsf.webPosPayOrderServer.timeout}" serialization="hessian">
    </jsf:consumer>

    <!--pos直连-->
    <jsf:consumer id="yunOrderServer" interface="com.jd.pos.jsf.yunorder.YunOrderServer"
                  alias="${jsf.posYunOrderServer.alias}" protocol="jsf" retries="${jsf.posYunOrderServer.retries}"  filter="consumerChainFilter"
                  timeout="${jsf.posYunOrderServer.timeout}" serialization="hessian">
    </jsf:consumer>

    <!--B商家创建应收-->
    <jsf:consumer id="receivableJSF" interface="com.jd.supplierrecon.rpc.server.ReceivableJSF"
                  protocol="jsf" serialization="hessian" alias="${jsf.receivableJSF.alias}" retries="${jsf.receivableJSF.retries}"  filter="consumerChainFilter"
                  timeout="${jsf.receivableJSF.timeout}">
    </jsf:consumer>

    <!--B商家修改应收-->
    <jsf:consumer id="modifyDueJSF" interface="com.jd.supplierrecon.rpc.server.ModifyDueJSF"
                  protocol="jsf" serialization="hessian" alias="${jsf.modifyDueJSF.alias}" retries="${jsf.modifyDueJSF.retries}"  filter="consumerChainFilter"
                  timeout="${jsf.modifyDueJSF.timeout}">
    </jsf:consumer>
    <!--B商家查询应收-->
    <jsf:consumer id="queryPaymentInfo" interface="com.jd.supplierrecon.rpc.server.QueryPaymentInfo"
                  protocol="jsf" serialization="hessian" alias="${jsf.queryPaymentInfo.alias}" retries="${jsf.queryPaymentInfo.retries}"  filter="consumerChainFilter"
                  timeout="${jsf.queryPaymentInfo.timeout}">
    </jsf:consumer>
    <!-- 删除订单 -->
    <jsf:consumer id="deleteOrderApi" interface="com.jdl.cp.op.client.api.DeleteOrderApi"
                  alias="${jsf.deleteOrderApi.alias}" protocol="jsf" serialization="hessian"  filter="consumerChainFilter"
                  timeout="${jsf.deleteOrderApi.timeout}" retries="${jsf.deleteOrderApi.retries}">
        <jsf:parameter key="token" value="${jsf.deleteOrderApi.token}" hide="true"/>
    </jsf:consumer>

    <!-- 订单取消下发ofc接口 -->
    <jsf:consumer id="cancelOrderOfcService"
                  interface="cn.jdl.oms.express.api.ofc.CancelOrderOfcService"
                  alias="${jsf.consumer.cancel.express.order}" serialization="hessian"  filter="consumerChainFilter"
                  protocol="jsf"
                  timeout="${jsf.consumer.cancel.express.order.timeout}" retries="${jsf.consumer.cancel.express.order.retries}">
        <jsf:parameter key="token" value="${jsf.cancelOrderOfcService.token}" hide="true"/>
    </jsf:consumer>
    <!-- 订单删除下发ofc接口 -->
    <jsf:consumer id="deleteOrderOfcService"
                  interface="cn.jdl.oms.express.api.ofc.DeleteOrderOfcService"
                  alias="${jsf.consumer.delete.express.order}" serialization="hessian"  filter="consumerChainFilter"
                  protocol="jsf"
                  timeout="${jsf.consumer.delete.express.order.timeout}" retries="${jsf.consumer.delete.express.order.retries}">
        <jsf:parameter key="token" value="${jsf.deleteOrderOfcService.token}" hide="true"/>
    </jsf:consumer>
    <!-- 订单修改下发ofc接口 -->
    <jsf:consumer id="modifyOrderOfcService"
                  interface="cn.jdl.oms.express.api.ofc.ModifyOrderOfcService"
                  alias="${jsf.consumer.modify.express.order}" serialization="hessian"  filter="consumerChainFilter"
                  protocol="jsf"
                  timeout="${jsf.consumer.modify.express.order.timeout}" retries="${jsf.consumer.modify.express.order.retries}">
        <jsf:parameter key="token" value="${jsf.modifyOrderOfcService.token}" hide="true"/>
    </jsf:consumer>
    <!--pos到付应收-->
    <jsf:consumer id="webPosYunServer" interface="com.jd.pos.jsf.WebPosYunServer"
                  alias="${jsf.webPosYunServer.alias}" protocol="jsf"  filter="consumerChainFilter"
                  timeout="${jsf.webPosYunServer.timeout}" retries="${jsf.webPosYunServer.retries}" serialization="hessian">
        <jsf:parameter key="token" value="${jsf.webPosYunServer.token}" hide="true"/>
    </jsf:consumer>

    <jsf:consumer id="addressOrgJsfService" interface="com.jd.lf.os.jsf.AddressOrgJsfService"
                  alias="${jsf.addressOrgJsfService.alias}" protocol="jsf" retries="${jsf.addressOrgJsfService.retries}"  filter="consumerChainFilter"
                  timeout="${jsf.addressOrgJsfService.timeout}" serialization="hessian">
    </jsf:consumer>

    <jsf:consumer id="basicSiteQueryWS" interface="com.jd.ql.basic.ws.BasicSiteQueryWS"
                  alias="${jsf.BasicSiteQueryWS.alias}" protocol="jsf" retries="${jsf.BasicSiteQueryWS.retries}"  filter="consumerChainFilter"
                  timeout="${jsf.BasicSiteQueryWS.timeout}" serialization="hessian">
        <jsf:parameter key="token" value="${jsf.BasicSiteQueryWS.token}" hide="true"/>
    </jsf:consumer>

    <!--微信取消先享单-->
    <jsf:consumer id="weChatPaymentApi" interface="com.jd.ql.terminal.payment.api.WeChatPaymentApi"
                  alias="${jsf.weChatPaymentServer.alias}" protocol="jsf"  filter="consumerChainFilter"
                  timeout="${jsf.weChatPaymentServer.timeout}" retries="${jsf.weChatPaymentServer.retries}" serialization="hessian">
    </jsf:consumer>

    <!--创建外单台账应收-->
    <jsf:consumer id="orderResource" interface="com.jd.ots.orderbank.export.rest.OrderResource"
                  alias="${jsf.orderResource.alias}" protocol="jsf"  filter="consumerChainFilter"
                  timeout="${jsf.orderResource.timeout}" retries="${jsf.orderResource.retries}" serialization="hessian">
    </jsf:consumer>
    <!--校验运单号-->
    <jsf:consumer id="waybillCodeGenericValidateApi" interface="com.jd.cp.wbms.wcs.client.api.WaybillCodeGenericValidateApi"
                  alias="${jsf.waybill.check.alias}" protocol="jsf"  filter="consumerChainFilter"
                  timeout="${jsf.waybill.check.timeout}" retries="${jsf.waybill.check.retries}" serialization="hessian">
        <jsf:parameter key="token" value="${jsf.waybill.check.token}" hide="true"/>
    </jsf:consumer>
    <!--释放运单号-->
    <jsf:consumer id="waybillCodeUnLockApi" interface="com.jd.cp.wbms.wcs.client.api.WaybillCodeUnLockApi"
                  alias="${jsf.waybill.unlock.alias}" protocol="jsf"  filter="consumerChainFilter"
                  timeout="${jsf.waybill.unlock.timeout}" retries="${jsf.waybill.unlock.retries}" serialization="hessian">
        <jsf:parameter key="token" value="${jsf.waybill.unlock.token}" hide="true"/>
    </jsf:consumer>
    <!--获取运单号-->
    <jsf:consumer id="waybillCodeGenericGenerateApi" interface="com.jd.cp.wbms.wcs.client.api.WaybillCodeGenericGenerateApi"
                  alias="${jsf.waybill.get.alias}" protocol="jsf" serialization="hessian"  filter="consumerChainFilter"
                  timeout="${jsf.waybill.get.timeout}" retries="${jsf.waybill.get.retries}">
        <jsf:parameter key="token" value="${jsf.waybill.get.token}" hide="true"/>
    </jsf:consumer>
    <!--外单台账-外单退款-->
    <jsf:consumer id="gateWayService" interface="com.jd.project.server.jsf.orderoutserver.IOrderOutRefundService"
                  alias="${jsf.orderOutServer.alias}" protocol="jsf"  filter="consumerChainFilter"
                  timeout="${jsf.orderOutServer.timeout}" retries="${jsf.orderOutServer.retries}" serialization="hessian">
        <jsf:parameter key="token" value="${jsf.orderoutserver.token}" hide="true"/>
    </jsf:consumer>
    <!--退款网关接口-->
    <jsf:consumer id="refundGatewayChannelService" interface="com.jd.paygateway.project.server.IRefundGatewayChannelService"
                  alias="${jsf.refundGatewayApi.alias}" protocol="jsf"  filter="consumerChainFilter"
                  timeout="${jsf.refundGatewayApi.timeout}" retries="${jsf.refundGatewayApi.retries}" serialization="hessian">
    </jsf:consumer>
    <!--O2O询价-->
    <jsf:consumer id="freightQueryApi" interface="com.jd.o2o.provider.api.FreightQueryApi"
                  alias="${jsf.freightQueryApi.alias}" protocol="jsf"  filter="consumerChainFilter"
                  timeout="${jsf.freightQueryApi.timeout}" retries="${jsf.freightQueryApi.retries}" serialization="hessian">
    </jsf:consumer>
    <!-- 白条预授权 -->
    <jsf:consumer id="accountInfoFacade" interface="com.jd.logistics.facade.AccountInfoFacade"
                  alias="${jsf.accountInfoFacade.alias}"  filter="consumerChainFilter"
                  protocol="jsf" timeout="${jsf.accountInfoFacade.timeout}" retries="${jsf.accountInfoFacade.retries}" serialization="hessian">
    </jsf:consumer>

    <!-- 校验pin和商家编码是否匹配、商家编码是否在黑名单中 -->
    <jsf:consumer id="orderApi" interface="com.jd.ldop.oms.api.order.OrderApi" alias="${jsf.orderApi.alias}"
                  protocol="jsf" timeout="${jsf.accountInfoFacade.timeout}" retries="${jsf.orderApi.retries}" check="false">
    </jsf:consumer>

    <!-- 违禁品黑白名单接口 -->
    <jsf:consumer id="embargoVerificationApi" interface="com.jd.express.dispatcher.api.service.EmbargoVerificationApi" alias="${jsf.contraband.alias}"
                  protocol="jsf" timeout="${jsf.contraband.timeout}" retries="0" check="false" serialization="hessian">
    </jsf:consumer>

    <!-- 运单详情 -->
    <jsf:consumer id="generalWaybillQueryApi" interface="com.jd.ldop.center.api.waybill.GeneralWaybillQueryApi"
                  alias="${jsf.generalWaybillQueryApi.alias}" timeout="${jsf.generalWaybillQueryApi.timeout}" retries="${jsf.generalWaybillQueryApi.retries}"
                  protocol="jsf"  serialization="hessian">
    </jsf:consumer>

    <!--pin信息查询-->
    <jsf:consumer id="e2cCustomerRelationJsfService" interface="com.jd.ql.e2e.ws.jsf.E2CCustomerRelationJsfService"
                  protocol="jsf" alias="${jsf.e2cCustomerRelationJsfService.alias}"  filter="consumerChainFilter"
                  timeout="${jsf.e2cCustomerRelationJsfService.timeout}"
                  retries="${jsf.e2cCustomerRelationJsfService.retries}" serialization="hessian">

    </jsf:consumer>

    <!-- 积分 -->
    <jsf:consumer id="integralOperateApi" interface="com.jd.jdwl.jdl.integrate.middleground.api.IntegralOperateApi"
                  protocol="jsf" alias="${jsf.integralOperateApi.alias}" timeout="${jsf.integralOperateApi.timeout}"  filter="consumerChainFilter"
                  retries="${jsf.integralOperateApi.retries}" serialization="hessian">
    </jsf:consumer>

    <jsf:consumer id="integralSearchApi" interface="com.jd.jdwl.jdl.integrate.middleground.api.IntegralSearchApi"
                  protocol="jsf" alias="${jsf.integralSearchApi.alias}" timeout="${jsf.integralSearchApi.timeout}"  filter="consumerChainFilter"
                  retries="${jsf.integralSearchApi.retries}" serialization="hessian">
    </jsf:consumer>

    <!--e卡信息查询-->
    <jsf:consumer id="grayECardServiceJsfService" interface="com.jd.coo.cms.api.GrayECardService"
                  protocol="jsf" alias="${jsf.eCardService.alias}" timeout="${jsf.eCardService.timeout}"  filter="consumerChainFilter"
                  retries="${jsf.eCardService.retries}" serialization="hessian">
    </jsf:consumer>

    <!--自寄柜查询接口-->
    <jsf:consumer id="thirdCollectQueryApi" interface="com.jd.ql.terminal.collect.api.ThirdCollectQueryApi"
                  protocol="jsf" alias="${jsf.terminal.alias}" timeout="${jsf.timeout.alias}"  filter="consumerChainFilter"
                  retries="${jsf.terminal.retries}" serialization="hessian">
    </jsf:consumer>

    <!--判断订单是否存在-->
    <jsf:consumer id="getOrderNoApi" interface="com.jdl.cp.osc.client.api.GetOrderNoApi" alias="${jsf.getOrderNoApi.alias}"
                  protocol="jsf" timeout="${jsf.getOrderNoApi.timeout}"  filter="consumerChainFilter"
                  retries="${jsf.getOrderNoApi.retries}" serialization="hessian">
        <jsf:parameter key="token" hide="true" value="${jsf.getOrderNoApi.token}"/>
    </jsf:consumer>


    <!--判断订单是否存在-->
    <jsf:consumer id="waybillCompleteApi" interface="com.jd.ldop.center.api.waybill.WaybillCompleteApi" alias="uat-ldop-center"
                  protocol="jsf" timeout="2000"  filter="consumerChainFilter"
                  retries="1" serialization="hessian">
    </jsf:consumer>


    <!-- 中台全程跟踪接收API -->
    <jsf:consumer id="receiveOrderTrackingService" interface="cn.jdl.oms.ordertrack.service.ReceiveOrderTrackingService"
                  protocol="jsf" serialization="hessian" alias="${jsf.order.track.alias}"  filter="consumerChainFilter"
                  timeout="${jsf.order.track.timeout}">
    </jsf:consumer>

    <!-- 现结余额查询接口 -->
    <jsf:consumer id="lfspAccountBookGwService" interface="com.jd.bms.settlement.outward.service.gw.lfsp.LfspAccountBookGwService" protocol="jsf"
                  alias="${jsf.account.book.alias}" timeout="${jsf.account.book.timeout}"  filter="consumerChainFilter"
                  retries="${jsf.account.book.retries}" serialization="hessian">
        <jsf:parameter key="token" hide="true" value="${jsf.account.book.token}"/>
    </jsf:consumer>

    <!-- 财务账户实时预占风控接口 -->
    <jsf:consumer id="bmsAccountRiskOperateFacadeService" interface="com.jd.bms.account.api.outer.BmsAccountRiskOperateFacadeService"
                  protocol="jsf" serialization="hessian"
                  alias="${jsf.account.risk.operate.alias}" timeout="${jsf.account.risk.operate.timeout}"  filter="consumerChainFilter"
                  retries="${jsf.account.risk.operate.retries}">
        <jsf:parameter key="token" hide="true" value="${jsf.account.risk.operate.token}"/>
    </jsf:consumer>

    <!--订单控单: 是否禁单、时效是否升降级-->
    <jsf:consumer id="orderInterceptApi" interface="com.jdl.eclp.business.open.api.order.OrderInterceptApi"
                  protocol="jsf" alias="${jsf.product.adaptation.alias}" timeout="${jsf.product.adaptation.timeout}" serialization="hessian">
    </jsf:consumer>

    <!--产品中心查询接口-->
<!--    <jsf:consumer id="productCenterService" interface="com.jd.open.sp.lpc.service.ProductCenterService"-->
<!--                  timeout="${jsf.product.center.timeout}" retries="0" protocol="jsf" alias="${jsf.product.center.alias}" serialization="hessian">-->
<!--    </jsf:consumer>-->

    <!--产品中心查询接口，新接口-->
    <jsf:consumer id="productCenterService" interface="cn.jdl.pms.basic.api.ProductCenterService"  filter="consumerChainFilter"
                  timeout="${jsf.product.center.timeout}" retries="0" protocol="jsf" alias="${jsf.product.center.alias}" serialization="hessian">
    </jsf:consumer>

    <!--打印流水记录-->
    <jsf:consumer id="orderPrintApi" interface="com.jd.ldop.oms.api.order.OrderPrintApi"  filter="consumerChainFilter"
                  timeout="${jsf.OrderPrintApi.timeout}" retries="0" protocol="jsf" alias="${jsf.OrderPrintApi.alias}" serialization="hessian">
    </jsf:consumer>

    <!-- 状态修改 -->
    <jsf:consumer id="modifyOrderStatusApi" interface="com.jdl.cp.op.client.api.ModifyOrderStatusApi" alias="${jsf.modifyOrderStatusApi.alias}"
                  protocol="jsf" timeout="${jsf.modifyOrderStatusApi.timeout}"  filter="consumerChainFilter"
                  retries="${jsf.modifyOrderStatusApi.retries}" serialization="hessian">
        <jsf:parameter key="token" hide="true" value="${jsf.modifyOrderStatusApi.token}"/>
    </jsf:consumer>

    <!-- 取消拦截接口 -->
    <jsf:consumer id="waybillOperatorApi" interface="com.jd.ldop.center.api.waybill.client.WaybillOperatorApi"
                  alias="${jsf.waybillOperatorApi.alias}" timeout="${jsf.waybillOperatorApi.timeout}" protocol="jsf"
                  serialization="hessian" retries="${jsf.waybillOperatorApi.retries}">
    </jsf:consumer>
    <!--青龙三合一接单-->
    <jsf:consumer id="waybillIntegrateReceiveApi" interface="com.jd.ldop.delivery.api.WaybillIntegrateReceiveApi"
                  alias="${jsf.waybillIntegrateReceiveApi.alias}" protocol="jsf"
                  timeout="${jsf.waybillIntegrateReceiveApi.timeout}" serialization="hessian">
    </jsf:consumer>

    <!-- 快递取件码服务 -->
    <jsf:consumer id="messageServiceApi" interface="com.jdl.express.resource.api.message.MessageServiceApi"
                  alias="${jsf.messageServiceApi.alias}"
                  protocol="jsf" timeout="${jsf.messageServiceApi.timeout}"
                  retries="${jsf.messageServiceApi.retries}" serialization="hessian">
    </jsf:consumer>

    <!--地址围栏接口-->
    <jsf:consumer id="customPresortService"
                  interface="com.jd.lbs.geofencing.api.customfence.CustomPresortService"
                  alias="${jsf.customPresortService.alias}" protocol="jsf"
                  timeout="${jsf.customPresortService.timeout}" serialization="hessian"
                  retries="${jsf.customPresortService.retries}">
        <jsf:parameter key="token" value="${jsf.customPresortService.token}" hide="true"/>
    </jsf:consumer>

    <!-- 数科敏感词服务新 -->
    <jsf:consumer id="commentModerationService" interface="com.jd.ai.comment.moderation.service.CommentModerationService"
                  timeout="${jsf.commentModerationService.timeout}" retries="${jsf.commentModerationService.retries}"
                  protocol="jsf" alias="${jsf.commentModerationService.alias}">
    </jsf:consumer>

    <!--零售厂直订单查询接口-->
    <jsf:consumer id="iOrderSearchService"
                  interface="com.jd.dropship.center.api.service.IOrderSearchService"
                  alias="${jsf.dropship.center.alias}" protocol="jsf" timeout="${jsf.dropship.center.timeout}"
                  retries="${jsf.dropship.center.retries}"
                  serialization="hessian">
    </jsf:consumer>

    <!--揽收时间范围查询接口-->
    <jsf:consumer id="pickupRangeExtApi"
                  interface="com.jd.etms.vrs.api.pickuprange.PickupRangeExtApi"
                  alias="${jsf.pickup.range.ext.alias}" protocol="jsf" timeout="${jsf.pickup.range.ext.timeout}"
                  serialization="hessian">
    </jsf:consumer>

    <!-- 关联关系存储服务 -->
    <jsf:consumer id="createOrderRelationService" interface="cn.jdl.oms.relation.service.CreateOrderRelationService"
                  alias="${jsf.createOrderRelationService.alias}"
                  timeout="${jsf.createOrderRelationService.timeout}"
                  retries="${jsf.createOrderRelationService.retries}"  filter="consumerChainFilter"
                  protocol="jsf" serialization="hessian">
    </jsf:consumer>

    <!-- 京标转国标服务 -->
    <jsf:consumer id="gbDistrictJDDistrictMapService" interface="com.jd.addresstranslation.api.address.GBDistrictJDDistrictMapService"
                  alias="${jsf.gBDistrictJDDistrictMapService.alias}"
                  timeout="${jsf.gBDistrictJDDistrictMapService.timeout}"
                  retries="${jsf.gBDistrictJDDistrictMapService.retries}"
                  protocol="jsf" serialization="hessian">
    </jsf:consumer>

    <!-- 外单取件获取站点 -->
    <jsf:consumer id="tosDispatchQueryApi" interface="com.jd.tos.dispatch.ws.jsf.TosDispatchQueryApi"
                  alias="${tos.dispatch.ws.jsf.alias}"
                  timeout="${tos.dispatch.ws.jsf.timeout}"
                  retries="${tos.dispatch.ws.jsf.retries}"
                  protocol="jsf" serialization="hessian">
    </jsf:consumer>

    <!-- 配置中心-配置方案-配置值读取服务 -->
    <jsf:consumer id="lpcJsfProductCenterService" interface="com.jd.open.sp.lpc.service.ProductCenterService"
                  alias="${jsf.lpcProductCenterService.alias}"
                  timeout="${jsf.lpcProductCenterService.timeout}"
                  retries="${jsf.lpcProductCenterService.retries}"
                  protocol="jsf" serialization="hessian">
    </jsf:consumer>

    <!-- 大件拒单下单校验/查询服务接口 -->
    <jsf:consumer id="lasRefuseJsfService" interface="com.jd.las.waybill.adapter.refuse.service.out.LasRefuseJsfService"
                  alias="${jsf.lasRefuseJsfService.alias}"
                  timeout="${jsf.lasRefuseJsfService.timeout}"
                  retries="${jsf.lasRefuseJsfService.retries}"
                  protocol="jsf" serialization="hessian">
    </jsf:consumer>


    <!--产品中心查询接口，附加费综合查询接口-->
    <!-- https://joyspace.jd.com/pages/Ijm5yQVrDLrGkMlEhARt -->
    <jsf:consumer id="productSurchargeInfoService" interface="cn.jdl.pms.basic.api.ProductSurchargeInfoService"
                  timeout="${jsf.product.center.surcharge.timeout}"
                  retries="${jsf.product.center.surcharge.retries}"
                  protocol="jsf"
                  alias="${jsf.product.center.surcharge.alias}"  filter="consumerChainFilter"
                  serialization="hessian">
    </jsf:consumer>

    <!-- 关联关系查询服务 -->
    <jsf:consumer id="queryOrderRelationService" interface="cn.jdl.oms.relation.service.QueryOrderRelationService"
                  alias="${jsf.queryOrderRelationService.alias}"
                  timeout="${jsf.queryOrderRelationService.timeout}"
                  retries="${jsf.queryOrderRelationService.retries}"  filter="consumerChainFilter"
                  protocol="jsf" serialization="hessian">
    </jsf:consumer>

    <!-- 京东保险保费预展 -->
    <jsf:consumer id="logisticsClaimService" interface="com.jd.insure.claim.export.service.logistics.LogisticsClaimService"
                  alias="${jsf.logisticsClaimService.alias}"
                  timeout="${jsf.logisticsClaimService.timeout}"
                  retries="${jsf.logisticsClaimService.retries}"  filter="consumerChainFilter"
                  protocol="jsf" serialization="hessian">
    </jsf:consumer>

    <!-- 京东保险保费预占 new (上门取件) -->
    <jsf:consumer id="logisticPlatformClaimServiceJsf" interface="com.jd.insure.claim.export.service.freight.LogisticPlatformClaimService"
                  alias="${jsf.logisticPlatformClaimServiceJsf.alias}"
                  timeout="${jsf.logisticPlatformClaimServiceJsf.timeout}"
                  retries="${jsf.logisticPlatformClaimServiceJsf.retries}"  filter="consumerChainFilter"
                  protocol="jsf" serialization="hessian">
    </jsf:consumer>

    <!-- 开放发票服务 -->
    <jsf:consumer id="appCashSettlementService" interface="com.jd.bms.settlement.outward.service.AppCashSettlementService"
                  alias="${jsf.openInvoiceService.alias}"
                  timeout="${jsf.openInvoiceService.timeout}"
                  retries="${jsf.openInvoiceService.retries}"  filter="consumerChainFilter"
                  protocol="jsf" serialization="hessian">
        <jsf:parameter key="token" value="${jsf.openInvoiceService.token}" hide="true"/>
    </jsf:consumer>


    <!-- 查询商家结算主体  https://cf.jd.com/pages/viewpage.action?pageId=187581961 -->
    <jsf:consumer id="customerSettlementBodyApi" interface="com.jd.lbs.master.customer.api.CustomerSettlementBodyApi"
                  alias="${jsf.customerSettlementBodyService.alias}"
                  timeout="${jsf.customerSettlementBodyService.timeout}"
                  retries="${jsf.customerSettlementBodyService.retries}"  filter="consumerChainFilter"
                  protocol="jsf" serialization="hessian" >
        <jsf:parameter key="token" value="${jsf.customerSettlementBodyService.token}" hide="true"/>
    </jsf:consumer>

    <!-- 预收月结查询  https://cf.jd.com/pages/viewpage.action?pageId=480926759 -->
    <jsf:consumer id="advanceSurplusAnalysisFacade" interface="com.jd.bms.settlement.outward.service.advance.AdvanceSurplusAnalysisFacade"
                  alias="${jsf.advanceSurplusAnalysisFacade.alias}"
                  timeout="${jsf.advanceSurplusAnalysisFacade.timeout}"
                  retries="${jsf.advanceSurplusAnalysisFacade.retries}"  filter="consumerChainFilter"
                  protocol="jsf" serialization="hessian">
        <jsf:parameter key="token" value="${jsf.advanceSurplusAnalysisFacade.token}" hide="true"/>
    </jsf:consumer>

    <!--通用计费询价-->
    <jsf:consumer id="standardComputeService" interface="com.jd.ccjf.data.service.compute.StandardComputeService"
                  alias="${jsf.standardComputeService.alias}"
                  timeout="${jsf.standardComputeService.timeout}"
                  retries="${jsf.standardComputeService.retries}"  filter="consumerChainFilter"
                  protocol="jsf" serialization="hessian">
    </jsf:consumer>

    <!--运单号查询包装信息-->
    <jsf:consumer id="waybillQueryApi" interface="com.jd.etms.waybill.api.WaybillQueryApi"
                  alias="${jsf.waybillQueryApi.alias}"
                  timeout="${jsf.waybillQueryApi.timeout}"
                  retries="${jsf.waybillQueryApi.retries}"
                  protocol="jsf" serialization="hessian">
    </jsf:consumer>

    <!--运单号查询包裹称重流水-->
    <jsf:consumer id="waybillPackageApi" interface="com.jd.etms.waybill.api.WaybillPackageApi"
                  alias="${jsf.waybillPackageApi.alias}"
                  timeout="${jsf.waybillPackageApi.timeout}"
                  retries="${jsf.waybillPackageApi.retries}"
                  protocol="jsf" serialization="hessian">
    </jsf:consumer>

    <!--运单退款申请-->
    <jsf:consumer id="waybillRefundApi" interface="com.jd.etms.wrefund.api.WaybillRefundApi"
                  alias="${jsf.waybillRefundApi.alias}"
                  timeout="${jsf.waybillRefundApi.timeout}"
                  retries="${jsf.waybillRefundApi.retries}"  filter="consumerChainFilter"
                  protocol="jsf" serialization="hessian">
    </jsf:consumer>

    <!--仓配快递获取虚拟站点-->
    <jsf:consumer id="thirdDistributeQueryApi"
                  interface="com.jd.ldop.alpha.lp.api.ThirdDistributeQueryApi"
                  alias="${jsf.third.distribute.query.alias}" protocol="jsf"
                  timeout="${jsf.third.distribute.query.timeout}"
                  retries="${jsf.third.distribute.query.retries}"
                  serialization="hessian">
    </jsf:consumer>

    <!--订单预处理-->
    <jsf:consumer id="preprocessService"
                  interface="com.jdl.oms.client.api.order.PreprocessService"
                  alias="${jsf.preprocessService.alias}"
                  timeout="${jsf.preprocessService.timeout}"
                  retries="${jsf.preprocessService.retries}"
                  protocol="jsf"
                  serialization="hessian">
        <jsf:parameter key="token" value="${jsf.preprocessService.token}" hide="true"/>
    </jsf:consumer>

    <!-- 持久化-订单状态流水 -->
    <!-- 相同别名不同接口的jsf需要独立配置参数 -->
    <jsf:consumer id="persistOrderStatusApi" interface="com.jdl.cp.op.client.api.PersistOrderStatusApi"
                  alias="${jsf.persistOrderApi.status.alias}"
                  timeout="${jsf.persistOrderApi.status.timeout}"
                  retries="${jsf.persistOrderApi.status.retries}"  filter="consumerChainFilter"
                  protocol="jsf" serialization="hessian">
        <jsf:parameter key="token" value="${jsf.persistOrderApi.status.token}" hide="true"/>
    </jsf:consumer>

    <!-- 统一代扣网关 -->
    <jsf:consumer id="paySplit" interface="com.jd.jr.pay.export.rest.PaySplit"
                  protocol="jsf" serialization="hessian"
                  alias="${jsf.pay.split.alias}"
                  timeout="${jsf.pay.split.timeout}"  filter="consumerChainFilter"
                  retries="${jsf.pay.split.retries}" />


    <!-- 区域服务能力相关服务接口 -->
    <jsf:consumer id="regionServiceAbilityApi" interface="com.jd.ql.dispatch.api.RegionServiceAbilityApi"
                  alias="${jsf.regionServiceAbilityApi.alias}"
                  timeout="${jsf.regionServiceAbilityApi.timeout}"
                  retries="${jsf.regionServiceAbilityApi.retries}"
                  protocol="jsf" serialization="hessian">
        <jsf:parameter key="token" value="${jsf.regionServiceAbilityApi.token}" hide="true"/>
    </jsf:consumer>

    <!-- 青龙-冷链城配-纯配实时计费接口 -->
    <jsf:consumer id="publishedQlLLComputeService" interface="com.jd.ccjf.data.service.compute.QlLLComputeService"
                  alias="${jsf.publishedQlLLComputeService.alias}"
                  timeout="${jsf.publishedQlLLComputeService.timeout}"
                  retries="${jsf.publishedQlLLComputeService.retries}"  filter="consumerChainFilter"
                  protocol="jsf" serialization="hessian">
        <jsf:parameter key="token" value="${jsf.publishedQlLLComputeService.token}" hide="true" />
    </jsf:consumer>

    <!--TMS-Basic基础资料-->
    <jsf:consumer id="basicQueryApi"
                  interface="com.jd.tms.basic.ws.BasicQueryWS"
                  alias="${jsf.tms.basic.alias}" protocol="jsf"
                  timeout="${jsf.tms.basic.timeout}"
                  retries="${jsf.tms.basic.retries}"
                  serialization="hessian">
    </jsf:consumer>

    <!--删除关联关系-->
    <jsf:consumer id="deleteOrderRelationService" interface="cn.jdl.oms.relation.service.DeleteOrderRelationService"
                  alias="${jsf.deleteOrderRelationService.alias}"
                  timeout="${jsf.deleteOrderRelationService.timeout}"
                  retries="${jsf.deleteOrderRelationService.retries}"  filter="consumerChainFilter"
                  protocol="jsf" serialization="hessian">
    </jsf:consumer>

    <!-- eclp-co查包裹信息 -->
    <jsf:consumer id="lwbB2bPackageItemService"
                  interface="com.jd.eclp.bbp.co.lwb.service.LwbB2bPackageItemService"
                  alias="${jsf.lwbB2bPackageItemService.alias}" protocol="jsf"
                  timeout="${jsf.lwbB2bPackageItemService.timeout}"
                  retries="${jsf.lwbB2bPackageItemService.retries}"
                  serialization="hessian">
    </jsf:consumer>

    <!-- eclp-co查主档信息 -->
    <jsf:consumer id="lwbMainService"
                  interface="com.jd.eclp.bbp.co.lwb.service.LwbMainService"
                  alias="${jsf.lwbMainService.alias}" protocol="jsf"
                  timeout="${jsf.lwbMainService.timeout}"
                  retries="${jsf.lwbMainService.retries}"
                  serialization="hessian">
    </jsf:consumer>


    <!--删除关联关系-->
    <jsf:consumer id="modifyExpressOrderService" interface="cn.jdl.oms.express.service.ModifyExpressOrderService"
                  alias="${jsf.modifyExpressOrderService.alias}"
                  timeout="${jsf.modifyExpressOrderService.timeout}"
                  retries="${jsf.modifyExpressOrderService.retries}"  filter="consumerChainFilter"
                  protocol="jsf" serialization="hessian">
    </jsf:consumer>

    <!--外单通用查询接口-->
    <jsf:consumer id="waybillAggQueryAPi" interface="com.jd.ldop.center.api.waybill.common.WaybillAggQueryAPi"
                  alias="${jsf.waybillAggQueryAPi.alias}"
                  timeout="${jsf.waybillAggQueryAPi.timeout}"
                  retries="${jsf.waybillAggQueryAPi.retries}"  filter="consumerChainFilter"
                  protocol="jsf" serialization="hessian">
    </jsf:consumer>

    <!--eclp异常中心-->
    <jsf:consumer id="exceptionService" interface="com.jd.eclp.exception.api.ExceptionService"
                  alias="${jsf.exceptionService.alias}"
                  timeout="${jsf.exceptionService.timeout}"
                  retries="${jsf.exceptionService.retries}"
                  protocol="jsf" serialization="hessian">
    </jsf:consumer>

    <!-- 权益中台权益预占 -->
    <jsf:consumer id="freeUserRightsWriteService"
                  interface="com.jd.omni.user.rights.backbone.api.service.free.FreeUserRightsWriteService"
                  alias="${jsf.FreeUserRightsWriteService.alias}" protocol="jsf"
                  timeout="${jsf.FreeUserRightsWriteService.timeout}"
                  retries="${jsf.FreeUserRightsWriteService.retries}"  filter="consumerChainFilter"
                  serialization="hessian">
    </jsf:consumer>

    <!--tms询价服务-->
    <jsf:consumer id="rfqEnquiryWS" interface="com.jd.tms.rfq.ws.RfqEnquiryWS"
                  alias="${jsf.rfqEnquiryWS.alias}" protocol="jsf" retries="${jsf.rfqEnquiryWS.retries}"  filter="consumerChainFilter"
                  timeout="${jsf.rfqEnquiryWS.timeout}" serialization="hessian">
    </jsf:consumer>

    <!--冷链实时询价-->
    <jsf:consumer id="llClientInquiryService" interface="com.jd.ccjf.data.service.compute.LLClientInquiryService"
                  alias="${jsf.llClientInquiryService.alias}" protocol="jsf" retries="${jsf.llClientInquiryService.retries}" filter="consumerChainFilter"
                  timeout="${jsf.llClientInquiryService.timeout}" serialization="hessian">
        <jsf:parameter key="token" value="${jsf.llClientInquiryService.token}" hide="true" />
    </jsf:consumer>

    <!-- 青龙全程跟踪 -->
    <jsf:consumer id="waybillTraceApi" interface="com.jd.etms.waybill.api.WaybillTraceApi"  filter="consumerChainFilter"
                  alias="${jsf.waybillTraceApi.alias}" protocol="jsf" retries="${jsf.waybillTraceApi.retries}"
                  timeout="${jsf.waybillTraceApi.timeout}" serialization="hessian">
    </jsf:consumer>


    <!--收入集成-->
    <jsf:consumer id="ebsService" interface="com.jd.lbs.ebs.service.EBSService"
                  alias="${jsf.ebsService.alias}"
                  timeout="${jsf.ebsService.timeout}"
                  retries="${jsf.ebsService.retries}"  filter="consumerChainFilter"
                  protocol="jsf" serialization="hessian">
    </jsf:consumer>

    <!--京东零售:跨境业务-换汇接口-->
    <!-- https://cf.jd.com/pages/viewpage.action?pageId=632114714 -->
    <jsf:consumer id="exchangeRateReadRpcService" interface="com.jdw.sc.price.exchange.rate.service.api.read.ExchangeRateReadRpcService"
                  alias="${jsf.sc.price.exchange.rate.alias}"
                  timeout="${jsf.sc.price.exchange.rate.timeout}"
                  retries="${jsf.sc.price.exchange.rate.retries}"
                  protocol="jsf" serialization="hessian">
    </jsf:consumer>

    <!-- 仓位预占接口（专线） -->
    <jsf:consumer id="shippingLineWS" interface="com.jd.tms.csc.ws.ShippingLineWS"
                  alias="${jsf.ecpSpecialLineWS.alias}" protocol="jsf" serialization="hessian"
                  timeout="${jsf.ecpSpecialLineWS.timeout}" retries="${jsf.ecpSpecialLineWS.retries}">
        <jsf:parameter key="token" value="${jsf.ecpSpecialLineWS.token}" hide="true" />
    </jsf:consumer>

    <!-- 保险物损理赔 -->
    <jsf:consumer id="logisticsClaimResource" interface="com.jd.insure.inter.claim.export.resource.LogisticsClaimResource"
                  alias="${jsf.logistics.claim.resource.alias}"
                  timeout="${jsf.logistics.claim.resource.timeout}"
                  retries="${jsf.logistics.claim.resource.retries}"
                  protocol="jsf" serialization="hessian">
    </jsf:consumer>

    <!--关闭支付二维码-->
    <jsf:consumer id="qrCloseService" interface="com.jd.jr.pospay.mobile.gateway.export.inf.QrCloseService"
                  alias="${jsf.qrCloseService.alias}"
                  timeout="${jsf.qrCloseService.timeout}"
                  retries="${jsf.qrCloseService.retries}"
                  protocol="jsf" serialization="hessian">
    </jsf:consumer>

    <!-- 查询青龙基础资料员工信息 -->
    <jsf:consumer id="basicStaffQueryWS" interface="com.jd.ql.basic.ws.BasicStaffQueryWS"
                  alias="${jsf.basicStaffQueryWS.alias}"
                  timeout="${jsf.basicStaffQueryWS.timeout}"
                  retries="${jsf.basicStaffQueryWS.retries}"
                  protocol="jsf" serialization="hessian">
        <jsf:parameter key="token" value="${jsf.basicStaffQueryWS.token}" hide="true"/>
    </jsf:consumer>

    <!-- 非商城订单应收 -->
    <jsf:consumer id="webPosExtOrderServer" interface="com.jd.pos.jsf.WebPosExtOrderServer"
                  alias="${jsf.webPosExtOrderServer.alias}"
                  timeout="${jsf.webPosExtOrderServer.timeout}"
                  retries="${jsf.webPosExtOrderServer.retries}"
                  protocol="jsf" serialization="hessian">
        <jsf:parameter key="token" value="${jsf.webPosExtOrderServer.token}" hide="true"/>
    </jsf:consumer>

    <!--产品映射-->
    <jsf:consumer id="productMappingService" interface="com.jdl.product.api.ProductMappingService"
                  protocol="jsf" serialization="hessian" alias="${jsf.productMappingService.alias}"
                  timeout="${jsf.productMappingService.timeout}" retries="${jsf.productMappingService.retries}">
        <jsf:parameter key="token" hide="true" value="${jsf.productMappingService.token}"/>
    </jsf:consumer>

    <!--总代系统-->
    <jsf:consumer id="createOrderPayService" interface="com.wy.export.inf.CreateOrderPayService"
                  alias="${jsf.createOrderPayService.alias}"
                  timeout="${jsf.createOrderPayService.timeout}"
                  retries="${jsf.createOrderPayService.retries}"
                  protocol="jsf" serialization="hessian">
    </jsf:consumer>

    <!--站点匹配服务接口[简易预分拣服务]-->
    <jsf:consumer id="stationMatchServiceApi" interface="com.jd.bluedragon.preseparate.jsf.StationMatchServiceApi"
                  alias="${jsf.station.match.alias}" protocol="jsf" retries="${jsf.station.match.retries}"
                  timeout="${jsf.station.match.timeout}" serialization="hessian">
        <jsf:parameter key="token" value="${jsf.station.match.token}" hide="true"/>
    </jsf:consumer>

    <!--敏感词接口-->
    <jsf:consumer id="sensitiveWordJsfService" interface="com.jd.sensitiveword.service.SensitiveWordJsfService"
                  alias="${jsf.sensitive.words.alias}" protocol="jsf"
                  retries="${jsf.sensitive.words.retries}" timeout="${jsf.sensitive.words.timeout}">
    </jsf:consumer>

    <!-- 芝麻代扣-支付单 -->
    <jsf:consumer id="payCreditResource" interface="com.jd.payment.gateway.core.export.pay.PayCreditResource"
                  alias="${jsf.alipay.order.alias}" protocol="jsf"
                  retries="${jsf.alipay.order.retries}" timeout="${jsf.alipay.order.timeout}">
    </jsf:consumer>

    <!--服务加服务单接口-->
    <jsf:consumer id="commonOrderService" interface="com.jd.serviceplus.order.api.CommonOrderService"
                  alias="${jsf.serviceplus.alias}" protocol="jsf"
                  retries="${jsf.serviceplus.retries}" timeout="${jsf.serviceplus.timeout}">
    </jsf:consumer>

    <!--服务加服务单接口-->
    <jsf:consumer id="afsServiceInfoApi" interface="com.jd.afs.biz.center.api.serviceBill.AfsServiceInfoApi"
                  alias="${jsf.afsCenter.alias}" protocol="jsf"
                  retries="${jsf.afsCenter.retries}"
                  timeout="${jsf.afsCenter.timeout}">
    </jsf:consumer>

    <!--取消关联配送单-->
    <jsf:consumer id="cancelExpressOrderService" interface="cn.jdl.oms.express.service.CancelExpressOrderService"
                  alias="${jsf.cancelExpressOrderService.alias}"
                  timeout="${jsf.cancelExpressOrderService.timeout}"
                  retries="${jsf.cancelExpressOrderService.retries}"  filter="consumerChainFilter"
                  protocol="jsf" serialization="hessian">
    </jsf:consumer>
    <!-- 查询神行异常 -->
    <jsf:consumer id="bscExceptionQueryService" interface="com.jdl.bsc.exception.api.service.BscExceptionQueryService" filter="consumerChainFilter"
                  alias="${jsf.bscExceptionQueryService.alias}" protocol="jsf"
                  retries="${jsf.bscExceptionQueryService.retries}"
                  timeout="${jsf.bscExceptionQueryService.timeout}">
    </jsf:consumer>
    <!-- 更新神行异常 -->
    <jsf:consumer id="bscExceptionUpdateService" interface="com.jdl.bsc.exception.api.service.BscExceptionUpdateService" filter="consumerChainFilter"
                  alias="${jsf.bscExceptionUpdateService.alias}" protocol="jsf"
                  retries="${jsf.bscExceptionUpdateService.retries}"
                  timeout="${jsf.bscExceptionUpdateService.timeout}">
    </jsf:consumer>

    <!--创建关联配送单-->
    <jsf:consumer id="createExpressOrderService" interface="cn.jdl.oms.express.service.CreateExpressOrderService"
                  alias="${jsf.createExpressOrderService.alias}"
                  timeout="${jsf.createExpressOrderService.timeout}"
                  retries="${jsf.createExpressOrderService.retries}"  filter="consumerChainFilter"
                  protocol="jsf" serialization="hessian">
    </jsf:consumer>

    <!--驾车距离计算-->
    <jsf:consumer id="carNavigateService" interface="com.jd.lbs.jdlbsapi.search.CarNavigateService" filter="consumerChainFilter"
                  alias="${jsf.carNavigateService.alias}" protocol="jsf"
                  retries="${jsf.carNavigateService.retries}"
                  timeout="${jsf.carNavigateService.timeout}">
        <jsf:parameter key="token" value="${jsf.carNavigateService.token}" hide="true"/>
    </jsf:consumer>

    <!--查询产品销售时间-->
    <jsf:consumer id="productTimeConfigService" interface="cn.jdl.pms.basic.api.ProductTimeConfigService" filter="consumerChainFilter"
                  alias="${jsf.productTimeConfigService.alias}" protocol="jsf"
                  retries="${jsf.productTimeConfigService.retries}"
                  timeout="${jsf.productTimeConfigService.timeout}">
    </jsf:consumer>

    <!--外单台帐实收接口-->
    <jsf:consumer id="operatePayResource" interface="com.jd.ots.orderbank.export.rest.OperatePayResource" filter="consumerChainFilter"
                  alias="${jsf.operatePayResource.alias}" protocol="jsf"
                  retries="${jsf.operatePayResource.retries}"
                  timeout="${jsf.operatePayResource.timeout}">
    </jsf:consumer>

    <!-- 商家信息查询接口 125-->
    <jsf:consumer id="basicTraderNewAPI" interface="com.jd.ldop.basic.api.BasicTraderNewAPI"
                  alias="${jsf.basicTraderNewAPI.alias}" timeout="${jsf.basicTraderNewAPI.timeout}" filter="consumerChainFilter"
                  retries="${jsf.basicTraderNewAPI.retries}" serialization="hessian">
    </jsf:consumer>
</beans>

