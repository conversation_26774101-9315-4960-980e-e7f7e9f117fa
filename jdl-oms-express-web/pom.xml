<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>cn.jdl.oms</groupId>
    <artifactId>jdl-oms-express-web</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0-SNAPSHOT</version>
    <name>jdl-oms-express-web</name>
    <url>http://maven.apache.org</url>
    <modules>
        <module>jdl-oms-express-worker</module>
        <module>jdl-oms-express-main</module>
      <module>jdl-oms-express-monitor</module>
  </modules>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <!--servlet-->
        <servlet.version>2.5</servlet.version>
        <!--物流云服务-->
        <lcp.client.version>1.0-SNAPSHOT</lcp.client.version>
        <!--coo ucc versioin-->
        <coo.ucc.version>0.0.3-SNAPSHOT</coo.ucc.version>
        <!--安全加密-->
        <security.configsec.version>1.0.2.RELEASE</security.configsec.version>
        <!--Tomcat安全加固-->
        <security.tomcat.version>1.11.WEBAPP</security.tomcat.version>
        <!--HttpClinet版本-->
        <httpclient.version>4.5.12</httpclient.version>
        <!--jdbc 相关的引用-->
        <mybatis.spring.version>1.3.1</mybatis.spring.version>
        <mysql.connector.java.version>5.1.48</mysql.connector.java.version>
        <commons.dbcp.version>1.4</commons.dbcp.version>
        <!--dbcp2-->
        <commons.dbcp2.version>2.4.0</commons.dbcp2.version>
        <!--dbcp2-->
        <commons.pool2.version>2.6.2</commons.pool2.version>
        <!--jdbc start-->
        <mybatis.version>3.5.6</mybatis.version>
        <mybatis.spring.version>1.3.1</mybatis.spring.version>
        <mysql.connector.java.version>5.1.48</mysql.connector.java.version>
        <dbcp.version>1.4</dbcp.version>
        <!--jdbc end-->
        <google.gson.version>2.8.6</google.gson.version>

        <cn.jdl.oms.client.version>2.0.0-SNAPSHOT</cn.jdl.oms.client.version>
        <!-- ducc版本 -->
        <ducc.version>1.4.3</ducc.version>
        <!--ump-->
        <ump.version>20240630</ump.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.12</version>
                <scope>test</scope>
            </dependency>
            <!--项目内-->
            <!--领域服务-->
            <dependency>
                <groupId>cn.jdl.oms</groupId>
                <artifactId>jdl-oms-express-domain-service</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-test</artifactId>
                <version>5.2.9.RELEASE</version>
            </dependency>
            <!--servlet-->
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>servlet-api</artifactId>
                <version>${servlet.version}</version>
            </dependency>
            <!--物流云服务依赖-->
<!--            <dependency>-->
<!--                <artifactId>lcp-client</artifactId>-->
<!--                <groupId>com.jd.lcp</groupId>-->
<!--                <version>${lcp.client.version}</version>-->
<!--                <exclusions>-->
<!--                    <exclusion>-->
<!--                        <artifactId>aspectjweaver</artifactId>-->
<!--                        <groupId>org.aspectj</groupId>-->
<!--                    </exclusion>-->
<!--                </exclusions>-->
<!--            </dependency>-->
            <!--coo uuc-->
            <dependency>
                <groupId>com.jd.coo</groupId>
                <artifactId>ucc-client</artifactId>
                <version>${coo.ucc.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--应用配置文件加密-->
            <dependency>
                <groupId>com.jd.security.configsec</groupId>
                <artifactId>spring-configsec-sdk</artifactId>
                <version>${security.configsec.version}</version>
            </dependency>
            <!--Tomcat 安全加固-->
            <dependency>
                <groupId>com.jd.security</groupId>
                <artifactId>jd-security-tomcat</artifactId>
                <version>${security.tomcat.version}</version>
            </dependency>

            <!--数据库连接配置依赖 start-->
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>${mybatis.spring.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.connector.java.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-dbcp2</artifactId>
                <version>${commons.dbcp2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>${commons.pool2.version}</version>
            </dependency>
            <!--数据库连接配置依赖 end-->
            <!--数据库连接配置依赖 end-->
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${google.gson.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.jdl.oms</groupId>
                <artifactId>jdl-oms-client</artifactId>
                <version>${cn.jdl.oms.client.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>jdl-batrix-sdk</artifactId>
                        <groupId>cn.jdl.batrix</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>cn.jdl.batrix</groupId>
                        <artifactId>jdl-batrix-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.jd.ump</groupId>
                <artifactId>profiler</artifactId>
                <version>${ump.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-api</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.aspectj</groupId>
                        <artifactId>aspectjweaver</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.9</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <!-- compiler插件参数设置，指定编码 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.0</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <!-- resource插件设置，指定字符编码 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.0.2</version>
                <configuration>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>jd-central</id>
            <name>libs-releases</name>
            <url>http://artifactory.jd.com/libs-releases-local</url>
        </repository>
        <snapshotRepository>
            <id>jd-snapshots</id>
            <name>libs-snapshots</name>
            <url>http://artifactory.jd.com/libs-snapshots-local</url>
        </snapshotRepository>
    </distributionManagement>
</project>
